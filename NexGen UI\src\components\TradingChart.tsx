import React, { useEffect, useRef, useState } from 'react';
import { create<PERSON><PERSON>, ColorType, IChartApi, ISeriesApi, UTCTimestamp, LineStyle, CandlestickSeries, LineSeries, AreaSeries } from 'lightweight-charts';
import { BarChart3, LineChart as LineChartIcon } from 'lucide-react';

interface CandlestickData {
    time: UTCTimestamp;
    open: number;
    high: number;
    low: number;
    close: number;
}

const chartTypes = [
  { key: 'candlestick', label: 'Candles', icon: <BarChart3 size={16} /> },
  { key: 'line', label: 'Line', icon: <LineChartIcon size={16} /> },
  { key: 'area', label: 'Area', icon: <BarChart3 size={16} /> },
];

const mockCandles: CandlestickData[] = [];
let lastClose = 100;
for (let i = 0; i < 150; i++) {
    const time = (Math.floor(Date.now() / 1000) - (150 - i) * 3600) as UTCTimestamp;
    const open = lastClose + (Math.random() - 0.5) * 5;
    const close = open + (Math.random() - 0.5) * 10;
    const high = Math.max(open, close) + Math.random() * 5;
    const low = Math.min(open, close) - Math.random() * 5;
    lastClose = close;
    mockCandles.push({ time, open, high, low, close });
}

const TradingChart: React.FC = () => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const [chartType, setChartType] = useState<'candlestick' | 'line' | 'area'>('candlestick');

  useEffect(() => {
    if (!chartContainerRef.current) return;

    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      height: chartContainerRef.current.clientHeight,
      layout: {
        background: { type: ColorType.Solid, color: '#181c23' },
        textColor: '#c3e7fa',
      },
      grid: {
        vertLines: { color: '#23272f', style: LineStyle.Dotted },
        horzLines: { color: '#23272f', style: LineStyle.Dotted },
      },
      crosshair: { mode: 1 },
      timeScale: { borderColor: '#23272f', timeVisible: true, secondsVisible: false },
      rightPriceScale: { borderColor: '#23272f' },
    });
    chartRef.current = chart;

    let series: ISeriesApi<any>;

    if (chartType === 'candlestick') {
      series = chart.addSeries(CandlestickSeries, {
        upColor: '#00ffb3',
        downColor: '#ff4757',
        borderVisible: false,
        wickUpColor: '#00ffb3',
        wickDownColor: '#ff4757',
      });
      series.setData(mockCandles);
    } else if (chartType === 'line') {
      series = chart.addSeries(LineSeries, {
        color: '#00d4ff',
        lineWidth: 2,
      });
      series.setData(mockCandles.map(c => ({ time: c.time, value: c.close })));
    } else {
      series = chart.addSeries(AreaSeries, {
        topColor: 'rgba(0, 212, 255, 0.4)',
        bottomColor: 'rgba(0, 212, 255, 0.05)',
        lineColor: '#00d4ff',
        lineWidth: 2,
      });
      series.setData(mockCandles.map(c => ({ time: c.time, value: c.close })));
    }

    chart.timeScale().fitContent();

    const handleResize = () => {
      if (chartContainerRef.current && chartRef.current) {
        chartRef.current.resize(chartContainerRef.current.clientWidth, chartContainerRef.current.clientHeight);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      chart.remove();
    };
  }, [chartType]);

  return (
    <div className="terminal-card p-6 flex flex-col">
      <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-terminal-text font-mono">BTC/USD</h2>
          <div className="flex bg-terminal-surface rounded-lg p-1 border border-terminal-border">
            {chartTypes.map((type) => (
              <button
                key={type.key}
                className={`flex items-center space-x-2 px-3 py-1.5 rounded-md text-sm font-mono font-medium transition-all duration-200 ${chartType === type.key ? 'bg-neon-blue text-white shadow-glow-blue-sm' : 'text-terminal-muted hover:text-terminal-text'}`}
                onClick={() => setChartType(type.key as any)}
              >
                {type.icon}
                <span>{type.label}</span>
              </button>
            ))}
          </div>
      </div>
      <div ref={chartContainerRef} className="w-full h-[500px] rounded-xl border border-terminal-border bg-terminal-bg" />
    </div>
  );
};

export default TradingChart;