"""news_strategy.py
High-level, plug-and-play news-sentiment module for the NexGen Forex Trading Bot.
--------------------------------------------------------------------------
Workflow
1.  wait_for_mt5_trigger()  – optional helper to block until a MT5 anomaly/event arrives.
2.  fetch_news()            – pull recent headlines from Marketaux + Yahoo Finance.
3.  score_headlines()       – sentiment-score each article via TextBlob (primary) or VADER.
4.  make_decision()         – aggregate sentiment and output BUY / SELL / HOLD.
5.  run_sentiment_strategy(symbol) – convenience wrapper.

All calls are non-blocking, network operations have 5-second timeouts and are wrapped in
try/except so the trading loop never crashes.
"""
from __future__ import annotations
import requests, logging, time, random
from dataclasses import dataclass
from typing import List, Dict, Optional
from datetime import datetime, timedelta
from threading import Thread, Event

from app.config import config

# ---------------------------------------------------------------------------
# 1. OPTIONAL MT5 TRIGGER HELPER
# ---------------------------------------------------------------------------
try:
    import MetaTrader5 as mt5
except ImportError:
    mt5 = None  # The module is optional for unit-testing

_TRIGGER_STOP = Event()

def wait_for_mt5_trigger(symbol: str, check_interval: float = 5.0, 
                         vol_threshold: float = 2.0):
    """Block until a simple volume-spike trigger fires.
    volume > avg_volume * vol_threshold over the last 30 bars.
    """
    if not mt5 or not mt5.terminal_info():
        logging.warning("MT5 not initialized – skipping trigger wait")
        return

    def _loop():
        from collections import deque
        vols: deque[float] = deque(maxlen=30)
        while not _TRIGGER_STOP.is_set():
            rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_M1, 0, 1)
            if rates is None or len(rates) == 0:
                time.sleep(check_interval); continue
            vol = rates[0].volume
            vols.append(vol)
            if len(vols) == vols.maxlen:
                avg = sum(vols)/len(vols)
                if vol > avg * vol_threshold:
                    logging.info(f"MT5 volume trigger fired on {symbol} vol={vol:.0f} avg={avg:.0f}")
                    break
            time.sleep(check_interval)
        _TRIGGER_STOP.clear()

    _loop()

# ---------------------------------------------------------------------------
# 2. NEWS FETCHING LAYER
# ---------------------------------------------------------------------------
MTAUX_ENDPOINT = "https://api.marketaux.com/v1/news/all"
YF_SEARCH_URL = "https://query2.finance.yahoo.com/v1/finance/search"
TIMEOUT = 5

# Try yahooquery first
try:
    from yahooquery import Ticker as YQTicker
    _has_yq = True
except ImportError:
    _has_yq = False

@dataclass
class Article:
    title: str
    url: str
    source: str
    published: str
    summary: Optional[str]
    sentiment: Optional[float] = None


def _currency_to_keywords(symbol: str) -> List[str]:
    # EURUSD -> ["EUR", "USD"]  
    return [symbol[:3].upper(), symbol[3:6].upper()]


def fetch_marketaux(symbol: str, limit: int = 3) -> List[Article]:
    if not config.FINNHUB_API_KEY and not getattr(config, "MARKETAUX_API_KEY", None):
        return []
    params = {
        "api_token": getattr(config, "MARKETAUX_API_KEY", "demo"),
        "symbols": symbol,
        "filter_entities": "true",
        "language": "en",
        "limit": limit,
    }
    try:
        r = requests.get(MTAUX_ENDPOINT, params=params, timeout=TIMEOUT)
        r.raise_for_status()
        data = r.json()
        articles = []
        for a in data.get("data", [])[:limit]:
            articles.append(Article(
                title=a.get("title"),
                url=a.get("url"),
                source=a.get("source"),
                published=a.get("published_at"),
                summary=a.get("description")))
        return articles
    except Exception as e:
        logging.error(f"Marketaux fetch error: {e}")
        return []


def fetch_yahoo(symbol: str, limit: int = 3) -> List[Article]:
    # Prefer yahooquery if installed
    if _has_yq:
        try:
            yq_sym = f"{symbol.upper()}=X"
            tk = YQTicker(yq_sym)
            items = tk.news or []
            out: List[Article] = []
            for n in items[:limit]:
                out.append(Article(
                    title=n.get("title"),
                    url=n.get("link"),
                    source=n.get("publisher"),
                    published=datetime.fromtimestamp(n.get("providerPublishTime", 0)).isoformat(),
                    summary=n.get("summary") if "summary" in n else None))
            return out
        except Exception as e:
            logging.warning(f"yahooquery error, falling back: {e}")

    # Fallback to public Search endpoint
    kws = " ".join(_currency_to_keywords(symbol))
    params = {"q": kws, "newsCount": limit, "lang": "en-US"}
    try:
        r = requests.get(YF_SEARCH_URL, params=params, timeout=TIMEOUT)
        r.raise_for_status()
        items = r.json().get("news", [])[:limit]
        out: List[Article] = []
        for n in items:
            out.append(Article(
                title=n.get("title"),
                url=n.get("link"),
                source=n.get("publisher"),
                published=datetime.fromtimestamp(n.get("providerPublishTime", 0)).isoformat(),
                summary=None))
        return out
    except Exception as e:
        logging.error(f"Yahoo search fetch error: {e}")
        return []


def fetch_news(symbol: str) -> List[Article]:
    arts = fetch_marketaux(symbol, 3) + fetch_yahoo(symbol, 3)
    # remove duplicates by URL
    seen = set(); uniq = []
    for a in arts:
        if a.url not in seen:
            uniq.append(a); seen.add(a.url)
    # Log raw payloads
    for a in uniq:
        try:
            from app.database import db
            db.log_news_raw(a.source.lower(), symbol, a.__dict__)
        except Exception as log_err:
            logging.debug(f"News DB log err: {log_err}")
    return uniq[:6]

# ---------------------------------------------------------------------------
# 3. SENTIMENT ANALYSIS
# ---------------------------------------------------------------------------
try:
    from textblob import TextBlob
    _use_textblob = True
except ImportError:
    from nltk.sentiment.vader import SentimentIntensityAnalyzer
    _vader = SentimentIntensityAnalyzer(); _use_textblob = False


def _score(text: str) -> float:
    if _use_textblob:
        return TextBlob(text).sentiment.polarity  # -1 .. +1
    else:
        return _vader.polarity_scores(text)["compound"]


def score_headlines(articles: List[Article]) -> List[Article]:
    for a in articles:
        snippet = a.summary or a.title
        a.sentiment = _score(snippet)
    return articles

# ---------------------------------------------------------------------------
# 4. DECISION LAYER
# ---------------------------------------------------------------------------

def make_decision(articles: List[Article]) -> str:
    if not articles:
        return "HOLD"
    avg = sum(a.sentiment for a in articles if a.sentiment is not None) / len(articles)
    if avg > 0.2:
        return "BUY"
    if avg < -0.2:
        return "SELL"
    return "HOLD"

# ---------------------------------------------------------------------------
# 5. HIGH-LEVEL WRAPPER
# ---------------------------------------------------------------------------

def run_sentiment_strategy(symbol: str, await_mt5_trigger: bool = False) -> str:
    if await_mt5_trigger:
        wait_for_mt5_trigger(symbol)

    arts = score_headlines(fetch_news(symbol))
    decision = make_decision(arts)

    logging.info("Sentiment decision %s → %s", symbol, decision)
    for a in arts[:3]:
        logging.info("%.2f | %s | %s", a.sentiment, a.source, a.title)
    return decision

# ---------------------------------------------------------------------------
# __main__ test
# ---------------------------------------------------------------------------
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    print(run_sentiment_strategy("EURUSD")) 