import pandas as pd
import numpy as np
from pydantic import BaseModel, ValidationError
from lightgbm import LGBMClassifier
import joblib
import os
from .config import config
import MetaTrader5 as mt5

class TradeDecision(BaseModel):
    action: str  # BUY, SELL, HOLD
    confidence: float  # 0-1
    sl_pips: int
    tp_ratio: float
    reason: str

class MLBrain:
    """
    Handles training and inference for the ML-based trade decision engine.
    """
    def __init__(self, model_path="app/lgbm_model.pkl"):
        self.model_path = model_path
        self.model = None
        if os.path.exists(self.model_path):
            self.model = joblib.load(self.model_path)

    def train(self, X: pd.DataFrame, y: pd.Series):
        """
        Train a LightGBM classifier and save it.
        """
        self.model = LGBMClassifier(n_estimators=100, random_state=42)
        self.model.fit(X, y)
        joblib.dump(self.model, self.model_path)

    def predict(self, X: pd.DataFrame) -> tuple:
        """
        Predict class and probability for the latest row in X.
        Returns: (prediction, confidence)
        """
        if self.model is None:
            raise ValueError("Model not trained or loaded.")
        pred = self.model.predict(X)[-1]
        proba = self.model.predict_proba(X)[-1]
        confidence = np.max(proba)
        return pred, confidence

    @staticmethod
    def features_from_df(df: pd.DataFrame) -> pd.DataFrame:
        """
        Selects and returns the feature columns for ML.
        """
        features = [
            "rsi", "macd", "macd_signal", "macd_diff",
            "bb_upper", "bb_middle", "bb_lower", "bb_pct",
            "stoch_k", "stoch_d"
        ]
        return df[features]

class HybridBrain:
    """
    Combines an ML model with a GPT reasoning layer for decision making.
    """
    def __init__(self, ml_brain: MLBrain):
        self.ml_brain = ml_brain
        self.openai_client = None
        if config.OPENAI_API_KEY:
            from openai import OpenAI
            self.openai_client = OpenAI(api_key=config.OPENAI_API_KEY)

    def get_decision(self, symbol: str, df: pd.DataFrame) -> TradeDecision:
        """
        Generates a trade decision using the hybrid ML + LLM approach.
        """
        # 1. Get base ML prediction
        features = self.ml_brain.features_from_df(df)
        ml_pred, ml_conf = self.ml_brain.predict(features)

        action_map = {0: "HOLD", 1: "BUY", 2: "SELL"} # Example mapping
        prediction_str = action_map.get(ml_pred, "HOLD")

        # 2. If no OpenAI key or ML says HOLD, fallback to pure ML decision
        if not self.openai_client or prediction_str == "HOLD":
            print("Fallback to pure ML signal (Hold signal or no API key).")
            return self._create_ml_fallback_decision(prediction_str, ml_conf)

        # 3. Augment with GPT-4.5 reasoning
        try:
            latest_indicators = df.iloc[-1]
            account_info = mt5.account_info()
            
            prompt = self._build_gpt_prompt(
                symbol=symbol,
                rsi=latest_indicators['rsi'],
                macd_diff=latest_indicators['macd_diff'],
                bb_pct=latest_indicators['bb_pct'],
                prediction=prediction_str,
                confidence=ml_conf,
                balance=account_info.balance,
                risk_pct=config.RISK_PERCENT
            )

            response = self.openai_client.chat.completions.create(
                model="gpt-4-turbo", # Or "gpt-4.5" when available
                messages=[{"role": "user", "content": prompt}],
                response_format={"type": "json_object"},
                temperature=0.5,
            )
            
            response_json = response.choices[0].message.content
            # 4. Validate output with Pydantic
            decision = TradeDecision.parse_raw(response_json)
            print(f"GPT validation successful. Reason: {decision.reason}")
            return decision

        except Exception as e:
            print(f"GPT validation failed: {e}. Falling back to ML signal.")
            return self._create_ml_fallback_decision(prediction_str, ml_conf)

    def _build_gpt_prompt(self, **kwargs) -> str:
        """Constructs the prompt for the GPT model."""
        return f"""
        Analyze {kwargs['symbol']} trade:
        Indicators: RSI={kwargs['rsi']:.2f}, MACD_diff={kwargs['macd_diff']:.5f}, Bollinger_pct={kwargs['bb_pct']:.2f}
        ML prediction: {kwargs['prediction']} ({kwargs['confidence']:.2%})
        Account: ${kwargs['balance']:.2f} | Risk: {kwargs['risk_pct']}%
        
        Output JSON: 
        {{"action":"BUY/SELL/HOLD", "confidence":0-1, "sl_pips": 30-300, "tp_ratio":1.2-3.0, "reason":"strategic rationale"}}
        """

    def _create_ml_fallback_decision(self, prediction: str, confidence: float) -> TradeDecision:
        """Creates a TradeDecision object based only on the ML output."""
        if prediction == "HOLD":
            return TradeDecision(action="HOLD", confidence=confidence, sl_pips=0, tp_ratio=0, reason="ML signal is HOLD.")
        
        return TradeDecision(
            action=prediction,
            confidence=confidence,
            sl_pips=50,  # Default fallback SL
            tp_ratio=1.5, # Default fallback TP Ratio
            reason=f"Fallback decision based on ML signal: {prediction}"
        )

# Example usage:
# from app.data_feeder import DataFeeder
# feeder = DataFeeder()
# df = feeder.get_features('EURUSD')
# X = MLBrain.features_from_df(df)
# y = ... # your target labels
# brain = MLBrain()
# brain.train(X, y)
# pred, conf = brain.predict(X) 