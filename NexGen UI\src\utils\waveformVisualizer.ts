// Audio waveform visualizer utility
// Provides a real-time visualization of audio input on a canvas element

export interface WaveformVisualizerOptions {
  lineColor?: string;
  lineWidth?: number;
  backgroundColor?: string;
  smoothing?: number;
  fftSize?: number;
}

export class WaveformVisualizer {
  private canvas: HTMLCanvasElement;
  private canvasContext: CanvasRenderingContext2D;
  private audioContext: AudioContext | null = null;
  private analyser: AnalyserNode | null = null;
  private dataArray: Uint8Array | null = null;
  private sourceNode: MediaStreamAudioSourceNode | null = null;
  private animationId: number | null = null;
  private options: Required<WaveformVisualizerOptions>;
  private isActive: boolean = false;
  
  constructor(canvas: HTMLCanvasElement, options: WaveformVisualizerOptions = {}) {
    this.canvas = canvas;
    const ctx = this.canvas.getContext('2d');
    
    if (!ctx) {
      throw new Error('Could not get canvas context');
    }
    
    this.canvasContext = ctx;
    
    // Set default options
    this.options = {
      lineColor: options.lineColor || '#00CCFF',
      lineWidth: options.lineWidth || 2,
      backgroundColor: options.backgroundColor || 'rgba(0, 0, 0, 0.1)',
      smoothing: options.smoothing || 0.8,
      fftSize: options.fftSize || 2048
    };
    
    // Initialize canvas dimensions to match its display size
    this.resizeCanvas();
    
    // Add window resize listener
    window.addEventListener('resize', this.resizeCanvas.bind(this));
  }
  
  private resizeCanvas(): void {
    const { width, height } = this.canvas.getBoundingClientRect();
    
    // Set the canvas internal dimensions to match its CSS dimensions
    if (this.canvas.width !== width || this.canvas.height !== height) {
      this.canvas.width = width;
      this.canvas.height = height;
    }
  }
  
  public connectToAudioStream(stream: MediaStream): void {
    // Create audio context and analyzer
    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
    this.analyser = this.audioContext.createAnalyser();
    
    // Configure analyzer
    this.analyser.fftSize = this.options.fftSize;
    this.analyser.smoothingTimeConstant = this.options.smoothing;
    
    // Create data array for analyzer
    const bufferLength = this.analyser.frequencyBinCount;
    this.dataArray = new Uint8Array(bufferLength);
    
    // Connect the microphone to the analyzer
    this.sourceNode = this.audioContext.createMediaStreamSource(stream);
    this.sourceNode.connect(this.analyser);
    
    // Start visualization
    this.start();
  }
  
  public start(): void {
    if (this.isActive) return;
    
    this.isActive = true;
    this.draw();
  }
  
  public stop(): void {
    if (!this.isActive) return;
    
    this.isActive = false;
    
    // Cancel animation frame
    if (this.animationId !== null) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
    
    // Clear canvas
    this.canvasContext.clearRect(0, 0, this.canvas.width, this.canvas.height);
  }
  
  public disconnect(): void {
    this.stop();
    
    // Disconnect and clean up audio nodes
    if (this.sourceNode) {
      this.sourceNode.disconnect();
      this.sourceNode = null;
    }
    
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close().catch(err => console.error('Error closing audio context', err));
      this.audioContext = null;
    }
    
    this.analyser = null;
    this.dataArray = null;
  }
  
  private draw(): void {
    if (!this.isActive || !this.analyser || !this.dataArray) return;
    
    // Schedule next frame
    this.animationId = requestAnimationFrame(this.draw.bind(this));
    
    // Get audio data
    this.analyser.getByteTimeDomainData(this.dataArray);
    
    // Clear canvas with background
    this.canvasContext.fillStyle = this.options.backgroundColor;
    this.canvasContext.fillRect(0, 0, this.canvas.width, this.canvas.height);
    
    // Draw waveform
    this.canvasContext.lineWidth = this.options.lineWidth;
    this.canvasContext.strokeStyle = this.options.lineColor;
    this.canvasContext.beginPath();
    
    const sliceWidth = this.canvas.width / this.dataArray.length;
    let x = 0;
    
    for (let i = 0; i < this.dataArray.length; i++) {
      // Normalize dataArray values (0-255) to canvas height
      const v = this.dataArray[i] / 128.0;
      const y = v * this.canvas.height / 2;
      
      if (i === 0) {
        this.canvasContext.moveTo(x, y);
      } else {
        this.canvasContext.lineTo(x, y);
      }
      
      x += sliceWidth;
    }
    
    // Draw line to the end of the canvas
    this.canvasContext.lineTo(this.canvas.width, this.canvas.height / 2);
    this.canvasContext.stroke();
  }
  
  // Update visualization options
  public updateOptions(newOptions: Partial<WaveformVisualizerOptions>): void {
    this.options = {
      ...this.options,
      ...newOptions
    };
    
    // Update analyzer settings if it exists
    if (this.analyser && newOptions.fftSize) {
      this.analyser.fftSize = newOptions.fftSize;
      // Recreate data array with new size if fftSize changed
      this.dataArray = new Uint8Array(this.analyser.frequencyBinCount);
    }
    
    if (this.analyser && newOptions.smoothing !== undefined) {
      this.analyser.smoothingTimeConstant = newOptions.smoothing;
    }
  }
  
  // Get current audio level (0-1)
  public getAudioLevel(): number {
    if (!this.analyser || !this.dataArray) return 0;
    
    this.analyser.getByteFrequencyData(this.dataArray);
    
    // Calculate average level
    let sum = 0;
    for (let i = 0; i < this.dataArray.length; i++) {
      sum += this.dataArray[i];
    }
    
    return sum / this.dataArray.length / 255; // Normalize to 0-1
  }
}

// For browsers that don't have these types defined
declare global {
  interface Window {
    webkitAudioContext: typeof AudioContext;
  }
} 