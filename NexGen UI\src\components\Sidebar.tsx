import React, { useState } from 'react';
import { Settings, Brain, Database, TrendingUp, Target, SlidersHorizontal, Newspaper, LineChart, Shield, Bot, Zap, Mic, Wallet, LayoutGrid } from 'lucide-react';
import { Orb } from 'react-ai-orb';

interface SidebarProps {
  activePanel: string;
  setActivePanel: (panel: string) => void;
}

type VoiceState = 'idle' | 'listening' | 'speaking' | 'thinking';

// Custom ChatGPT/Meta-style orb palette
const chatgptVoicePalette = {
  mainBgStart: "#1a2a3a",
  mainBgEnd: "#2e8b57",
  shadowColor1: "#00ffb3",
  shadowColor2: "#00eaff",
  shadowColor3: "#00cfff",
  shadowColor4: "#00ffb3",
  shapeAStart: "#00eaff",
  shapeAEnd: "#00ffb3",
  shapeBStart: "#2e8b57",
  shapeBMiddle: "#00eaff",
  shapeBEnd: "#1a2a3a",
  shapeCStart: "#00ffb3",
  shapeCMiddle: "#00eaff",
  shapeCEnd: "#2e8b57",
  shapeDStart: "#00eaff",
  shapeDMiddle: "#00ffb3",
  shapeDEnd: "#1a2a3a",
};

const orbStatePresets = {
  idle: {
    ...chatgptVoicePalette,
    animationSpeedBase: 1,
    mainOrbHueAnimation: false,
  },
  listening: {
    ...chatgptVoicePalette,
    animationSpeedBase: 2,
    mainOrbHueAnimation: true,
  },
  thinking: {
    ...chatgptVoicePalette,
    animationSpeedBase: 1.5,
    mainOrbHueAnimation: true,
    hueRotation: 180,
  },
  speaking: {
    ...chatgptVoicePalette,
    animationSpeedBase: 2.5,
    mainOrbHueAnimation: true,
    hueRotation: 240,
  },
};

const Sidebar: React.FC<SidebarProps> = ({ activePanel, setActivePanel }) => {
  const [multiAgent, setMultiAgent] = useState(false);
  // Voice state: idle -> listening -> speaking -> idle (cycle for demo)
  const [voiceState, setVoiceState] = useState<VoiceState>('idle');

  const navItems = [
    { name: 'Chart', icon: LineChart },
    { name: 'Account', icon: Wallet },
    { name: 'Bot Control', icon: LayoutGrid },
    { name: 'Logs', icon: Database },
    { name: 'Explainability', icon: Brain },
    { name: 'News', icon: Newspaper }, 
    { name: 'Backtest', icon: TrendingUp },
    { name: 'Forward Test', icon: Target },
    { name: 'AI Assistant', icon: Bot },
    { name: 'Anomaly', icon: Shield },
    { name: 'Config', icon: Settings },
    { name: 'System', icon: SlidersHorizontal },
  ];

  // Choose orb preset based on voice state
  const orbPreset = orbStatePresets[voiceState] || orbStatePresets.idle;

  // Cycle through voice states for demo
  const handleVoiceToggle = () => {
    setVoiceState((prev) =>
      prev === 'idle' ? 'listening' : prev === 'listening' ? 'speaking' : 'idle'
    );
  };

  return (
    <aside className="w-64 bg-terminal-surface flex-shrink-0 flex flex-col p-6 space-y-8 border-r border-terminal-border">
      {/* Logo */}
      <div className="flex items-center space-x-3 px-2">
        <Bot size={32} className="text-neon-blue" />
        <div>
          <h1 className="text-xl font-bold text-terminal-text">NexGen</h1>
          <p className="text-xs text-terminal-muted font-mono">Trading Bot</p>
        </div>
      </div>
      
      {/* Orb and Toggles - single row, subtle, original spacing */}
      <div className="flex items-center justify-center space-x-3 px-4">
        <div className="orb-glow">
          <Orb {...orbPreset} size={1.2} />
        </div>
        <button
          className={`p-2 rounded-full border border-terminal-border transition-all duration-200 shadow ${multiAgent ? 'bg-yellow-400/20 text-yellow-400 shadow-glow-yellow' : 'bg-terminal-card/50 text-terminal-muted hover:bg-yellow-400/10'}`}
          onClick={() => setMultiAgent((v) => !v)}
          title="Toggle Multi-Agent Mode"
          aria-label="Toggle Multi-Agent Mode"
        >
          <Zap size={18} />
        </button>
        <button
          className={`p-2 rounded-full border border-terminal-border transition-all duration-200 shadow ${voiceState !== 'idle' ? 'bg-neon-blue/20 text-neon-blue shadow-glow-blue' : 'bg-terminal-card/50 text-terminal-muted hover:bg-neon-blue/10'}`}
          onClick={handleVoiceToggle}
          title={`Voice Mode: ${voiceState.charAt(0).toUpperCase() + voiceState.slice(1)}`}
          aria-label="Toggle Voice Mode"
        >
          <Mic size={18} />
        </button>
      </div>

      {/* Navigation */}
      <nav className="flex-grow space-y-2">
        <p className="text-sm text-terminal-muted px-3 pb-2 font-mono">PANELS</p>
        {navItems.map((item) => (
          <button
            key={item.name}
            onClick={() => setActivePanel(item.name)}
            className={`w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left transition-all duration-200 ${
              activePanel === item.name
                ? 'bg-neon-blue/20 text-neon-blue shadow-glow-blue-sm'
                : 'text-terminal-text hover:bg-terminal-card/50'
            }`}
          >
            <item.icon size={20} />
            <span className="font-medium">{item.name}</span>
          </button>
        ))}
      </nav>

      {/* Footer Status */}
      <div className="flex-shrink-0 space-y-4">
        <div className="flex items-center space-x-3 px-3 text-sm">
          <div className="relative">
            <div className="w-2.5 h-2.5 bg-neon-green rounded-full animate-pulse"></div>
            <div className="absolute inset-0 w-2.5 h-2.5 bg-neon-green rounded-full animate-ping opacity-30"></div>
          </div>
          <span className="text-terminal-muted font-mono">Status:</span>
          <span className="text-neon-green font-mono font-bold">OPERATIONAL</span>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;