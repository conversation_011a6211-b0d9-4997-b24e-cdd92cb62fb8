import numpy as np
from datetime import datetime, timedelta
from skopt import gp_minimize
from skopt.space import Real
from app.database import db
from app.config import config

class AutoTuner:
    """
    Optimizes risk parameters using Bayesian optimization and recent performance.
    """
    def __init__(self, lookback_days=14):
        self.lookback_days = lookback_days
        self.last_tuned = None
        self.last_best = None

    def fetch_performance(self):
        since = (datetime.now() - timedelta(days=self.lookback_days)).strftime('%Y-%m-%d')
        trades = db.conn.execute(
            "SELECT * FROM trades WHERE timestamp >= ? AND status = 'CLOSED'", (since,)
        ).fetchall()
        return trades

    def simulate(self, risk_percent, tp_ratio, trades):
        # Simulate PnL using these parameters (very basic example)
        pnl = 0
        for t in trades:
            # Assume profit/loss scales with risk and tp_ratio
            if t['profit_loss'] is not None:
                scale = (risk_percent / config.RISK_PERCENT) * (tp_ratio / config.TP_RATIO if hasattr(config, 'TP_RATIO') else 1.0)
                pnl += t['profit_loss'] * scale
        return -pnl  # Negative for minimization

    def optimize(self):
        trades = self.fetch_performance()
        if not trades or len(trades) < 10:
            print("Not enough trades for tuning.")
            return None
        space = [
            Real(1.0, 5.0, name='risk_percent'),
            Real(1.2, 3.0, name='tp_ratio')
        ]
        def objective(params):
            risk_percent, tp_ratio = params
            return self.simulate(risk_percent, tp_ratio, trades)
        res = gp_minimize(objective, space, n_calls=20, random_state=42)
        best_risk, best_tp = res.x
        self.last_best = (best_risk, best_tp)
        return best_risk, best_tp

    def apply_tuning(self):
        result = self.optimize()
        if not result:
            return False
        best_risk, best_tp = result
        # Cap risk increase to +0.5% per week
        new_risk = min(best_risk, config.RISK_PERCENT + 0.5)
        # Optionally, update config dynamically (if supported)
        config.RISK_PERCENT = new_risk
        if hasattr(config, 'TP_RATIO'):
            config.TP_RATIO = best_tp
        print(f"Auto-tuner set RISK_PERCENT={new_risk:.2f}, TP_RATIO={best_tp:.2f}")
        self.last_tuned = datetime.now()
        return True 