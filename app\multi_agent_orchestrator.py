from app.postgresql_memory import PostgresMemory
from app.config import config
import os

class MultiAgentOrchestrator:
    def __init__(self):
        self.memory = PostgresMemory()
        self.openai_api_key = config.OPENAI_API_KEY
        self.claude_api_key = os.getenv('CLAUDE_API_KEY', '')
        if self.openai_api_key:
            import openai
            self.openai_client = openai.OpenAI(api_key=self.openai_api_key)
        else:
            self.openai_client = None
        if self.claude_api_key:
            import anthropic
            self.claude_client = anthropic.Anthropic(api_key=self.claude_api_key)
        else:
            self.claude_client = None

    def process_query(self, prompt: str) -> str:
        gpt_response = None
        claude_response = None
        gpt_conf = 0.0
        claude_conf = 0.0
        # Query GPT
        if self.openai_client:
            try:
                gpt_result = self.openai_client.chat.completions.create(
                    model="gpt-4-turbo",
                    messages=[{"role": "user", "content": prompt}],
                    response_format={"type": "json_object"},
                    temperature=0.5,
                )
                gpt_response = gpt_result.choices[0].message.content
                if isinstance(gpt_response, dict) and 'confidence' in gpt_response:
                    gpt_conf = float(gpt_response['confidence'])
            except Exception as e:
                gpt_response = f"[GPT error: {e}]"
        # Query Claude
        if self.claude_client:
            try:
                claude_result = self.claude_client.messages.create(
                    model="claude-3-opus-20240229",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=1024,
                )
                claude_response = claude_result.content[0].text
                # Extract confidence if present (customize as needed)
            except Exception as e:
                claude_response = f"[Claude error: {e}]"
        # Store both responses
        self.memory.store_memory('MULTI_AGENT', str({
            'gpt': gpt_response,
            'claude': claude_response
        }), {'prompt': prompt})
        # Select most confident (if available)
        if gpt_conf >= claude_conf:
            return f"GPT: {gpt_response}"
        elif claude_response:
            return f"Claude: {claude_response}"
        else:
            return "No valid response from agents."

# Usage example:
# orchestrator = MultiAgentOrchestrator()
# answer = orchestrator.process_query('What is the best trade this week?') 