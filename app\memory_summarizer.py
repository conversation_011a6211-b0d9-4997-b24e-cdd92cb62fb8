from app.postgresql_memory import PostgresMemory
from app.config import config
import openai
from datetime import datetime, timedelta

class MemorySummarizer:
    def __init__(self):
        self.memory = PostgresMemory()
        self.openai_api_key = config.OPENAI_API_KEY
        if self.openai_api_key:
            self.openai_client = openai.OpenAI(api_key=self.openai_api_key)
        else:
            self.openai_client = None

    def summarize_week(self):
        # Fetch last 7 days of memory logs
        since = datetime.now() - timedelta(days=7)
        logs = self.memory.retrieve_memory(limit=500)
        week_logs = [log for log in logs if log['timestamp'] >= since]
        content = '\n'.join([log['content'] for log in week_logs])
        prompt = f"""
        Summarize the following trading bot memory logs for the past week. Focus on key decisions, mistakes, improvements, and notable events. Be concise and actionable.
        Logs:
        {content}
        """
        if not self.openai_client:
            summary = "No OpenAI key provided. Skipping summarization."
        else:
            try:
                response = self.openai_client.chat.completions.create(
                    model="gpt-4-turbo",
                    messages=[{"role": "user", "content": prompt}],
                    response_format={"type": "text"},
                    temperature=0.5,
                )
                summary = response.choices[0].message.content.strip()
            except Exception as e:
                summary = f"[GPT error: {e}]"
        # Store summary in memory
        self.memory.store_memory(
            'SUMMARY',
            summary,
            {'timestamp': datetime.now().isoformat()}
        )
        return summary

# Usage example:
# summarizer = MemorySummarizer()
# summarizer.summarize_week() 