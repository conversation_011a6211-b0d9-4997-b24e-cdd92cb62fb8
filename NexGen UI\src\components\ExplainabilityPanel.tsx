import React, { useState, useEffect } from 'react';
import { Brain, MessageCircle, Clock, Zap, Target, TrendingUp, BarChart3, Loader2, ServerCrash } from 'lucide-react';
import { api } from '../services/api';

// This interface defines the structure of the JSON object *inside* the explanation string
interface ParsedExplanation {
  tradeId: string;
  explanation: string;
  confidence: number;
  factors: Array<{
    name: string;
    impact: number;
    description: string;
    type: 'technical' | 'fundamental' | 'sentiment';
  }>;
  timestamp: string;
  recommendation: string;
}

interface ExplainabilityPanelProps {
  tradeId: number | null;
}

const ExplainabilityPanel: React.FC<ExplainabilityPanelProps> = ({ tradeId }) => {
  const [explanation, setExplanation] = useState<ParsedExplanation | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!tradeId) {
      setExplanation(null);
      return;
    }

    const fetchExplanation = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const rawExplanation = await api.getTradeExplanation(tradeId);
        // The explanation from the DB is a JSON string, so we need to parse it.
        const parsedData: ParsedExplanation = JSON.parse(rawExplanation.explanation);
        setExplanation(parsedData);
      } catch (err) {
        setError(`Failed to fetch or parse explanation for trade #${tradeId}.`);
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchExplanation();
  }, [tradeId]);

  if (!tradeId) {
    return (
      <div className="terminal-card p-6 flex flex-col items-center justify-center h-full text-center">
        <Brain className="text-neon-purple/50" size={48} />
        <p className="mt-4 text-lg font-mono text-terminal-text">Select a Trade</p>
        <p className="text-sm font-mono text-terminal-muted">Choose a trade from the logs to see the AI's analysis.</p>
      </div>
    );
  }

  if (isLoading) {
    return (
        <div className="terminal-card p-6 flex items-center justify-center h-full">
            <Loader2 className="animate-spin text-neon-purple" size={48} />
            <p className="ml-4 text-lg font-mono text-terminal-text">Analyzing Trade #{tradeId}...</p>
        </div>
    );
  }

  if (error || !explanation) {
    return (
      <div className="terminal-card p-6 flex flex-col items-center justify-center h-full bg-neon-red/10 border border-neon-red">
        <ServerCrash className="text-neon-red" size={48} />
        <p className="mt-4 text-lg font-mono text-neon-red">Analysis Failed</p>
        <p className="text-sm font-mono text-terminal-muted">{error || 'No explanation data available.'}</p>
      </div>
    );
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-neon-green';
    if (confidence >= 60) return 'text-neon-yellow';
    return 'text-neon-red';
  };

  const getImpactColor = (impact: number) => {
    if (impact >= 30) return 'bg-neon-red';
    if (impact >= 20) return 'bg-neon-yellow';
    return 'bg-neon-blue';
  };

  const getFactorTypeColor = (type: string) => {
    switch (type) {
      case 'technical': return 'text-neon-blue';
      case 'fundamental': return 'text-neon-green';
      case 'sentiment': return 'text-neon-purple';
      default: return 'text-terminal-muted';
    }
  };

  const getFactorTypeIcon = (type: string) => {
    switch (type) {
      case 'technical': return <BarChart3 size={14} />;
      case 'fundamental': return <TrendingUp size={14} />;
      case 'sentiment': return <Brain size={14} />;
      default: return <Target size={14} />;
    }
  };

  return (
    <div className="terminal-card p-6">
      <div className="flex items-center space-x-3 mb-6">
        <div className="p-2 bg-neon-purple/20 rounded-lg">
          <Brain className="text-neon-purple" size={24} />
        </div>
        <div>
          <h2 className="text-xl font-bold text-terminal-text">AI Explainability Analysis</h2>
          <p className="text-sm text-terminal-muted font-mono">Trade ID: {explanation.tradeId}</p>
        </div>
      </div>

      {/* Confidence & Recommendation */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="terminal-surface p-4 rounded-lg border-l-4 border-l-neon-purple">
          <div className="flex items-center justify-between mb-2">
            <span className="text-terminal-muted font-mono font-medium">Confidence Score</span>
            <span className={`text-2xl font-bold font-mono ${getConfidenceColor(explanation.confidence)}`}>
              {explanation.confidence}%
            </span>
          </div>
          <div className="w-full bg-terminal-bg rounded-full h-3 overflow-hidden">
            <div
              className="h-full rounded-full transition-all duration-1000 relative"
              style={{ 
                width: `${explanation.confidence}%`,
                background: explanation.confidence >= 80 
                  ? 'linear-gradient(90deg, #00ff88, #00d4ff)' 
                  : explanation.confidence >= 60
                    ? 'linear-gradient(90deg, #ffd700, #ff6b35)'
                    : 'linear-gradient(90deg, #ff4757, #ff6b35)'
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
            </div>
          </div>
        </div>

        <div className="terminal-surface p-4 rounded-lg border-l-4 border-l-neon-green">
          <div className="text-sm text-terminal-muted font-mono mb-1">AI Recommendation</div>
          <div className="text-sm font-mono text-terminal-text leading-relaxed">
            {explanation.recommendation}
          </div>
        </div>
      </div>

      {/* Explanation Text */}
      <div className="terminal-surface p-5 rounded-lg mb-6 bg-gradient-to-r from-terminal-surface to-terminal-card/50">
        <div className="flex items-center space-x-2 mb-4">
          <MessageCircle className="text-neon-blue" size={20} />
          <h3 className="text-lg font-semibold text-terminal-text font-mono">Decision Rationale</h3>
        </div>
        <p className="text-terminal-text leading-relaxed font-mono text-sm mb-4">
          {explanation.explanation}
        </p>
        <div className="flex items-center space-x-2 text-sm text-terminal-muted">
          <Clock size={14} />
          <span className="font-mono">Generated at {new Date(explanation.timestamp).toLocaleString()}</span>
        </div>
      </div>

      {/* Contributing Factors */}
      <div className="terminal-surface p-5 rounded-lg">
        <div className="flex items-center space-x-2 mb-4">
          <Zap className="text-neon-yellow" size={20} />
          <h3 className="text-lg font-semibold text-terminal-text font-mono">Contributing Factors</h3>
        </div>
        
        <div className="space-y-3">
          {explanation.factors.map((factor: any, index: number) => (
            <div key={index} className="terminal-bg rounded-lg p-4 hover:bg-terminal-card/30 transition-all duration-300">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${
                    factor.type === 'technical' ? 'bg-neon-blue/20' :
                    factor.type === 'fundamental' ? 'bg-neon-green/20' :
                    'bg-neon-purple/20'
                  }`}>
                    <div className={getFactorTypeColor(factor.type)}>
                      {getFactorTypeIcon(factor.type)}
                    </div>
                  </div>
                  <div>
                    <span className="text-terminal-text font-medium font-mono">{factor.name}</span>
                    <div className="text-xs text-terminal-muted font-mono capitalize">
                      {factor.type} Analysis
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-sm font-mono text-terminal-text font-bold">{factor.impact}%</span>
                  <div className="w-20 bg-terminal-surface rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-1000 ${getImpactColor(factor.impact)}`}
                      style={{ width: `${(factor.impact / 50) * 100}%` }}
                    />
                  </div>
                </div>
              </div>
              <p className="text-sm text-terminal-muted font-mono leading-relaxed">
                {factor.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ExplainabilityPanel;