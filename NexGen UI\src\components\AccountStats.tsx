import React, { useState, useEffect } from 'react';
import { DollarSign, TrendingUp, TrendingDown, PieChart, Target, Shield, Zap, Award } from 'lucide-react';

interface AccountData {
  balance: number;
  equity: number;
  margin: number;
  freeMargin: number;
  totalPnL: number;
  dailyPnL: number;
  openTrades: number;
  winRate: number;
  profitFactor: number;
  maxDrawdown: number;
}

const AccountStats: React.FC = () => {
  const [accountData, setAccountData] = useState<AccountData>({
    balance: 50000,
    equity: 52341.75,
    margin: 2500,
    freeMargin: 49841.75,
    totalPnL: 2341.75,
    dailyPnL: 156.23,
    openTrades: 3,
    winRate: 68.5,
    profitFactor: 1.45,
    maxDrawdown: 8.2
  });

  useEffect(() => {
    const interval = setInterval(() => {
      setAccountData(prev => ({
        ...prev,
        equity: prev.balance + prev.totalPnL + (Math.random() - 0.5) * 100,
        dailyPnL: prev.dailyPnL + (Math.random() - 0.5) * 10,
        totalPnL: prev.totalPnL + (Math.random() - 0.5) * 20
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const stats = [
    {
      label: 'Account Balance',
      value: `$${accountData.balance.toLocaleString()}`,
      icon: DollarSign,
      color: 'text-neon-blue',
      bgColor: 'bg-neon-blue/10'
    },
    {
      label: 'Current Equity',
      value: `$${accountData.equity.toLocaleString()}`,
      icon: TrendingUp,
      color: 'text-neon-green',
      bgColor: 'bg-neon-green/10'
    },
    {
      label: 'Used Margin',
      value: `$${accountData.margin.toLocaleString()}`,
      icon: PieChart,
      color: 'text-neon-yellow',
      bgColor: 'bg-neon-yellow/10'
    },
    {
      label: 'Free Margin',
      value: `$${accountData.freeMargin.toLocaleString()}`,
      icon: Shield,
      color: 'text-neon-purple',
      bgColor: 'bg-neon-purple/10'
    }
  ];

  const performanceStats = [
    {
      label: 'Total P&L',
      value: `${accountData.totalPnL >= 0 ? '+' : ''}$${accountData.totalPnL.toFixed(2)}`,
      change: accountData.totalPnL >= 0 ? '+4.7%' : '-2.1%',
      positive: accountData.totalPnL >= 0,
      icon: Target
    },
    {
      label: 'Daily P&L',
      value: `${accountData.dailyPnL >= 0 ? '+' : ''}$${accountData.dailyPnL.toFixed(2)}`,
      change: accountData.dailyPnL >= 0 ? '+0.31%' : '-0.15%',
      positive: accountData.dailyPnL >= 0,
      icon: Zap
    },
    {
      label: 'Open Trades',
      value: accountData.openTrades.toString(),
      change: 'Active',
      positive: true,
      icon: TrendingUp
    },
    {
      label: 'Win Rate',
      value: `${accountData.winRate}%`,
      change: '+2.1%',
      positive: true,
      icon: Award
    }
  ];

  const marginUsagePercent = (accountData.margin / accountData.balance) * 100;

  return (
    <div className="terminal-card p-6">
      <div className="flex items-center space-x-3 mb-6">
        <div className="p-2 bg-neon-green/20 rounded-lg">
          <Target className="text-neon-green" size={24} />
        </div>
        <div>
          <h2 className="text-xl font-bold text-terminal-text">Account Overview</h2>
          <p className="text-sm text-terminal-muted font-mono">Live Trading Account</p>
        </div>
      </div>

      {/* Main Stats Grid */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        {stats.map((stat, index) => (
          <div key={index} className={`terminal-surface p-4 rounded-lg border-l-4 border-l-transparent hover:border-l-current transition-all duration-300 ${stat.bgColor}`}>
            <div className="flex items-center justify-between mb-3">
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={stat.color} size={20} />
              </div>
              <span className="text-xs text-terminal-muted font-mono">24h</span>
            </div>
            <div className="text-sm text-terminal-muted mb-1 font-mono">{stat.label}</div>
            <div className={`text-xl font-bold font-mono ${stat.color}`}>{stat.value}</div>
          </div>
        ))}
      </div>

      {/* Performance Metrics */}
      <div className="space-y-3 mb-6">
        {performanceStats.map((stat, index) => (
          <div key={index} className="flex items-center justify-between terminal-surface p-4 rounded-lg hover:bg-terminal-card/50 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${stat.positive ? 'bg-neon-green/20' : 'bg-neon-red/20'}`}>
                <stat.icon className={stat.positive ? 'text-neon-green' : 'text-neon-red'} size={16} />
              </div>
              <div>
                <div className="text-sm text-terminal-muted font-mono">{stat.label}</div>
                <div className="text-lg font-bold text-terminal-text font-mono">{stat.value}</div>
              </div>
            </div>
            <div className={`flex items-center space-x-1 px-3 py-1.5 rounded-full text-xs font-mono font-medium ${
              stat.positive 
                ? 'bg-neon-green/20 text-neon-green border border-neon-green/30' 
                : 'bg-neon-red/20 text-neon-red border border-neon-red/30'
            }`}>
              {stat.positive ? <TrendingUp size={12} /> : <TrendingDown size={12} />}
              <span>{stat.change}</span>
            </div>
          </div>
        ))}
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="terminal-surface p-4 rounded-lg bg-neon-green/5 border border-neon-green/20">
          <div className="text-sm text-terminal-muted mb-1 font-mono">Profit Factor</div>
          <div className="text-2xl font-bold text-neon-green font-mono">{accountData.profitFactor}</div>
          <div className="text-xs text-neon-green/80 font-mono">Excellent</div>
        </div>
        <div className="terminal-surface p-4 rounded-lg bg-neon-yellow/5 border border-neon-yellow/20">
          <div className="text-sm text-terminal-muted mb-1 font-mono">Max Drawdown</div>
          <div className="text-2xl font-bold text-neon-yellow font-mono">{accountData.maxDrawdown}%</div>
          <div className="text-xs text-neon-yellow/80 font-mono">Low Risk</div>
        </div>
      </div>

      {/* Margin Usage Bar */}
      <div className="terminal-surface p-4 rounded-lg">
        <div className="flex justify-between items-center mb-3">
          <span className="text-sm text-terminal-muted font-mono">Margin Usage</span>
          <span className="text-sm font-mono font-bold text-terminal-text">
            {marginUsagePercent.toFixed(1)}%
          </span>
        </div>
        <div className="relative w-full bg-terminal-bg rounded-full h-3 overflow-hidden">
          <div
            className="h-full rounded-full transition-all duration-1000 relative overflow-hidden"
            style={{ 
              width: `${marginUsagePercent}%`,
              background: marginUsagePercent > 80 
                ? 'linear-gradient(90deg, #ff4757, #ff6b35)' 
                : marginUsagePercent > 50 
                  ? 'linear-gradient(90deg, #ffd700, #ff6b35)'
                  : 'linear-gradient(90deg, #00ff88, #00d4ff)'
            }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
          </div>
        </div>
        <div className="flex justify-between text-xs text-terminal-muted font-mono mt-2">
          <span>Safe</span>
          <span>Moderate</span>
          <span>High Risk</span>
        </div>
      </div>
    </div>
  );
};

export default AccountStats;