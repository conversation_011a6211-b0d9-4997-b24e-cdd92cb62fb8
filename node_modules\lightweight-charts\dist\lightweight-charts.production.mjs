/*!
 * @license
 * TradingView Lightweight Charts™ v5.0.7
 * Copyright (c) 2025 TradingView, Inc.
 * Licensed under Apache License 2.0 https://www.apache.org/licenses/LICENSE-2.0
 */
import{size as t,bindCanvasElementBitmapSizeTo as i,equalSizes as s,tryCreateCanvasRenderingTarget2D as n}from"fancy-canvas";const e={title:"",visible:!0,lastValueVisible:!0,priceLineVisible:!0,priceLineSource:0,priceLineWidth:1,priceLineColor:"",priceLineStyle:2,baseLineVisible:!0,baseLineWidth:1,baseLineColor:"#B2B5BE",baseLineStyle:0,priceFormat:{type:"price",precision:2,minMove:.01}};var r,h;function a(t,i){const s={0:[],1:[t.lineWidth,t.lineWidth],2:[2*t.lineWidth,2*t.lineWidth],3:[6*t.lineWidth,6*t.lineWidth],4:[t.lineWidth,4*t.lineWidth]}[i];t.setLineDash(s)}function l(t,i,s,n){t.beginPath();const e=t.lineWidth%2?.5:0;t.moveTo(s,i+e),t.lineTo(n,i+e),t.stroke()}function o(t,i){if(!t)throw new Error("Assertion failed"+(i?": "+i:""))}function _(t){if(void 0===t)throw new Error("Value is undefined");return t}function u(t){if(null===t)throw new Error("Value is null");return t}function c(t){return u(_(t))}!function(t){t[t.Simple=0]="Simple",t[t.WithSteps=1]="WithSteps",t[t.Curved=2]="Curved"}(r||(r={})),function(t){t[t.Solid=0]="Solid",t[t.Dotted=1]="Dotted",t[t.Dashed=2]="Dashed",t[t.LargeDashed=3]="LargeDashed",t[t.SparseDotted=4]="SparseDotted"}(h||(h={}));class d{constructor(){this.t=[]}i(t,i,s){const n={h:t,l:i,o:!0===s};this.t.push(n)}_(t){const i=this.t.findIndex((i=>t===i.h));i>-1&&this.t.splice(i,1)}u(t){this.t=this.t.filter((i=>i.l!==t))}p(t,i,s){const n=[...this.t];this.t=this.t.filter((t=>!t.o)),n.forEach((n=>n.h(t,i,s)))}v(){return this.t.length>0}m(){this.t=[]}}function f(t,...i){for(const s of i)for(const i in s)void 0!==s[i]&&Object.prototype.hasOwnProperty.call(s,i)&&!["__proto__","constructor","prototype"].includes(i)&&("object"!=typeof s[i]||void 0===t[i]||Array.isArray(s[i])?t[i]=s[i]:f(t[i],s[i]));return t}function p(t){return"number"==typeof t&&isFinite(t)}function v(t){return"number"==typeof t&&t%1==0}function m(t){return"string"==typeof t}function w(t){return"boolean"==typeof t}function g(t){const i=t;if(!i||"object"!=typeof i)return i;let s,n,e;for(n in s=Array.isArray(i)?[]:{},i)i.hasOwnProperty(n)&&(e=i[n],s[n]=e&&"object"==typeof e?g(e):e);return s}function M(t){return null!==t}function b(t){return null===t?void 0:t}const S="-apple-system, BlinkMacSystemFont, 'Trebuchet MS', Roboto, Ubuntu, sans-serif";function x(t,i,s){return void 0===i&&(i=S),`${s=void 0!==s?`${s} `:""}${t}px ${i}`}class C{constructor(t){this.M={S:1,C:5,k:NaN,P:"",T:"",R:"",D:"",V:0,I:0,B:0,A:0,L:0},this.O=t}N(){const t=this.M,i=this.W(),s=this.F();return t.k===i&&t.T===s||(t.k=i,t.T=s,t.P=x(i,s),t.A=2.5/12*i,t.V=t.A,t.I=i/12*t.C,t.B=i/12*t.C,t.L=0),t.R=this.H(),t.D=this.U(),this.M}H(){return this.O.N().layout.textColor}U(){return this.O.$()}W(){return this.O.N().layout.fontSize}F(){return this.O.N().layout.fontFamily}}function y(t){return t<0?0:t>255?255:Math.round(t)||0}function k(t){return.199*t[0]+.687*t[1]+.114*t[2]}class P{constructor(t,i){this.q=new Map,this.Y=t,i&&(this.q=i)}j(t,i){if("transparent"===t)return t;const s=this.K(t),n=s[3];return`rgba(${s[0]}, ${s[1]}, ${s[2]}, ${i*n})`}X(t){const i=this.K(t);return{Z:`rgb(${i[0]}, ${i[1]}, ${i[2]})`,G:k(i)>160?"black":"white"}}J(t){return k(this.K(t))}tt(t,i,s){const[n,e,r,h]=this.K(t),[a,l,o,_]=this.K(i),u=[y(n+s*(a-n)),y(e+s*(l-e)),y(r+s*(o-r)),(c=h+s*(_-h),c<=0||c>1?Math.min(Math.max(c,0),1):Math.round(1e4*c)/1e4)];var c;return`rgba(${u[0]}, ${u[1]}, ${u[2]}, ${u[3]})`}K(t){const i=this.q.get(t);if(i)return i;const s=function(t){const i=document.createElement("div");i.style.display="none",document.body.appendChild(i),i.style.color=t;const s=window.getComputedStyle(i).color;return document.body.removeChild(i),s}(t),n=s.match(/^rgba?\s*\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d+))?\)$/);if(!n){if(this.Y.length)for(const i of this.Y){const s=i(t);if(s)return this.q.set(t,s),s}throw new Error(`Failed to parse color: ${t}`)}const e=[parseInt(n[1],10),parseInt(n[2],10),parseInt(n[3],10),n[4]?parseFloat(n[4]):1];return this.q.set(t,e),e}}class T{constructor(){this.it=[]}st(t){this.it=t}nt(t,i,s){this.it.forEach((n=>{n.nt(t,i,s)}))}}class R{nt(t,i,s){t.useBitmapCoordinateSpace((t=>this.et(t,i,s)))}}class D extends R{constructor(){super(...arguments),this.rt=null}ht(t){this.rt=t}et({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){if(null===this.rt||null===this.rt.lt)return;const n=this.rt.lt,e=this.rt,r=Math.max(1,Math.floor(i))%2/2,h=h=>{t.beginPath();for(let a=n.to-1;a>=n.from;--a){const n=e.ot[a],l=Math.round(n._t*i)+r,o=n.ut*s,_=h*s+r;t.moveTo(l,o),t.arc(l,o,_,0,2*Math.PI)}t.fill()};e.ct>0&&(t.fillStyle=e.dt,h(e.ft+e.ct)),t.fillStyle=e.vt,h(e.ft)}}function V(){return{ot:[{_t:0,ut:0,wt:0,gt:0}],vt:"",dt:"",ft:0,ct:0,lt:null}}const I={from:0,to:1};class B{constructor(t,i,s){this.Mt=new T,this.bt=[],this.St=[],this.xt=!0,this.O=t,this.Ct=i,this.yt=s,this.Mt.st(this.bt)}kt(t){this.Pt(),this.xt=!0}Tt(){return this.xt&&(this.Rt(),this.xt=!1),this.Mt}Pt(){const t=this.yt.Dt();t.length!==this.bt.length&&(this.St=t.map(V),this.bt=this.St.map((t=>{const i=new D;return i.ht(t),i})),this.Mt.st(this.bt))}Rt(){const t=2===this.Ct.N().mode||!this.Ct.Vt(),i=this.yt.It(),s=this.Ct.Bt(),n=this.O.At();this.Pt(),i.forEach(((i,e)=>{const r=this.St[e],h=i.Et(s),a=i.zt();!t&&null!==h&&i.Vt()&&null!==a?(r.vt=h.Lt,r.ft=h.ft,r.ct=h.Ot,r.ot[0].gt=h.gt,r.ot[0].ut=i.Wt().Nt(h.gt,a.Ft),r.dt=h.Ht??this.O.Ut(r.ot[0].ut/i.Wt().$t()),r.ot[0].wt=s,r.ot[0]._t=n.qt(s),r.lt=I):r.lt=null}))}}class A extends R{constructor(t){super(),this.Yt=t}et({context:t,bitmapSize:i,horizontalPixelRatio:s,verticalPixelRatio:n}){if(null===this.Yt)return;const e=this.Yt.jt.Vt,r=this.Yt.Kt.Vt;if(!e&&!r)return;const h=Math.round(this.Yt._t*s),o=Math.round(this.Yt.ut*n);t.lineCap="butt",e&&h>=0&&(t.lineWidth=Math.floor(this.Yt.jt.ct*s),t.strokeStyle=this.Yt.jt.R,t.fillStyle=this.Yt.jt.R,a(t,this.Yt.jt.Xt),function(t,i,s,n){t.beginPath();const e=t.lineWidth%2?.5:0;t.moveTo(i+e,s),t.lineTo(i+e,n),t.stroke()}(t,h,0,i.height)),r&&o>=0&&(t.lineWidth=Math.floor(this.Yt.Kt.ct*n),t.strokeStyle=this.Yt.Kt.R,t.fillStyle=this.Yt.Kt.R,a(t,this.Yt.Kt.Xt),l(t,o,0,i.width))}}class E{constructor(t,i){this.xt=!0,this.Zt={jt:{ct:1,Xt:0,R:"",Vt:!1},Kt:{ct:1,Xt:0,R:"",Vt:!1},_t:0,ut:0},this.Gt=new A(this.Zt),this.Jt=t,this.yt=i}kt(){this.xt=!0}Tt(t){return this.xt&&(this.Rt(),this.xt=!1),this.Gt}Rt(){const t=this.Jt.Vt(),i=this.yt.Qt().N().crosshair,s=this.Zt;if(2===i.mode)return s.Kt.Vt=!1,void(s.jt.Vt=!1);s.Kt.Vt=t&&this.Jt.ti(this.yt),s.jt.Vt=t&&this.Jt.ii(),s.Kt.ct=i.horzLine.width,s.Kt.Xt=i.horzLine.style,s.Kt.R=i.horzLine.color,s.jt.ct=i.vertLine.width,s.jt.Xt=i.vertLine.style,s.jt.R=i.vertLine.color,s._t=this.Jt.si(),s.ut=this.Jt.ni()}}function z(t,i,s,n,e,r){t.fillRect(i+r,s,n-2*r,r),t.fillRect(i+r,s+e-r,n-2*r,r),t.fillRect(i,s,r,e),t.fillRect(i+n-r,s,r,e)}function L(t,i,s,n,e,r){t.save(),t.globalCompositeOperation="copy",t.fillStyle=r,t.fillRect(i,s,n,e),t.restore()}function O(t,i,s,n,e,r){t.beginPath(),t.roundRect?t.roundRect(i,s,n,e,r):(t.lineTo(i+n-r[1],s),0!==r[1]&&t.arcTo(i+n,s,i+n,s+r[1],r[1]),t.lineTo(i+n,s+e-r[2]),0!==r[2]&&t.arcTo(i+n,s+e,i+n-r[2],s+e,r[2]),t.lineTo(i+r[3],s+e),0!==r[3]&&t.arcTo(i,s+e,i,s+e-r[3],r[3]),t.lineTo(i,s+r[0]),0!==r[0]&&t.arcTo(i,s,i+r[0],s,r[0]))}function N(t,i,s,n,e,r,h=0,a=[0,0,0,0],l=""){if(t.save(),!h||!l||l===r)return O(t,i,s,n,e,a),t.fillStyle=r,t.fill(),void t.restore();const o=h/2;var _;O(t,i+o,s+o,n-h,e-h,(_=-o,a.map((t=>0===t?t:t+_)))),"transparent"!==r&&(t.fillStyle=r,t.fill()),"transparent"!==l&&(t.lineWidth=h,t.strokeStyle=l,t.closePath(),t.stroke()),t.restore()}function W(t,i,s,n,e,r,h){t.save(),t.globalCompositeOperation="copy";const a=t.createLinearGradient(0,0,0,e);a.addColorStop(0,r),a.addColorStop(1,h),t.fillStyle=a,t.fillRect(i,s,n,e),t.restore()}class F{constructor(t,i){this.ht(t,i)}ht(t,i){this.Yt=t,this.ei=i}$t(t,i){return this.Yt.Vt?t.k+t.A+t.V:0}nt(t,i,s,n){if(!this.Yt.Vt||0===this.Yt.ri.length)return;const e=this.Yt.R,r=this.ei.Z,h=t.useBitmapCoordinateSpace((t=>{const h=t.context;h.font=i.P;const a=this.hi(t,i,s,n),l=a.ai;return a.li?N(h,l.oi,l._i,l.ui,l.ci,r,l.di,[l.ft,0,0,l.ft],r):N(h,l.fi,l._i,l.ui,l.ci,r,l.di,[0,l.ft,l.ft,0],r),this.Yt.pi&&(h.fillStyle=e,h.fillRect(l.fi,l.mi,l.wi-l.fi,l.gi)),this.Yt.Mi&&(h.fillStyle=i.D,h.fillRect(a.li?l.bi-l.di:0,l._i,l.di,l.Si-l._i)),a}));t.useMediaCoordinateSpace((({context:t})=>{const s=h.xi;t.font=i.P,t.textAlign=h.li?"right":"left",t.textBaseline="middle",t.fillStyle=e,t.fillText(this.Yt.ri,s.Ci,(s._i+s.Si)/2+s.yi)}))}hi(t,i,s,n){const{context:e,bitmapSize:r,mediaSize:h,horizontalPixelRatio:a,verticalPixelRatio:l}=t,o=this.Yt.pi||!this.Yt.ki?i.C:0,_=this.Yt.Pi?i.S:0,u=i.A+this.ei.Ti,c=i.V+this.ei.Ri,d=i.I,f=i.B,p=this.Yt.ri,v=i.k,m=s.Di(e,p),w=Math.ceil(s.Vi(e,p)),g=v+u+c,M=i.S+d+f+w+o,b=Math.max(1,Math.floor(l));let S=Math.round(g*l);S%2!=b%2&&(S+=1);const x=_>0?Math.max(1,Math.floor(_*a)):0,C=Math.round(M*a),y=Math.round(o*a),k=this.ei.Ii??this.ei.Bi,P=Math.round(k*l)-Math.floor(.5*l),T=Math.floor(P+b/2-S/2),R=T+S,D="right"===n,V=D?h.width-_:_,I=D?r.width-x:x;let B,A,E;return D?(B=I-C,A=I-y,E=V-o-d-_):(B=I+C,A=I+y,E=V+o+d),{li:D,ai:{_i:T,mi:P,Si:R,ui:C,ci:S,ft:2*a,di:x,oi:B,fi:I,wi:A,gi:b,bi:r.width},xi:{_i:T/l,Si:R/l,Ci:E,yi:m}}}}class H{constructor(t){this.Ai={Bi:0,Z:"#000",Ri:0,Ti:0},this.Ei={ri:"",Vt:!1,pi:!0,ki:!1,Ht:"",R:"#FFF",Mi:!1,Pi:!1},this.zi={ri:"",Vt:!1,pi:!1,ki:!0,Ht:"",R:"#FFF",Mi:!0,Pi:!0},this.xt=!0,this.Li=new(t||F)(this.Ei,this.Ai),this.Oi=new(t||F)(this.zi,this.Ai)}ri(){return this.Ni(),this.Ei.ri}Bi(){return this.Ni(),this.Ai.Bi}kt(){this.xt=!0}$t(t,i=!1){return Math.max(this.Li.$t(t,i),this.Oi.$t(t,i))}Wi(){return this.Ai.Ii||0}Fi(t){this.Ai.Ii=t}Hi(){return this.Ni(),this.Ei.Vt||this.zi.Vt}Ui(){return this.Ni(),this.Ei.Vt}Tt(t){return this.Ni(),this.Ei.pi=this.Ei.pi&&t.N().ticksVisible,this.zi.pi=this.zi.pi&&t.N().ticksVisible,this.Li.ht(this.Ei,this.Ai),this.Oi.ht(this.zi,this.Ai),this.Li}$i(){return this.Ni(),this.Li.ht(this.Ei,this.Ai),this.Oi.ht(this.zi,this.Ai),this.Oi}Ni(){this.xt&&(this.Ei.pi=!0,this.zi.pi=!1,this.qi(this.Ei,this.zi,this.Ai))}}class U extends H{constructor(t,i,s){super(),this.Jt=t,this.Yi=i,this.ji=s}qi(t,i,s){if(t.Vt=!1,2===this.Jt.N().mode)return;const n=this.Jt.N().horzLine;if(!n.labelVisible)return;const e=this.Yi.zt();if(!this.Jt.Vt()||this.Yi.Ki()||null===e)return;const r=this.Yi.Xi().X(n.labelBackgroundColor);s.Z=r.Z,t.R=r.G;const h=2/12*this.Yi.k();s.Ti=h,s.Ri=h;const a=this.ji(this.Yi);s.Bi=a.Bi,t.ri=this.Yi.Zi(a.gt,e),t.Vt=!0}}const $=/[1-9]/g;class q{constructor(){this.Yt=null}ht(t){this.Yt=t}nt(t,i){if(null===this.Yt||!1===this.Yt.Vt||0===this.Yt.ri.length)return;const s=t.useMediaCoordinateSpace((({context:t})=>(t.font=i.P,Math.round(i.Gi.Vi(t,u(this.Yt).ri,$)))));if(s<=0)return;const n=i.Ji,e=s+2*n,r=e/2,h=this.Yt.Qi;let a=this.Yt.Bi,l=Math.floor(a-r)+.5;l<0?(a+=Math.abs(0-l),l=Math.floor(a-r)+.5):l+e>h&&(a-=Math.abs(h-(l+e)),l=Math.floor(a-r)+.5);const o=l+e,_=Math.ceil(0+i.S+i.C+i.A+i.k+i.V);t.useBitmapCoordinateSpace((({context:t,horizontalPixelRatio:s,verticalPixelRatio:n})=>{const e=u(this.Yt);t.fillStyle=e.Z;const r=Math.round(l*s),h=Math.round(0*n),a=Math.round(o*s),c=Math.round(_*n),d=Math.round(2*s);if(t.beginPath(),t.moveTo(r,h),t.lineTo(r,c-d),t.arcTo(r,c,r+d,c,d),t.lineTo(a-d,c),t.arcTo(a,c,a,c-d,d),t.lineTo(a,h),t.fill(),e.pi){const r=Math.round(e.Bi*s),a=h,l=Math.round((a+i.C)*n);t.fillStyle=e.R;const o=Math.max(1,Math.floor(s)),_=Math.floor(.5*s);t.fillRect(r-_,a,o,l-a)}})),t.useMediaCoordinateSpace((({context:t})=>{const s=u(this.Yt),e=0+i.S+i.C+i.A+i.k/2;t.font=i.P,t.textAlign="left",t.textBaseline="middle",t.fillStyle=s.R;const r=i.Gi.Di(t,"Apr0");t.translate(l+n,e+r),t.fillText(s.ri,0,0)}))}}class Y{constructor(t,i,s){this.xt=!0,this.Gt=new q,this.Zt={Vt:!1,Z:"#4c525e",R:"white",ri:"",Qi:0,Bi:NaN,pi:!0},this.Ct=t,this.ts=i,this.ji=s}kt(){this.xt=!0}Tt(){return this.xt&&(this.Rt(),this.xt=!1),this.Gt.ht(this.Zt),this.Gt}Rt(){const t=this.Zt;if(t.Vt=!1,2===this.Ct.N().mode)return;const i=this.Ct.N().vertLine;if(!i.labelVisible)return;const s=this.ts.At();if(s.Ki())return;t.Qi=s.Qi();const n=this.ji();if(null===n)return;t.Bi=n.Bi;const e=s.ss(this.Ct.Bt());t.ri=s.ns(u(e)),t.Vt=!0;const r=this.ts.Xi().X(i.labelBackgroundColor);t.Z=r.Z,t.R=r.G,t.pi=s.N().ticksVisible}}class j{constructor(){this.es=null,this.rs=0}hs(){return this.rs}ls(t){this.rs=t}Wt(){return this.es}_s(t){this.es=t}us(t){return[]}cs(){return[]}Vt(){return!0}}var K;!function(t){t[t.Normal=0]="Normal",t[t.Magnet=1]="Magnet",t[t.Hidden=2]="Hidden",t[t.MagnetOHLC=3]="MagnetOHLC"}(K||(K={}));class X extends j{constructor(t,i){super(),this.yt=null,this.ds=NaN,this.fs=0,this.ps=!1,this.vs=new Map,this.ws=!1,this.gs=new WeakMap,this.Ms=new WeakMap,this.bs=NaN,this.Ss=NaN,this.xs=NaN,this.Cs=NaN,this.ts=t,this.ys=i;this.ks=((t,i)=>s=>{const n=i(),e=t();if(s===u(this.yt).Ps())return{gt:e,Bi:n};{const t=u(s.zt());return{gt:s.Ts(n,t),Bi:n}}})((()=>this.ds),(()=>this.Ss));const s=((t,i)=>()=>{const s=this.ts.At().Rs(t()),n=i();return s&&Number.isFinite(n)?{wt:s,Bi:n}:null})((()=>this.fs),(()=>this.si()));this.Ds=new Y(this,t,s)}N(){return this.ys}Vs(t,i){this.xs=t,this.Cs=i}Is(){this.xs=NaN,this.Cs=NaN}Bs(){return this.xs}As(){return this.Cs}Es(t,i,s){this.ws||(this.ws=!0),this.ps=!0,this.zs(t,i,s)}Bt(){return this.fs}si(){return this.bs}ni(){return this.Ss}Vt(){return this.ps}Ls(){this.ps=!1,this.Os(),this.ds=NaN,this.bs=NaN,this.Ss=NaN,this.yt=null,this.Is(),this.Ns()}Ws(t){let i=this.gs.get(t);i||(i=new E(this,t),this.gs.set(t,i));let s=this.Ms.get(t);return s||(s=new B(this.ts,this,t),this.Ms.set(t,s)),[i,s]}ti(t){return t===this.yt&&this.ys.horzLine.visible}ii(){return this.ys.vertLine.visible}Fs(t,i){this.ps&&this.yt===t||this.vs.clear();const s=[];return this.yt===t&&s.push(this.Hs(this.vs,i,this.ks)),s}cs(){return this.ps?[this.Ds]:[]}Us(){return this.yt}Ns(){this.ts.$s().forEach((t=>{this.gs.get(t)?.kt(),this.Ms.get(t)?.kt()})),this.vs.forEach((t=>t.kt())),this.Ds.kt()}qs(t){return t&&!t.Ps().Ki()?t.Ps():null}zs(t,i,s){this.Ys(t,i,s)&&this.Ns()}Ys(t,i,s){const n=this.bs,e=this.Ss,r=this.ds,h=this.fs,a=this.yt,l=this.qs(s);this.fs=t,this.bs=isNaN(t)?NaN:this.ts.At().qt(t),this.yt=s;const o=null!==l?l.zt():null;return null!==l&&null!==o?(this.ds=i,this.Ss=l.Nt(i,o)):(this.ds=NaN,this.Ss=NaN),n!==this.bs||e!==this.Ss||h!==this.fs||r!==this.ds||a!==this.yt}Os(){const t=this.ts.js().map((t=>t.Xs().Ks())).filter(M),i=0===t.length?null:Math.max(...t);this.fs=null!==i?i:NaN}Hs(t,i,s){let n=t.get(i);return void 0===n&&(n=new U(this,i,s),t.set(i,n)),n}}function Z(t){return"left"===t||"right"===t}class G{constructor(t){this.Zs=new Map,this.Gs=[],this.Js=t}Qs(t,i){const s=function(t,i){return void 0===t?i:{tn:Math.max(t.tn,i.tn),sn:t.sn||i.sn}}(this.Zs.get(t),i);this.Zs.set(t,s)}nn(){return this.Js}en(t){const i=this.Zs.get(t);return void 0===i?{tn:this.Js}:{tn:Math.max(this.Js,i.tn),sn:i.sn}}rn(){this.hn(),this.Gs=[{an:0}]}ln(t){this.hn(),this.Gs=[{an:1,Ft:t}]}_n(t){this.un(),this.Gs.push({an:5,Ft:t})}hn(){this.un(),this.Gs.push({an:6})}cn(){this.hn(),this.Gs=[{an:4}]}dn(t){this.hn(),this.Gs.push({an:2,Ft:t})}fn(t){this.hn(),this.Gs.push({an:3,Ft:t})}pn(){return this.Gs}vn(t){for(const i of t.Gs)this.mn(i);this.Js=Math.max(this.Js,t.Js),t.Zs.forEach(((t,i)=>{this.Qs(i,t)}))}static wn(){return new G(2)}static gn(){return new G(3)}mn(t){switch(t.an){case 0:this.rn();break;case 1:this.ln(t.Ft);break;case 2:this.dn(t.Ft);break;case 3:this.fn(t.Ft);break;case 4:this.cn();break;case 5:this._n(t.Ft);break;case 6:this.un()}}un(){const t=this.Gs.findIndex((t=>5===t.an));-1!==t&&this.Gs.splice(t,1)}}const J=".";function Q(t,i){if(!p(t))return"n/a";if(!v(i))throw new TypeError("invalid length");if(i<0||i>16)throw new TypeError("invalid length");if(0===i)return t.toString();return("0000000000000000"+t.toString()).slice(-i)}class tt{constructor(t,i){if(i||(i=1),p(t)&&v(t)||(t=100),t<0)throw new TypeError("invalid base");this.Yi=t,this.Mn=i,this.bn()}format(t){const i=t<0?"−":"";return t=Math.abs(t),i+this.Sn(t)}bn(){if(this.xn=0,this.Yi>0&&this.Mn>0){let t=this.Yi;for(;t>1;)t/=10,this.xn++}}Sn(t){const i=this.Yi/this.Mn;let s=Math.floor(t),n="";const e=void 0!==this.xn?this.xn:NaN;if(i>1){let r=+(Math.round(t*i)-s*i).toFixed(this.xn);r>=i&&(r-=i,s+=1),n=J+Q(+r.toFixed(this.xn)*this.Mn,e)}else s=Math.round(s*i)/i,e>0&&(n=J+Q(0,e));return s.toFixed(0)+n}}class it extends tt{constructor(t=100){super(t)}format(t){return`${super.format(t)}%`}}class st{constructor(t){this.Cn=t}format(t){let i="";return t<0&&(i="-",t=-t),t<995?i+this.yn(t):t<999995?i+this.yn(t/1e3)+"K":t<999999995?(t=1e3*Math.round(t/1e3),i+this.yn(t/1e6)+"M"):(t=1e6*Math.round(t/1e6),i+this.yn(t/1e9)+"B")}yn(t){let i;const s=Math.pow(10,this.Cn);return i=(t=Math.round(t*s)/s)>=1e-15&&t<1?t.toFixed(this.Cn).replace(/\.?0+$/,""):String(t),i.replace(/(\.[1-9]*)0+$/,((t,i)=>i))}}const nt=/[2-9]/g;class et{constructor(t=50){this.kn=0,this.Pn=1,this.Tn=1,this.Rn={},this.Dn=new Map,this.Vn=t}In(){this.kn=0,this.Dn.clear(),this.Pn=1,this.Tn=1,this.Rn={}}Vi(t,i,s){return this.Bn(t,i,s).width}Di(t,i,s){const n=this.Bn(t,i,s);return((n.actualBoundingBoxAscent||0)-(n.actualBoundingBoxDescent||0))/2}Bn(t,i,s){const n=s||nt,e=String(i).replace(n,"0");if(this.Dn.has(e))return _(this.Dn.get(e)).An;if(this.kn===this.Vn){const t=this.Rn[this.Tn];delete this.Rn[this.Tn],this.Dn.delete(t),this.Tn++,this.kn--}t.save(),t.textBaseline="middle";const r=t.measureText(e);return t.restore(),0===r.width&&i.length||(this.Dn.set(e,{An:r,En:this.Pn}),this.Rn[this.Pn]=e,this.kn++,this.Pn++),r}}class rt{constructor(t){this.zn=null,this.M=null,this.Ln="right",this.On=t}Nn(t,i,s){this.zn=t,this.M=i,this.Ln=s}nt(t){null!==this.M&&null!==this.zn&&this.zn.nt(t,this.M,this.On,this.Ln)}}class ht{constructor(t,i,s){this.Wn=t,this.On=new et(50),this.Fn=i,this.O=s,this.W=-1,this.Gt=new rt(this.On)}Tt(){const t=this.O.Hn(this.Fn);if(null===t)return null;const i=t.Un(this.Fn)?t.$n():this.Fn.Wt();if(null===i)return null;const s=t.qn(i);if("overlay"===s)return null;const n=this.O.Yn();return n.k!==this.W&&(this.W=n.k,this.On.In()),this.Gt.Nn(this.Wn.$i(),n,s),this.Gt}}class at extends R{constructor(){super(...arguments),this.Yt=null}ht(t){this.Yt=t}jn(t,i){if(!this.Yt?.Vt)return null;const{ut:s,ct:n,Kn:e}=this.Yt;return i>=s-n-7&&i<=s+n+7?{Xn:this.Yt,Kn:e}:null}et({context:t,bitmapSize:i,horizontalPixelRatio:s,verticalPixelRatio:n}){if(null===this.Yt)return;if(!1===this.Yt.Vt)return;const e=Math.round(this.Yt.ut*n);e<0||e>i.height||(t.lineCap="butt",t.strokeStyle=this.Yt.R,t.lineWidth=Math.floor(this.Yt.ct*s),a(t,this.Yt.Xt),l(t,e,0,i.width))}}class lt{constructor(t){this.Zn={ut:0,R:"rgba(0, 0, 0, 0)",ct:1,Xt:0,Vt:!1},this.Gn=new at,this.xt=!0,this.Jn=t,this.Qn=t.Qt(),this.Gn.ht(this.Zn)}kt(){this.xt=!0}Tt(){return this.Jn.Vt()?(this.xt&&(this.te(),this.xt=!1),this.Gn):null}}class ot extends lt{constructor(t){super(t)}te(){this.Zn.Vt=!1;const t=this.Jn.Wt(),i=t.ie().ie;if(2!==i&&3!==i)return;const s=this.Jn.N();if(!s.baseLineVisible||!this.Jn.Vt())return;const n=this.Jn.zt();null!==n&&(this.Zn.Vt=!0,this.Zn.ut=t.Nt(n.Ft,n.Ft),this.Zn.R=s.baseLineColor,this.Zn.ct=s.baseLineWidth,this.Zn.Xt=s.baseLineStyle)}}class _t extends R{constructor(){super(...arguments),this.Yt=null}ht(t){this.Yt=t}se(){return this.Yt}et({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){const n=this.Yt;if(null===n)return;const e=Math.max(1,Math.floor(i)),r=e%2/2,h=Math.round(n.ne.x*i)+r,a=n.ne.y*s;t.fillStyle=n.ee,t.beginPath();const l=Math.max(2,1.5*n.re)*i;t.arc(h,a,l,0,2*Math.PI,!1),t.fill(),t.fillStyle=n.he,t.beginPath(),t.arc(h,a,n.ft*i,0,2*Math.PI,!1),t.fill(),t.lineWidth=e,t.strokeStyle=n.ae,t.beginPath(),t.arc(h,a,n.ft*i+e/2,0,2*Math.PI,!1),t.stroke()}}const ut=[{le:0,oe:.25,_e:4,ue:10,ce:.25,de:0,fe:.4,pe:.8},{le:.25,oe:.525,_e:10,ue:14,ce:0,de:0,fe:.8,pe:0},{le:.525,oe:1,_e:14,ue:14,ce:0,de:0,fe:0,pe:0}];class ct{constructor(t){this.Gt=new _t,this.xt=!0,this.ve=!0,this.me=performance.now(),this.we=this.me-1,this.ge=t}Me(){this.we=this.me-1,this.kt()}be(){if(this.kt(),2===this.ge.N().lastPriceAnimation){const t=performance.now(),i=this.we-t;if(i>0)return void(i<650&&(this.we+=2600));this.me=t,this.we=t+2600}}kt(){this.xt=!0}Se(){this.ve=!0}Vt(){return 0!==this.ge.N().lastPriceAnimation}xe(){switch(this.ge.N().lastPriceAnimation){case 0:return!1;case 1:return!0;case 2:return performance.now()<=this.we}}Tt(){return this.xt?(this.Rt(),this.xt=!1,this.ve=!1):this.ve&&(this.Ce(),this.ve=!1),this.Gt}Rt(){this.Gt.ht(null);const t=this.ge.Qt().At(),i=t.ye(),s=this.ge.zt();if(null===i||null===s)return;const n=this.ge.ke(!0);if(n.Pe||!i.Te(n.Re))return;const e={x:t.qt(n.Re),y:this.ge.Wt().Nt(n.gt,s.Ft)},r=n.R,h=this.ge.N().lineWidth,a=this.De(this.Ve(),r);this.Gt.ht({ee:r,re:h,he:a.he,ae:a.ae,ft:a.ft,ne:e})}Ce(){const t=this.Gt.se();if(null!==t){const i=this.De(this.Ve(),t.ee);t.he=i.he,t.ae=i.ae,t.ft=i.ft}}Ve(){return this.xe()?performance.now()-this.me:2599}Ie(t,i,s,n){const e=s+(n-s)*i;return this.ge.Qt().Xi().j(t,e)}De(t,i){const s=t%2600/2600;let n;for(const t of ut)if(s>=t.le&&s<=t.oe){n=t;break}o(void 0!==n,"Last price animation internal logic error");const e=(s-n.le)/(n.oe-n.le);return{he:this.Ie(i,e,n.ce,n.de),ae:this.Ie(i,e,n.fe,n.pe),ft:(r=e,h=n._e,a=n.ue,h+(a-h)*r)};var r,h,a}}class dt extends lt{constructor(t){super(t)}te(){const t=this.Zn;t.Vt=!1;const i=this.Jn.N();if(!i.priceLineVisible||!this.Jn.Vt())return;const s=this.Jn.ke(0===i.priceLineSource);s.Pe||(t.Vt=!0,t.ut=s.Bi,t.R=this.Jn.Be(s.R),t.ct=i.priceLineWidth,t.Xt=i.priceLineStyle)}}class ft extends H{constructor(t){super(),this.Jt=t}qi(t,i,s){t.Vt=!1,i.Vt=!1;const n=this.Jt;if(!n.Vt())return;const e=n.N(),r=e.lastValueVisible,h=""!==n.Ae(),a=0===e.seriesLastValueMode,l=n.ke(!1);if(l.Pe)return;r&&(t.ri=this.Ee(l,r,a),t.Vt=0!==t.ri.length),(h||a)&&(i.ri=this.ze(l,r,h,a),i.Vt=i.ri.length>0);const o=n.Be(l.R),_=this.Jt.Qt().Xi().X(o);s.Z=_.Z,s.Bi=l.Bi,i.Ht=n.Qt().Ut(l.Bi/n.Wt().$t()),t.Ht=o,t.R=_.G,i.R=_.G}ze(t,i,s,n){let e="";const r=this.Jt.Ae();return s&&0!==r.length&&(e+=`${r} `),i&&n&&(e+=this.Jt.Wt().Le()?t.Oe:t.Ne),e.trim()}Ee(t,i,s){return i?s?this.Jt.Wt().Le()?t.Ne:t.Oe:t.ri:""}}function pt(t,i,s,n){const e=Number.isFinite(i),r=Number.isFinite(s);return e&&r?t(i,s):e||r?e?i:s:n}class vt{constructor(t,i){this.We=t,this.Fe=i}He(t){return null!==t&&(this.We===t.We&&this.Fe===t.Fe)}Ue(){return new vt(this.We,this.Fe)}$e(){return this.We}qe(){return this.Fe}Ye(){return this.Fe-this.We}Ki(){return this.Fe===this.We||Number.isNaN(this.Fe)||Number.isNaN(this.We)}vn(t){return null===t?this:new vt(pt(Math.min,this.$e(),t.$e(),-1/0),pt(Math.max,this.qe(),t.qe(),1/0))}je(t){if(!p(t))return;if(0===this.Fe-this.We)return;const i=.5*(this.Fe+this.We);let s=this.Fe-i,n=this.We-i;s*=t,n*=t,this.Fe=i+s,this.We=i+n}Ke(t){p(t)&&(this.Fe+=t,this.We+=t)}Xe(){return{minValue:this.We,maxValue:this.Fe}}static Ze(t){return null===t?null:new vt(t.minValue,t.maxValue)}}class mt{constructor(t,i){this.Ge=t,this.Je=i||null}Qe(){return this.Ge}tr(){return this.Je}Xe(){return{priceRange:null===this.Ge?null:this.Ge.Xe(),margins:this.Je||void 0}}static Ze(t){return null===t?null:new mt(vt.Ze(t.priceRange),t.margins)}}class wt extends lt{constructor(t,i){super(t),this.ir=i}te(){const t=this.Zn;t.Vt=!1;const i=this.ir.N();if(!this.Jn.Vt()||!i.lineVisible)return;const s=this.ir.sr();null!==s&&(t.Vt=!0,t.ut=s,t.R=i.color,t.ct=i.lineWidth,t.Xt=i.lineStyle,t.Kn=this.ir.N().id)}}class gt extends H{constructor(t,i){super(),this.ge=t,this.ir=i}qi(t,i,s){t.Vt=!1,i.Vt=!1;const n=this.ir.N(),e=n.axisLabelVisible,r=""!==n.title,h=this.ge;if(!e||!h.Vt())return;const a=this.ir.sr();if(null===a)return;r&&(i.ri=n.title,i.Vt=!0),i.Ht=h.Qt().Ut(a/h.Wt().$t()),t.ri=this.nr(n.price),t.Vt=!0;const l=this.ge.Qt().Xi().X(n.axisLabelColor||n.color);s.Z=l.Z;const o=n.axisLabelTextColor||l.G;t.R=o,i.R=o,s.Bi=a}nr(t){const i=this.ge.zt();return null===i?"":this.ge.Wt().Zi(t,i.Ft)}}class Mt{constructor(t,i){this.ge=t,this.ys=i,this.er=new wt(t,this),this.Wn=new gt(t,this),this.rr=new ht(this.Wn,t,t.Qt())}hr(t){f(this.ys,t),this.kt(),this.ge.Qt().ar()}N(){return this.ys}lr(){return this.er}_r(){return this.rr}ur(){return this.Wn}kt(){this.er.kt(),this.Wn.kt()}sr(){const t=this.ge,i=t.Wt();if(t.Qt().At().Ki()||i.Ki())return null;const s=t.zt();return null===s?null:i.Nt(this.ys.price,s.Ft)}}class bt extends j{constructor(t){super(),this.ts=t}Qt(){return this.ts}}const St={Bar:(t,i,s,n)=>{const e=i.upColor,r=i.downColor,h=u(t(s,n)),a=c(h.Ft[0])<=c(h.Ft[3]);return{cr:h.R??(a?e:r)}},Candlestick:(t,i,s,n)=>{const e=i.upColor,r=i.downColor,h=i.borderUpColor,a=i.borderDownColor,l=i.wickUpColor,o=i.wickDownColor,_=u(t(s,n)),d=c(_.Ft[0])<=c(_.Ft[3]);return{cr:_.R??(d?e:r),dr:_.Ht??(d?h:a),pr:_.vr??(d?l:o)}},Custom:(t,i,s,n)=>({cr:u(t(s,n)).R??i.color}),Area:(t,i,s,n)=>{const e=u(t(s,n));return{cr:e.vt??i.lineColor,vt:e.vt??i.lineColor,mr:e.mr??i.topColor,wr:e.wr??i.bottomColor}},Baseline:(t,i,s,n)=>{const e=u(t(s,n));return{cr:e.Ft[3]>=i.baseValue.price?i.topLineColor:i.bottomLineColor,gr:e.gr??i.topLineColor,Mr:e.Mr??i.bottomLineColor,br:e.br??i.topFillColor1,Sr:e.Sr??i.topFillColor2,Cr:e.Cr??i.bottomFillColor1,yr:e.yr??i.bottomFillColor2}},Line:(t,i,s,n)=>{const e=u(t(s,n));return{cr:e.R??i.color,vt:e.R??i.color}},Histogram:(t,i,s,n)=>({cr:u(t(s,n)).R??i.color})};class xt{constructor(t){this.kr=(t,i)=>void 0!==i?i.Ft:this.ge.Xs().Pr(t),this.ge=t,this.Tr=St[t.Rr()]}Dr(t,i){return this.Tr(this.kr,this.ge.N(),t,i)}}function Ct(t,i,s,n,e=0,r=i.length){let h=r-e;for(;0<h;){const r=h>>1,a=e+r;n(i[a],s)===t?(e=a+1,h-=r+1):h=r}return e}const yt=Ct.bind(null,!0),kt=Ct.bind(null,!1);var Pt;!function(t){t[t.NearestLeft=-1]="NearestLeft",t[t.None=0]="None",t[t.NearestRight=1]="NearestRight"}(Pt||(Pt={}));const Tt=30;class Rt{constructor(){this.Vr=[],this.Ir=new Map,this.Br=new Map,this.Ar=[]}Er(){return this.zr()>0?this.Vr[this.Vr.length-1]:null}Lr(){return this.zr()>0?this.Or(0):null}Ks(){return this.zr()>0?this.Or(this.Vr.length-1):null}zr(){return this.Vr.length}Ki(){return 0===this.zr()}Te(t){return null!==this.Nr(t,0)}Pr(t){return this.Wr(t)}Wr(t,i=0){const s=this.Nr(t,i);return null===s?null:{...this.Fr(s),Re:this.Or(s)}}Hr(){return this.Vr}Ur(t,i,s){if(this.Ki())return null;let n=null;for(const e of s){n=Dt(n,this.$r(t,i,e))}return n}ht(t){this.Br.clear(),this.Ir.clear(),this.Vr=t,this.Ar=t.map((t=>t.Re))}qr(){return this.Ar}Or(t){return this.Vr[t].Re}Fr(t){return this.Vr[t]}Nr(t,i){const s=this.Yr(t);if(null===s&&0!==i)switch(i){case-1:return this.jr(t);case 1:return this.Kr(t);default:throw new TypeError("Unknown search mode")}return s}jr(t){let i=this.Xr(t);return i>0&&(i-=1),i!==this.Vr.length&&this.Or(i)<t?i:null}Kr(t){const i=this.Zr(t);return i!==this.Vr.length&&t<this.Or(i)?i:null}Yr(t){const i=this.Xr(t);return i===this.Vr.length||t<this.Vr[i].Re?null:i}Xr(t){return yt(this.Vr,t,((t,i)=>t.Re<i))}Zr(t){return kt(this.Vr,t,((t,i)=>t.Re>i))}Gr(t,i,s){let n=null;for(let e=t;e<i;e++){const t=this.Vr[e].Ft[s];Number.isNaN(t)||(null===n?n={Jr:t,Qr:t}:(t<n.Jr&&(n.Jr=t),t>n.Qr&&(n.Qr=t)))}return n}$r(t,i,s){if(this.Ki())return null;let n=null;const e=u(this.Lr()),r=u(this.Ks()),h=Math.max(t,e),a=Math.min(i,r),l=Math.ceil(h/Tt)*Tt,o=Math.max(l,Math.floor(a/Tt)*Tt);{const t=this.Xr(h),e=this.Zr(Math.min(a,l,i));n=Dt(n,this.Gr(t,e,s))}let _=this.Ir.get(s);void 0===_&&(_=new Map,this.Ir.set(s,_));for(let t=Math.max(l+1,h);t<o;t+=Tt){const i=Math.floor(t/Tt);let e=_.get(i);if(void 0===e){const t=this.Xr(i*Tt),n=this.Zr((i+1)*Tt-1);e=this.Gr(t,n,s),_.set(i,e)}n=Dt(n,e)}{const t=this.Xr(o),i=this.Zr(a);n=Dt(n,this.Gr(t,i,s))}return n}}function Dt(t,i){if(null===t)return i;if(null===i)return t;return{Jr:Math.min(t.Jr,i.Jr),Qr:Math.max(t.Qr,i.Qr)}}class Vt{constructor(t){this.th=t}nt(t,i,s){this.th.draw(t)}ih(t,i,s){this.th.drawBackground?.(t)}}class It{constructor(t){this.Dn=null,this.sh=t}Tt(){const t=this.sh.renderer();if(null===t)return null;if(this.Dn?.nh===t)return this.Dn.eh;const i=new Vt(t);return this.Dn={nh:t,eh:i},i}rh(){return this.sh.zOrder?.()??"normal"}}class Bt{constructor(t){this.hh=null,this.ah=t}oh(){return this.ah}Ns(){this.ah.updateAllViews?.()}Ws(){const t=this.ah.paneViews?.()??[];if(this.hh?.nh===t)return this.hh.eh;const i=t.map((t=>new It(t)));return this.hh={nh:t,eh:i},i}jn(t,i){return this.ah.hitTest?.(t,i)??null}}let At=class extends Bt{us(){return[]}};class Et{constructor(t){this.th=t}nt(t,i,s){this.th.draw(t)}ih(t,i,s){this.th.drawBackground?.(t)}}class zt{constructor(t){this.Dn=null,this.sh=t}Tt(){const t=this.sh.renderer();if(null===t)return null;if(this.Dn?.nh===t)return this.Dn.eh;const i=new Et(t);return this.Dn={nh:t,eh:i},i}rh(){return this.sh.zOrder?.()??"normal"}}function Lt(t){return{ri:t.text(),Bi:t.coordinate(),Ii:t.fixedCoordinate?.(),R:t.textColor(),Z:t.backColor(),Vt:t.visible?.()??!0,pi:t.tickVisible?.()??!0}}class Ot{constructor(t,i){this.Gt=new q,this._h=t,this.uh=i}Tt(){return this.Gt.ht({Qi:this.uh.Qi(),...Lt(this._h)}),this.Gt}}class Nt extends H{constructor(t,i){super(),this._h=t,this.Yi=i}qi(t,i,s){const n=Lt(this._h);s.Z=n.Z,t.R=n.R;const e=2/12*this.Yi.k();s.Ti=e,s.Ri=e,s.Bi=n.Bi,s.Ii=n.Ii,t.ri=n.ri,t.Vt=n.Vt,t.pi=n.pi}}class Wt extends Bt{constructor(t,i){super(t),this.dh=null,this.fh=null,this.ph=null,this.mh=null,this.ge=i}cs(){const t=this.ah.timeAxisViews?.()??[];if(this.dh?.nh===t)return this.dh.eh;const i=this.ge.Qt().At(),s=t.map((t=>new Ot(t,i)));return this.dh={nh:t,eh:s},s}Fs(){const t=this.ah.priceAxisViews?.()??[];if(this.fh?.nh===t)return this.fh.eh;const i=this.ge.Wt(),s=t.map((t=>new Nt(t,i)));return this.fh={nh:t,eh:s},s}wh(){const t=this.ah.priceAxisPaneViews?.()??[];if(this.ph?.nh===t)return this.ph.eh;const i=t.map((t=>new zt(t)));return this.ph={nh:t,eh:i},i}gh(){const t=this.ah.timeAxisPaneViews?.()??[];if(this.mh?.nh===t)return this.mh.eh;const i=t.map((t=>new zt(t)));return this.mh={nh:t,eh:i},i}Mh(t,i){return this.ah.autoscaleInfo?.(t,i)??null}}function Ft(t,i,s,n){t.forEach((t=>{i(t).forEach((t=>{t.rh()===s&&n.push(t)}))}))}function Ht(t){return t.Ws()}function Ut(t){return t.wh()}function $t(t){return t.gh()}const qt=["Area","Line","Baseline"];class Yt extends bt{constructor(t,i,s,n,e){super(t),this.Yt=new Rt,this.er=new dt(this),this.bh=[],this.Sh=new ot(this),this.xh=null,this.Ch=null,this.yh=null,this.kh=[],this.ys=s,this.Ph=i;const r=new ft(this);this.vs=[r],this.rr=new ht(r,this,t),qt.includes(this.Ph)&&(this.xh=new ct(this)),this.Th(),this.sh=n(this,this.Qt(),e)}m(){null!==this.yh&&clearTimeout(this.yh)}Be(t){return this.ys.priceLineColor||t}ke(t){const i={Pe:!0},s=this.Wt();if(this.Qt().At().Ki()||s.Ki()||this.Yt.Ki())return i;const n=this.Qt().At().ye(),e=this.zt();if(null===n||null===e)return i;let r,h;if(t){const t=this.Yt.Er();if(null===t)return i;r=t,h=t.Re}else{const t=this.Yt.Wr(n.bi(),-1);if(null===t)return i;if(r=this.Yt.Pr(t.Re),null===r)return i;h=t.Re}const a=r.Ft[3],l=this.Rh().Dr(h,{Ft:r}),o=s.Nt(a,e.Ft);return{Pe:!1,gt:a,ri:s.Zi(a,e.Ft),Oe:s.Dh(a),Ne:s.Vh(a,e.Ft),R:l.cr,Bi:o,Re:h}}Rh(){return null!==this.Ch||(this.Ch=new xt(this)),this.Ch}N(){return this.ys}hr(t){const i=t.priceScaleId;void 0!==i&&i!==this.ys.priceScaleId&&this.Qt().Ih(this,i),f(this.ys,t),void 0!==t.priceFormat&&(this.Th(),this.Qt().Bh()),this.Qt().Ah(this),this.Qt().Eh(),this.sh.kt("options")}ht(t,i){this.Yt.ht(t),this.sh.kt("data"),null!==this.xh&&(i&&i.zh?this.xh.be():0===t.length&&this.xh.Me());const s=this.Qt().Hn(this);this.Qt().Lh(s),this.Qt().Ah(this),this.Qt().Eh(),this.Qt().ar()}Oh(t){const i=new Mt(this,t);return this.bh.push(i),this.Qt().Ah(this),i}Nh(t){const i=this.bh.indexOf(t);-1!==i&&this.bh.splice(i,1),this.Qt().Ah(this)}Wh(){return this.bh}Rr(){return this.Ph}zt(){const t=this.Fh();return null===t?null:{Ft:t.Ft[3],Hh:t.wt}}Fh(){const t=this.Qt().At().ye();if(null===t)return null;const i=t.Uh();return this.Yt.Wr(i,1)}Xs(){return this.Yt}$h(t){const i=this.Yt.Pr(t);return null===i?null:"Bar"===this.Ph||"Candlestick"===this.Ph||"Custom"===this.Ph?{qh:i.Ft[0],Yh:i.Ft[1],jh:i.Ft[2],Kh:i.Ft[3]}:i.Ft[3]}Xh(t){const i=[];Ft(this.kh,Ht,"top",i);const s=this.xh;return null!==s&&s.Vt()?(null===this.yh&&s.xe()&&(this.yh=setTimeout((()=>{this.yh=null,this.Qt().Zh()}),0)),s.Se(),i.unshift(s),i):i}Ws(){const t=[];this.Gh()||t.push(this.Sh),t.push(this.sh,this.er);const i=this.bh.map((t=>t.lr()));return t.push(...i),Ft(this.kh,Ht,"normal",t),t}Jh(){return this.Qh(Ht,"bottom")}ta(t){return this.Qh(Ut,t)}ia(t){return this.Qh($t,t)}sa(t,i){return this.kh.map((s=>s.jn(t,i))).filter((t=>null!==t))}us(){return[this.rr,...this.bh.map((t=>t._r()))]}Fs(t,i){if(i!==this.es&&!this.Gh())return[];const s=[...this.vs];for(const t of this.bh)s.push(t.ur());return this.kh.forEach((t=>{s.push(...t.Fs())})),s}cs(){const t=[];return this.kh.forEach((i=>{t.push(...i.cs())})),t}Mh(t,i){if(void 0!==this.ys.autoscaleInfoProvider){const s=this.ys.autoscaleInfoProvider((()=>{const s=this.na(t,i);return null===s?null:s.Xe()}));return mt.Ze(s)}return this.na(t,i)}ea(){return this.ys.priceFormat.minMove}ra(){return this.ha}Ns(){this.sh.kt();for(const t of this.vs)t.kt();for(const t of this.bh)t.kt();this.er.kt(),this.Sh.kt(),this.xh?.kt(),this.kh.forEach((t=>t.Ns()))}Wt(){return u(super.Wt())}Et(t){if(!(("Line"===this.Ph||"Area"===this.Ph||"Baseline"===this.Ph)&&this.ys.crosshairMarkerVisible))return null;const i=this.Yt.Pr(t);if(null===i)return null;return{gt:i.Ft[3],ft:this.aa(),Ht:this.la(),Ot:this.oa(),Lt:this._a(t)}}Ae(){return this.ys.title}Vt(){return this.ys.visible}ua(t){this.kh.push(new Wt(t,this))}ca(t){this.kh=this.kh.filter((i=>i.oh()!==t))}da(){if("Custom"===this.Ph)return t=>this.sh.fa(t)}pa(){if("Custom"===this.Ph)return t=>this.sh.va(t)}ma(){return this.Yt.qr()}Gh(){return!Z(this.Wt().wa())}na(t,i){if(!v(t)||!v(i)||this.Yt.Ki())return null;const s="Line"===this.Ph||"Area"===this.Ph||"Baseline"===this.Ph||"Histogram"===this.Ph?[3]:[2,1],n=this.Yt.Ur(t,i,s);let e=null!==n?new vt(n.Jr,n.Qr):null,r=null;if("Histogram"===this.Rr()){const t=this.ys.base,i=new vt(t,t);e=null!==e?e.vn(i):i}return this.kh.forEach((s=>{const n=s.Mh(t,i);if(n?.priceRange){const t=new vt(n.priceRange.minValue,n.priceRange.maxValue);e=null!==e?e.vn(t):t}n?.margins&&(r=n.margins)})),new mt(e,r)}aa(){switch(this.Ph){case"Line":case"Area":case"Baseline":return this.ys.crosshairMarkerRadius}return 0}la(){switch(this.Ph){case"Line":case"Area":case"Baseline":{const t=this.ys.crosshairMarkerBorderColor;if(0!==t.length)return t}}return null}oa(){switch(this.Ph){case"Line":case"Area":case"Baseline":return this.ys.crosshairMarkerBorderWidth}return 0}_a(t){switch(this.Ph){case"Line":case"Area":case"Baseline":{const t=this.ys.crosshairMarkerBackgroundColor;if(0!==t.length)return t}}return this.Rh().Dr(t).cr}Th(){switch(this.ys.priceFormat.type){case"custom":this.ha={format:this.ys.priceFormat.formatter};break;case"volume":this.ha=new st(this.ys.priceFormat.precision);break;case"percent":this.ha=new it(this.ys.priceFormat.precision);break;default:{const t=Math.pow(10,this.ys.priceFormat.precision);this.ha=new tt(t,this.ys.priceFormat.minMove*t)}}null!==this.es&&this.es.ga()}Qh(t,i){const s=[];return Ft(this.kh,t,i,s),s}}const jt=[3],Kt=[0,1,2,3];class Xt{constructor(t){this.ys=t}Ma(t,i,s){let n=t;if(0===this.ys.mode)return n;const e=s.Ps(),r=e.zt();if(null===r)return n;const h=e.Nt(t,r),a=s.ba().filter((t=>t instanceof Yt)).reduce(((t,n)=>{if(s.Un(n)||!n.Vt())return t;const e=n.Wt(),r=n.Xs();if(e.Ki()||!r.Te(i))return t;const h=r.Pr(i);if(null===h)return t;const a=c(n.zt()),l=3===this.ys.mode?Kt:jt;return t.concat(l.map((t=>e.Nt(h.Ft[t],a.Ft))))}),[]);if(0===a.length)return n;a.sort(((t,i)=>Math.abs(t-h)-Math.abs(i-h)));const l=a[0];return n=e.Ts(l,r),n}}function Zt(t,i,s){return Math.min(Math.max(t,i),s)}function Gt(t,i,s){return i-t<=s}function Jt(t){const i=Math.ceil(t);return i%2==0?i-1:i}class Qt extends R{constructor(){super(...arguments),this.Yt=null}ht(t){this.Yt=t}et({context:t,bitmapSize:i,horizontalPixelRatio:s,verticalPixelRatio:n}){if(null===this.Yt)return;const e=Math.max(1,Math.floor(s));t.lineWidth=e,function(t,i){t.save(),t.lineWidth%2&&t.translate(.5,.5),i(),t.restore()}(t,(()=>{const r=u(this.Yt);if(r.Sa){t.strokeStyle=r.xa,a(t,r.Ca),t.beginPath();for(const n of r.ya){const r=Math.round(n.ka*s);t.moveTo(r,-e),t.lineTo(r,i.height+e)}t.stroke()}if(r.Pa){t.strokeStyle=r.Ta,a(t,r.Ra),t.beginPath();for(const s of r.Da){const r=Math.round(s.ka*n);t.moveTo(-e,r),t.lineTo(i.width+e,r)}t.stroke()}}))}}class ti{constructor(t){this.Gt=new Qt,this.xt=!0,this.yt=t}kt(){this.xt=!0}Tt(){if(this.xt){const t=this.yt.Qt().N().grid,i={Pa:t.horzLines.visible,Sa:t.vertLines.visible,Ta:t.horzLines.color,xa:t.vertLines.color,Ra:t.horzLines.style,Ca:t.vertLines.style,Da:this.yt.Ps().Va(),ya:(this.yt.Qt().At().Va()||[]).map((t=>({ka:t.coord})))};this.Gt.ht(i),this.xt=!1}return this.Gt}}class ii{constructor(t){this.sh=new ti(t)}lr(){return this.sh}}const si={Ia:4,Ba:1e-4};function ni(t,i){const s=100*(t-i)/i;return i<0?-s:s}function ei(t,i){const s=ni(t.$e(),i),n=ni(t.qe(),i);return new vt(s,n)}function ri(t,i){const s=100*(t-i)/i+100;return i<0?-s:s}function hi(t,i){const s=ri(t.$e(),i),n=ri(t.qe(),i);return new vt(s,n)}function ai(t,i){const s=Math.abs(t);if(s<1e-15)return 0;const n=Math.log10(s+i.Ba)+i.Ia;return t<0?-n:n}function li(t,i){const s=Math.abs(t);if(s<1e-15)return 0;const n=Math.pow(10,s-i.Ia)-i.Ba;return t<0?-n:n}function oi(t,i){if(null===t)return null;const s=ai(t.$e(),i),n=ai(t.qe(),i);return new vt(s,n)}function _i(t,i){if(null===t)return null;const s=li(t.$e(),i),n=li(t.qe(),i);return new vt(s,n)}function ui(t){if(null===t)return si;const i=Math.abs(t.qe()-t.$e());if(i>=1||i<1e-15)return si;const s=Math.ceil(Math.abs(Math.log10(i))),n=si.Ia+s;return{Ia:n,Ba:1/Math.pow(10,n)}}class ci{constructor(t,i){if(this.Aa=t,this.Ea=i,function(t){if(t<0)return!1;for(let i=t;i>1;i/=10)if(i%10!=0)return!1;return!0}(this.Aa))this.za=[2,2.5,2];else{this.za=[];for(let t=this.Aa;1!==t;){if(t%2==0)this.za.push(2),t/=2;else{if(t%5!=0)throw new Error("unexpected base");this.za.push(2,2.5),t/=5}if(this.za.length>100)throw new Error("something wrong with base")}}}La(t,i,s){const n=0===this.Aa?0:1/this.Aa;let e=Math.pow(10,Math.max(0,Math.ceil(Math.log10(t-i)))),r=0,h=this.Ea[0];for(;;){const t=Gt(e,n,1e-14)&&e>n+1e-14,i=Gt(e,s*h,1e-14),a=Gt(e,1,1e-14);if(!(t&&i&&a))break;e/=h,h=this.Ea[++r%this.Ea.length]}if(e<=n+1e-14&&(e=n),e=Math.max(1,e),this.za.length>0&&(a=e,l=1,o=1e-14,Math.abs(a-l)<o))for(r=0,h=this.za[0];Gt(e,s*h,1e-14)&&e>n+1e-14;)e/=h,h=this.za[++r%this.za.length];var a,l,o;return e}}class di{constructor(t,i,s,n){this.Oa=[],this.Yi=t,this.Aa=i,this.Na=s,this.Wa=n}La(t,i){if(t<i)throw new Error("high < low");const s=this.Yi.$t(),n=(t-i)*this.Fa()/s,e=new ci(this.Aa,[2,2.5,2]),r=new ci(this.Aa,[2,2,2.5]),h=new ci(this.Aa,[2.5,2,2]),a=[];return a.push(e.La(t,i,n),r.La(t,i,n),h.La(t,i,n)),function(t){if(t.length<1)throw Error("array is empty");let i=t[0];for(let s=1;s<t.length;++s)t[s]<i&&(i=t[s]);return i}(a)}Ha(){const t=this.Yi,i=t.zt();if(null===i)return void(this.Oa=[]);const s=t.$t(),n=this.Na(s-1,i),e=this.Na(0,i),r=this.Yi.N().entireTextOnly?this.Ua()/2:0,h=r,a=s-1-r,l=Math.max(n,e),o=Math.min(n,e);if(l===o)return void(this.Oa=[]);const _=this.La(l,o);if(this.$a(i,_,l,o,h,a),t.qa()&&this.Ya(_,o,l)){const t=this.Yi.ja();this.Ka(i,_,h,a,t,2*t)}}Va(){return this.Oa}Ua(){return this.Yi.k()}Fa(){return Math.ceil(2.5*this.Ua())}$a(t,i,s,n,e,r){const h=this.Oa,a=this.Yi;let l=s%i;l+=l<0?i:0;const o=s>=n?1:-1;let _=null,u=0;for(let c=s-l;c>n;c-=i){const s=this.Wa(c,t,!0);null!==_&&Math.abs(s-_)<this.Fa()||(s<e||s>r||(u<h.length?(h[u].ka=s,h[u].Xa=a.Za(c)):h.push({ka:s,Xa:a.Za(c)}),u++,_=s,a.Ga()&&(i=this.La(c*o,n))))}h.length=u}Ka(t,i,s,n,e,r){const h=this.Oa,a=this.Ja(t,s,e,r),l=this.Ja(t,n,-r,-e),o=this.Wa(0,t,!0)-this.Wa(i,t,!0);h.length>0&&h[0].ka-a.ka<o/2&&h.shift(),h.length>0&&l.ka-h[h.length-1].ka<o/2&&h.pop(),h.unshift(a),h.push(l)}Ja(t,i,s,n){const e=(s+n)/2,r=this.Na(i+s,t),h=this.Na(i+n,t),a=Math.min(r,h),l=Math.max(r,h),o=Math.max(.1,this.La(l,a)),_=this.Na(i+e,t),u=_-_%o,c=this.Wa(u,t,!0);return{Xa:this.Yi.Za(u),ka:c}}Ya(t,i,s){let n=c(this.Yi.Qe());return this.Yi.Ga()&&(n=_i(n,this.Yi.Qa())),n.$e()-i<t&&s-n.qe()<t}}function fi(t){return t.slice().sort(((t,i)=>u(t.hs())-u(i.hs())))}var pi;!function(t){t[t.Normal=0]="Normal",t[t.Logarithmic=1]="Logarithmic",t[t.Percentage=2]="Percentage",t[t.IndexedTo100=3]="IndexedTo100"}(pi||(pi={}));const vi=new it,mi=new tt(100,1);class wi{constructor(t,i,s,n,e){this.tl=0,this.il=null,this.Ge=null,this.sl=null,this.nl={el:!1,rl:null},this.hl=!1,this.al=0,this.ll=0,this.ol=new d,this._l=new d,this.ul=[],this.cl=null,this.dl=null,this.fl=null,this.pl=null,this.vl=null,this.ha=mi,this.ml=ui(null),this.wl=t,this.ys=i,this.gl=s,this.Ml=n,this.bl=e,this.Sl=new di(this,100,this.xl.bind(this),this.Cl.bind(this))}wa(){return this.wl}N(){return this.ys}hr(t){if(f(this.ys,t),this.ga(),void 0!==t.mode&&this.yl({ie:t.mode}),void 0!==t.scaleMargins){const i=_(t.scaleMargins.top),s=_(t.scaleMargins.bottom);if(i<0||i>1)throw new Error(`Invalid top margin - expect value between 0 and 1, given=${i}`);if(s<0||s>1)throw new Error(`Invalid bottom margin - expect value between 0 and 1, given=${s}`);if(i+s>1)throw new Error(`Invalid margins - sum of margins must be less than 1, given=${i+s}`);this.kl(),this.fl=null}}Pl(){return this.ys.autoScale}Tl(){return this.hl}Ga(){return 1===this.ys.mode}Le(){return 2===this.ys.mode}Rl(){return 3===this.ys.mode}Qa(){return this.ml}ie(){return{sn:this.ys.autoScale,Dl:this.ys.invertScale,ie:this.ys.mode}}yl(t){const i=this.ie();let s=null;void 0!==t.sn&&(this.ys.autoScale=t.sn),void 0!==t.ie&&(this.ys.mode=t.ie,2!==t.ie&&3!==t.ie||(this.ys.autoScale=!0),this.nl.el=!1),1===i.ie&&t.ie!==i.ie&&(!function(t,i){if(null===t)return!1;const s=li(t.$e(),i),n=li(t.qe(),i);return isFinite(s)&&isFinite(n)}(this.Ge,this.ml)?this.ys.autoScale=!0:(s=_i(this.Ge,this.ml),null!==s&&this.Vl(s))),1===t.ie&&t.ie!==i.ie&&(s=oi(this.Ge,this.ml),null!==s&&this.Vl(s));const n=i.ie!==this.ys.mode;n&&(2===i.ie||this.Le())&&this.ga(),n&&(3===i.ie||this.Rl())&&this.ga(),void 0!==t.Dl&&i.Dl!==t.Dl&&(this.ys.invertScale=t.Dl,this.Il()),this._l.p(i,this.ie())}Bl(){return this._l}k(){return this.gl.fontSize}$t(){return this.tl}Al(t){this.tl!==t&&(this.tl=t,this.kl(),this.fl=null)}El(){if(this.il)return this.il;const t=this.$t()-this.zl()-this.Ll();return this.il=t,t}Qe(){return this.Ol(),this.Ge}Vl(t,i){const s=this.Ge;(i||null===s&&null!==t||null!==s&&!s.He(t))&&(this.fl=null,this.Ge=t)}Nl(t){this.Vl(t),this.Wl(null!==t)}Ki(){return this.Ol(),0===this.tl||!this.Ge||this.Ge.Ki()}Fl(t){return this.Dl()?t:this.$t()-1-t}Nt(t,i){return this.Le()?t=ni(t,i):this.Rl()&&(t=ri(t,i)),this.Cl(t,i)}Hl(t,i,s){this.Ol();const n=this.Ll(),e=u(this.Qe()),r=e.$e(),h=e.qe(),a=this.El()-1,l=this.Dl(),o=a/(h-r),_=void 0===s?0:s.from,c=void 0===s?t.length:s.to,d=this.Ul();for(let s=_;s<c;s++){const e=t[s],h=e.gt;if(isNaN(h))continue;let a=h;null!==d&&(a=d(e.gt,i));const _=n+o*(a-r),u=l?_:this.tl-1-_;e.ut=u}}$l(t,i,s){this.Ol();const n=this.Ll(),e=u(this.Qe()),r=e.$e(),h=e.qe(),a=this.El()-1,l=this.Dl(),o=a/(h-r),_=void 0===s?0:s.from,c=void 0===s?t.length:s.to,d=this.Ul();for(let s=_;s<c;s++){const e=t[s];let h=e.qh,a=e.Yh,_=e.jh,u=e.Kh;null!==d&&(h=d(e.qh,i),a=d(e.Yh,i),_=d(e.jh,i),u=d(e.Kh,i));let c=n+o*(h-r),f=l?c:this.tl-1-c;e.ql=f,c=n+o*(a-r),f=l?c:this.tl-1-c,e.Yl=f,c=n+o*(_-r),f=l?c:this.tl-1-c,e.jl=f,c=n+o*(u-r),f=l?c:this.tl-1-c,e.Kl=f}}Ts(t,i){const s=this.xl(t,i);return this.Xl(s,i)}Xl(t,i){let s=t;return this.Le()?s=function(t,i){return i<0&&(t=-t),t/100*i+i}(s,i):this.Rl()&&(s=function(t,i){return t-=100,i<0&&(t=-t),t/100*i+i}(s,i)),s}ba(){return this.ul}Dt(){return this.dl||(this.dl=fi(this.ul)),this.dl}Zl(t){-1===this.ul.indexOf(t)&&(this.ul.push(t),this.ga(),this.Gl())}Jl(t){const i=this.ul.indexOf(t);if(-1===i)throw new Error("source is not attached to scale");this.ul.splice(i,1),0===this.ul.length&&(this.yl({sn:!0}),this.Vl(null)),this.ga(),this.Gl()}zt(){let t=null;for(const i of this.ul){const s=i.zt();null!==s&&((null===t||s.Hh<t.Hh)&&(t=s))}return null===t?null:t.Ft}Dl(){return this.ys.invertScale}Va(){const t=null===this.zt();if(null!==this.fl&&(t||this.fl.Ql===t))return this.fl.Va;this.Sl.Ha();const i=this.Sl.Va();return this.fl={Va:i,Ql:t},this.ol.p(),i}io(){return this.ol}so(t){this.Le()||this.Rl()||null===this.pl&&null===this.sl&&(this.Ki()||(this.pl=this.tl-t,this.sl=u(this.Qe()).Ue()))}no(t){if(this.Le()||this.Rl())return;if(null===this.pl)return;this.yl({sn:!1}),(t=this.tl-t)<0&&(t=0);let i=(this.pl+.2*(this.tl-1))/(t+.2*(this.tl-1));const s=u(this.sl).Ue();i=Math.max(i,.1),s.je(i),this.Vl(s)}eo(){this.Le()||this.Rl()||(this.pl=null,this.sl=null)}ro(t){this.Pl()||null===this.vl&&null===this.sl&&(this.Ki()||(this.vl=t,this.sl=u(this.Qe()).Ue()))}ho(t){if(this.Pl())return;if(null===this.vl)return;const i=u(this.Qe()).Ye()/(this.El()-1);let s=t-this.vl;this.Dl()&&(s*=-1);const n=s*i,e=u(this.sl).Ue();e.Ke(n),this.Vl(e,!0),this.fl=null}ao(){this.Pl()||null!==this.vl&&(this.vl=null,this.sl=null)}ra(){return this.ha||this.ga(),this.ha}Zi(t,i){switch(this.ys.mode){case 2:return this.lo(ni(t,i));case 3:return this.ra().format(ri(t,i));default:return this.nr(t)}}Za(t){switch(this.ys.mode){case 2:return this.lo(t);case 3:return this.ra().format(t);default:return this.nr(t)}}Dh(t){return this.nr(t,u(this.cl).ra())}Vh(t,i){return t=ni(t,i),this.lo(t,vi)}oo(){return this.ul}_o(t){this.nl={rl:t,el:!1}}Ns(){this.ul.forEach((t=>t.Ns()))}qa(){return this.ys.ensureEdgeTickMarksVisible&&this.Pl()}ja(){return this.k()/2}ga(){this.fl=null;let t=1/0;this.cl=null;for(const i of this.ul)i.hs()<t&&(t=i.hs(),this.cl=i);let i=100;null!==this.cl&&(i=Math.round(1/this.cl.ea())),this.ha=mi,this.Le()?(this.ha=vi,i=100):this.Rl()?(this.ha=new tt(100,1),i=100):null!==this.cl&&(this.ha=this.cl.ra()),this.Sl=new di(this,i,this.xl.bind(this),this.Cl.bind(this)),this.Sl.Ha()}Gl(){this.dl=null}Xi(){return this.bl}Wl(t){this.hl=t}zl(){return this.Dl()?this.ys.scaleMargins.bottom*this.$t()+this.ll:this.ys.scaleMargins.top*this.$t()+this.al}Ll(){return this.Dl()?this.ys.scaleMargins.top*this.$t()+this.al:this.ys.scaleMargins.bottom*this.$t()+this.ll}Ol(){this.nl.el||(this.nl.el=!0,this.uo())}kl(){this.il=null}Cl(t,i){if(this.Ol(),this.Ki())return 0;t=this.Ga()&&t?ai(t,this.ml):t;const s=u(this.Qe()),n=this.Ll()+(this.El()-1)*(t-s.$e())/s.Ye();return this.Fl(n)}xl(t,i){if(this.Ol(),this.Ki())return 0;const s=this.Fl(t),n=u(this.Qe()),e=n.$e()+n.Ye()*((s-this.Ll())/(this.El()-1));return this.Ga()?li(e,this.ml):e}Il(){this.fl=null,this.Sl.Ha()}uo(){if(this.Tl()&&!this.Pl())return;const t=this.nl.rl;if(null===t)return;let i=null;const s=this.oo();let n=0,e=0;for(const r of s){if(!r.Vt())continue;const s=r.zt();if(null===s)continue;const h=r.Mh(t.Uh(),t.bi());let a=h&&h.Qe();if(null!==a){switch(this.ys.mode){case 1:a=oi(a,this.ml);break;case 2:a=ei(a,s.Ft);break;case 3:a=hi(a,s.Ft)}if(i=null===i?a:i.vn(u(a)),null!==h){const t=h.tr();null!==t&&(n=Math.max(n,t.above),e=Math.max(e,t.below))}}}if(this.qa()&&(n=Math.max(n,this.ja()),e=Math.max(e,this.ja())),n===this.al&&e===this.ll||(this.al=n,this.ll=e,this.fl=null,this.kl()),null!==i){if(i.$e()===i.qe()){const t=this.cl,s=5*(null===t||this.Le()||this.Rl()?1:t.ea());this.Ga()&&(i=_i(i,this.ml)),i=new vt(i.$e()-s,i.qe()+s),this.Ga()&&(i=oi(i,this.ml))}if(this.Ga()){const t=_i(i,this.ml),s=ui(t);if(r=s,h=this.ml,r.Ia!==h.Ia||r.Ba!==h.Ba){const n=null!==this.sl?_i(this.sl,this.ml):null;this.ml=s,i=oi(t,s),null!==n&&(this.sl=oi(n,s))}}this.Vl(i)}else null===this.Ge&&(this.Vl(new vt(-.5,.5)),this.ml=ui(null));var r,h}Ul(){return this.Le()?ni:this.Rl()?ri:this.Ga()?t=>ai(t,this.ml):null}co(t,i,s){return void 0===i?(void 0===s&&(s=this.ra()),s.format(t)):i(t)}nr(t,i){return this.co(t,this.Ml.priceFormatter,i)}lo(t,i){return this.co(t,this.Ml.percentageFormatter,i)}}function gi(t){return t instanceof Yt}class Mi{constructor(t,i){this.ul=[],this.do=new Map,this.tl=0,this.fo=0,this.po=1e3,this.dl=null,this.vo=new d,this.kh=[],this.uh=t,this.ts=i,this.mo=new ii(this);const s=i.N();this.wo=this.Mo("left",s.leftPriceScale),this.bo=this.Mo("right",s.rightPriceScale),this.wo.Bl().i(this.So.bind(this,this.wo),this),this.bo.Bl().i(this.So.bind(this,this.bo),this),this.xo(s)}xo(t){if(t.leftPriceScale&&this.wo.hr(t.leftPriceScale),t.rightPriceScale&&this.bo.hr(t.rightPriceScale),t.localization&&(this.wo.ga(),this.bo.ga()),t.overlayPriceScales){const i=Array.from(this.do.values());for(const s of i){const i=u(s[0].Wt());i.hr(t.overlayPriceScales),t.localization&&i.ga()}}}Co(t){switch(t){case"left":return this.wo;case"right":return this.bo}return this.do.has(t)?_(this.do.get(t))[0].Wt():null}m(){this.Qt().yo().u(this),this.wo.Bl().u(this),this.bo.Bl().u(this),this.ul.forEach((t=>{t.m&&t.m()})),this.kh=this.kh.filter((t=>{const i=t.oh();return i.detached&&i.detached(),!1})),this.vo.p()}ko(){return this.po}Po(t){this.po=t}Qt(){return this.ts}Qi(){return this.fo}$t(){return this.tl}To(t){this.fo=t,this.Ro()}Al(t){this.tl=t,this.wo.Al(t),this.bo.Al(t),this.ul.forEach((i=>{if(this.Un(i)){const s=i.Wt();null!==s&&s.Al(t)}})),this.Ro()}Do(){return this.ul.filter(gi)}ba(){return this.ul}Un(t){const i=t.Wt();return null===i||this.wo!==i&&this.bo!==i}Zl(t,i,s){this.Vo(t,i,s?t.hs():this.ul.length)}Jl(t,i){const s=this.ul.indexOf(t);o(-1!==s,"removeDataSource: invalid data source"),this.ul.splice(s,1),i||this.ul.forEach(((t,i)=>t.ls(i)));const n=u(t.Wt()).wa();if(this.do.has(n)){const i=_(this.do.get(n)),s=i.indexOf(t);-1!==s&&(i.splice(s,1),0===i.length&&this.do.delete(n))}const e=t.Wt();e&&e.ba().indexOf(t)>=0&&(e.Jl(t),this.Io(e)),this.dl=null}qn(t){return t===this.wo?"left":t===this.bo?"right":"overlay"}Bo(){return this.wo}Ao(){return this.bo}Eo(t,i){t.so(i)}zo(t,i){t.no(i),this.Ro()}Lo(t){t.eo()}Oo(t,i){t.ro(i)}No(t,i){t.ho(i),this.Ro()}Wo(t){t.ao()}Ro(){this.ul.forEach((t=>{t.Ns()}))}Ps(){let t=null;return this.ts.N().rightPriceScale.visible&&0!==this.bo.ba().length?t=this.bo:this.ts.N().leftPriceScale.visible&&0!==this.wo.ba().length?t=this.wo:0!==this.ul.length&&(t=this.ul[0].Wt()),null===t&&(t=this.bo),t}$n(){let t=null;return this.ts.N().rightPriceScale.visible?t=this.bo:this.ts.N().leftPriceScale.visible&&(t=this.wo),t}Io(t){null!==t&&t.Pl()&&this.Fo(t)}Ho(t){const i=this.uh.ye();t.yl({sn:!0}),null!==i&&t._o(i),this.Ro()}Uo(){this.Fo(this.wo),this.Fo(this.bo)}$o(){this.Io(this.wo),this.Io(this.bo),this.ul.forEach((t=>{this.Un(t)&&this.Io(t.Wt())})),this.Ro(),this.ts.ar()}Dt(){return null===this.dl&&(this.dl=fi(this.ul)),this.dl}qo(t,i){i=Zt(i,0,this.ul.length-1);const s=this.ul.indexOf(t);o(-1!==s,"setSeriesOrder: invalid data source"),this.ul.splice(s,1),this.ul.splice(i,0,t),this.ul.forEach(((t,i)=>t.ls(i))),this.dl=null;for(const t of[this.wo,this.bo])t.Gl(),t.ga();this.ts.ar()}It(){return this.Dt().filter(gi)}Yo(){return this.vo}jo(){return this.mo}ua(t){this.kh.push(new At(t))}ca(t){this.kh=this.kh.filter((i=>i.oh()!==t)),t.detached&&t.detached(),this.ts.ar()}Ko(){return this.kh}sa(t,i){return this.kh.map((s=>s.jn(t,i))).filter((t=>null!==t))}Fo(t){const i=t.oo();if(i&&i.length>0&&!this.uh.Ki()){const i=this.uh.ye();null!==i&&t._o(i)}t.Ns()}Vo(t,i,s){let n=this.Co(i);if(null===n&&(n=this.Mo(i,this.ts.N().overlayPriceScales)),this.ul.splice(s,0,t),!Z(i)){const s=this.do.get(i)||[];s.push(t),this.do.set(i,s)}t.ls(s),n.Zl(t),t._s(n),this.Io(n),this.dl=null}So(t,i,s){i.ie!==s.ie&&this.Fo(t)}Mo(t,i){const s={visible:!0,autoScale:!0,...g(i)},n=new wi(t,s,this.ts.N().layout,this.ts.N().localization,this.ts.Xi());return n.Al(this.$t()),n}}function bi(t){return{Xo:t.Xo,Zo:{Kn:t.Go.externalId},Jo:t.Go.cursorStyle}}function Si(t,i,s,n){for(const e of t){const t=e.Tt(n);if(null!==t&&t.jn){const n=t.jn(i,s);if(null!==n)return{Qo:e,Zo:n}}}return null}function xi(t){return void 0!==t.Ws}function Ci(t,i,s){const n=[t,...t.Dt()],e=function(t,i,s){let n,e;for(const a of t){const t=a.sa?.(i,s)??[];for(const i of t)r=i.zOrder,h=n?.zOrder,(!h||"top"===r&&"top"!==h||"normal"===r&&"bottom"===h)&&(n=i,e=a)}var r,h;return n&&e?{Go:n,Xo:e}:null}(n,i,s);if("top"===e?.Go.zOrder)return bi(e);for(const r of n){if(e&&e.Xo===r&&"bottom"!==e.Go.zOrder&&!e.Go.isBackground)return bi(e);if(xi(r)){const n=Si(r.Ws(t),i,s,t);if(null!==n)return{Xo:r,Qo:n.Qo,Zo:n.Zo}}if(e&&e.Xo===r&&"bottom"!==e.Go.zOrder&&e.Go.isBackground)return bi(e)}return e?.Go?bi(e):null}class yi{constructor(t,i,s=50){this.kn=0,this.Pn=1,this.Tn=1,this.Dn=new Map,this.Rn=new Map,this.t_=t,this.i_=i,this.Vn=s}s_(t){const i=t.time,s=this.i_.cacheKey(i),n=this.Dn.get(s);if(void 0!==n)return n.n_;if(this.kn===this.Vn){const t=this.Rn.get(this.Tn);this.Rn.delete(this.Tn),this.Dn.delete(_(t)),this.Tn++,this.kn--}const e=this.t_(t);return this.Dn.set(s,{n_:e,En:this.Pn}),this.Rn.set(this.Pn,s),this.kn++,this.Pn++,e}}class ki{constructor(t,i){o(t<=i,"right should be >= left"),this.e_=t,this.r_=i}Uh(){return this.e_}bi(){return this.r_}h_(){return this.r_-this.e_+1}Te(t){return this.e_<=t&&t<=this.r_}He(t){return this.e_===t.Uh()&&this.r_===t.bi()}}function Pi(t,i){return null===t||null===i?t===i:t.He(i)}class Ti{constructor(){this.a_=new Map,this.Dn=null,this.l_=!1}o_(t){this.l_=t,this.Dn=null}__(t,i){this.u_(i),this.Dn=null;for(let s=i;s<t.length;++s){const i=t[s];let n=this.a_.get(i.timeWeight);void 0===n&&(n=[],this.a_.set(i.timeWeight,n)),n.push({index:s,time:i.time,weight:i.timeWeight,originalTime:i.originalTime})}}c_(t,i,s,n,e){const r=Math.ceil(i/t);return null!==this.Dn&&this.Dn.d_===r&&e===this.Dn.f_&&s===this.Dn.p_||(this.Dn={f_:e,p_:s,Va:this.v_(r,s,n),d_:r}),this.Dn.Va}u_(t){if(0===t)return void this.a_.clear();const i=[];this.a_.forEach(((s,n)=>{t<=s[0].index?i.push(n):s.splice(yt(s,t,(i=>i.index<t)),1/0)}));for(const t of i)this.a_.delete(t)}v_(t,i,s){let n=[];const e=t=>!i||s.has(t.index);for(const i of Array.from(this.a_.keys()).sort(((t,i)=>i-t))){if(!this.a_.get(i))continue;const s=n;n=[];const r=s.length;let h=0;const a=_(this.a_.get(i)),l=a.length;let o=1/0,u=-1/0;for(let i=0;i<l;i++){const l=a[i],_=l.index;for(;h<r;){const t=s[h],i=t.index;if(!(i<_&&e(t))){o=i;break}h++,n.push(t),u=i,o=1/0}if(o-_>=t&&_-u>=t&&e(l))n.push(l),u=_;else if(this.l_)return s}for(;h<r;h++)e(s[h])&&n.push(s[h])}return n}}class Ri{constructor(t){this.m_=t}w_(){return null===this.m_?null:new ki(Math.floor(this.m_.Uh()),Math.ceil(this.m_.bi()))}g_(){return this.m_}static M_(){return new Ri(null)}}function Di(t,i){return t.weight>i.weight?t:i}class Vi{constructor(t,i,s,n){this.fo=0,this.b_=null,this.S_=[],this.vl=null,this.pl=null,this.x_=new Ti,this.C_=new Map,this.y_=Ri.M_(),this.k_=!0,this.P_=new d,this.T_=new d,this.R_=new d,this.D_=null,this.V_=null,this.I_=new Map,this.B_=-1,this.A_=[],this.ys=i,this.Ml=s,this.E_=i.rightOffset,this.z_=i.barSpacing,this.ts=t,this.i_=n,this.L_(),this.x_.o_(i.uniformDistribution),this.O_()}N(){return this.ys}N_(t){f(this.Ml,t),this.W_(),this.L_()}hr(t,i){f(this.ys,t),this.ys.fixLeftEdge&&this.F_(),this.ys.fixRightEdge&&this.H_(),void 0!==t.barSpacing&&this.ts.dn(t.barSpacing),void 0!==t.rightOffset&&this.ts.fn(t.rightOffset),void 0===t.minBarSpacing&&void 0===t.maxBarSpacing||this.ts.dn(t.barSpacing??this.z_),void 0!==t.ignoreWhitespaceIndices&&t.ignoreWhitespaceIndices!==this.ys.ignoreWhitespaceIndices&&this.O_(),this.W_(),this.L_(),this.R_.p()}Rs(t){return this.S_[t]?.time??null}ss(t){return this.S_[t]??null}U_(t,i){if(this.S_.length<1)return null;if(this.i_.key(t)>this.i_.key(this.S_[this.S_.length-1].time))return i?this.S_.length-1:null;const s=yt(this.S_,this.i_.key(t),((t,i)=>this.i_.key(t.time)<i));return this.i_.key(t)<this.i_.key(this.S_[s].time)?i?s:null:s}Ki(){return 0===this.fo||0===this.S_.length||null===this.b_}q_(){return this.S_.length>0}ye(){return this.Y_(),this.y_.w_()}j_(){return this.Y_(),this.y_.g_()}K_(){const t=this.ye();if(null===t)return null;const i={from:t.Uh(),to:t.bi()};return this.X_(i)}X_(t){const i=Math.round(t.from),s=Math.round(t.to),n=u(this.Z_()),e=u(this.G_());return{from:u(this.ss(Math.max(n,i))),to:u(this.ss(Math.min(e,s)))}}J_(t){return{from:u(this.U_(t.from,!0)),to:u(this.U_(t.to,!0))}}Qi(){return this.fo}To(t){if(!isFinite(t)||t<=0)return;if(this.fo===t)return;const i=this.j_(),s=this.fo;if(this.fo=t,this.k_=!0,this.ys.lockVisibleTimeRangeOnResize&&0!==s){const i=this.z_*t/s;this.z_=i}if(this.ys.fixLeftEdge&&null!==i&&i.Uh()<=0){const i=s-t;this.E_-=Math.round(i/this.z_)+1,this.k_=!0}this.Q_(),this.tu()}qt(t){if(this.Ki()||!v(t))return 0;const i=this.iu()+this.E_-t;return this.fo-(i+.5)*this.z_-1}su(t,i){const s=this.iu(),n=void 0===i?0:i.from,e=void 0===i?t.length:i.to;for(let i=n;i<e;i++){const n=t[i].wt,e=s+this.E_-n,r=this.fo-(e+.5)*this.z_-1;t[i]._t=r}}nu(t,i){const s=Math.ceil(this.eu(t));return i&&this.ys.ignoreWhitespaceIndices&&!this.ru(s)?this.hu(s):s}fn(t){this.k_=!0,this.E_=t,this.tu(),this.ts.au(),this.ts.ar()}lu(){return this.z_}dn(t){this.ou(t),this.tu(),this.ts.au(),this.ts.ar()}_u(){return this.E_}Va(){if(this.Ki())return null;if(null!==this.V_)return this.V_;const t=this.z_,i=5*(this.ts.N().layout.fontSize+4)/8*(this.ys.tickMarkMaxCharacterLength||8),s=Math.round(i/t),n=u(this.ye()),e=Math.max(n.Uh(),n.Uh()-s),r=Math.max(n.bi(),n.bi()-s),h=this.x_.c_(t,i,this.ys.ignoreWhitespaceIndices,this.I_,this.B_),a=this.Z_()+s,l=this.G_()-s,o=this.uu(),_=this.ys.fixLeftEdge||o,c=this.ys.fixRightEdge||o;let d=0;for(const t of h){if(!(e<=t.index&&t.index<=r))continue;let s;d<this.A_.length?(s=this.A_[d],s.coord=this.qt(t.index),s.label=this.cu(t),s.weight=t.weight):(s={needAlignCoordinate:!1,coord:this.qt(t.index),label:this.cu(t),weight:t.weight},this.A_.push(s)),this.z_>i/2&&!o?s.needAlignCoordinate=!1:s.needAlignCoordinate=_&&t.index<=a||c&&t.index>=l,d++}return this.A_.length=d,this.V_=this.A_,this.A_}du(){this.k_=!0,this.dn(this.ys.barSpacing),this.fn(this.ys.rightOffset)}fu(t){this.k_=!0,this.b_=t,this.tu(),this.F_()}pu(t,i){const s=this.eu(t),n=this.lu(),e=n+i*(n/10);this.dn(e),this.ys.rightBarStaysOnScroll||this.fn(this._u()+(s-this.eu(t)))}so(t){this.vl&&this.ao(),null===this.pl&&null===this.D_&&(this.Ki()||(this.pl=t,this.vu()))}no(t){if(null===this.D_)return;const i=Zt(this.fo-t,0,this.fo),s=Zt(this.fo-u(this.pl),0,this.fo);0!==i&&0!==s&&this.dn(this.D_.lu*i/s)}eo(){null!==this.pl&&(this.pl=null,this.mu())}ro(t){null===this.vl&&null===this.D_&&(this.Ki()||(this.vl=t,this.vu()))}ho(t){if(null===this.vl)return;const i=(this.vl-t)/this.lu();this.E_=u(this.D_)._u+i,this.k_=!0,this.tu()}ao(){null!==this.vl&&(this.vl=null,this.mu())}wu(){this.gu(this.ys.rightOffset)}gu(t,i=400){if(!isFinite(t))throw new RangeError("offset is required and must be finite number");if(!isFinite(i)||i<=0)throw new RangeError("animationDuration (optional) must be finite positive number");const s=this.E_,n=performance.now();this.ts._n({Mu:t=>(t-n)/i>=1,bu:e=>{const r=(e-n)/i;return r>=1?t:s+(t-s)*r}})}kt(t,i){this.k_=!0,this.S_=t,this.x_.__(t,i),this.tu()}Su(){return this.P_}xu(){return this.T_}Cu(){return this.R_}iu(){return this.b_||0}yu(t){const i=t.h_();this.ou(this.fo/i),this.E_=t.bi()-this.iu(),this.tu(),this.k_=!0,this.ts.au(),this.ts.ar()}ku(){const t=this.Z_(),i=this.G_();null!==t&&null!==i&&this.yu(new ki(t,i+this.ys.rightOffset))}Pu(t){const i=new ki(t.from,t.to);this.yu(i)}ns(t){return void 0!==this.Ml.timeFormatter?this.Ml.timeFormatter(t.originalTime):this.i_.formatHorzItem(t.time)}O_(){if(!this.ys.ignoreWhitespaceIndices)return;this.I_.clear();const t=this.ts.js();for(const i of t)for(const t of i.ma())this.I_.set(t,!0);this.B_++}uu(){const t=this.ts.N().handleScroll,i=this.ts.N().handleScale;return!(t.horzTouchDrag||t.mouseWheel||t.pressedMouseMove||t.vertTouchDrag||i.axisDoubleClickReset.time||i.axisPressedMouseMove.time||i.mouseWheel||i.pinch)}Z_(){return 0===this.S_.length?null:0}G_(){return 0===this.S_.length?null:this.S_.length-1}Tu(t){return(this.fo-1-t)/this.z_}eu(t){const i=this.Tu(t),s=this.iu()+this.E_-i;return Math.round(1e6*s)/1e6}ou(t){const i=this.z_;this.z_=t,this.Q_(),i!==this.z_&&(this.k_=!0,this.Ru())}Y_(){if(!this.k_)return;if(this.k_=!1,this.Ki())return void this.Du(Ri.M_());const t=this.iu(),i=this.fo/this.z_,s=this.E_+t,n=new ki(s-i+1,s);this.Du(new Ri(n))}Q_(){const t=Zt(this.z_,this.Vu(),this.Iu());this.z_!==t&&(this.z_=t,this.k_=!0)}Iu(){return this.ys.maxBarSpacing>0?this.ys.maxBarSpacing:.5*this.fo}Vu(){return this.ys.fixLeftEdge&&this.ys.fixRightEdge&&0!==this.S_.length?this.fo/this.S_.length:this.ys.minBarSpacing}tu(){const t=this.Bu();null!==t&&this.E_<t&&(this.E_=t,this.k_=!0);const i=this.Au();this.E_>i&&(this.E_=i,this.k_=!0)}Bu(){const t=this.Z_(),i=this.b_;if(null===t||null===i)return null;return t-i-1+(this.ys.fixLeftEdge?this.fo/this.z_:Math.min(2,this.S_.length))}Au(){return this.ys.fixRightEdge?0:this.fo/this.z_-Math.min(2,this.S_.length)}vu(){this.D_={lu:this.lu(),_u:this._u()}}mu(){this.D_=null}cu(t){let i=this.C_.get(t.weight);return void 0===i&&(i=new yi((t=>this.Eu(t)),this.i_),this.C_.set(t.weight,i)),i.s_(t)}Eu(t){return this.i_.formatTickmark(t,this.Ml)}Du(t){const i=this.y_;this.y_=t,Pi(i.w_(),this.y_.w_())||this.P_.p(),Pi(i.g_(),this.y_.g_())||this.T_.p(),this.Ru()}Ru(){this.V_=null}W_(){this.Ru(),this.C_.clear()}L_(){this.i_.updateFormatter(this.Ml)}F_(){if(!this.ys.fixLeftEdge)return;const t=this.Z_();if(null===t)return;const i=this.ye();if(null===i)return;const s=i.Uh()-t;if(s<0){const t=this.E_-s-1;this.fn(t)}this.Q_()}H_(){this.tu(),this.Q_()}ru(t){return!this.ys.ignoreWhitespaceIndices||(this.I_.get(t)||!1)}hu(t){const i=function*(t){const i=Math.round(t),s=i<t;let n=1;for(;;)s?(yield i+n,yield i-n):(yield i-n,yield i+n),n++}(t),s=this.G_();for(;s;){const t=i.next().value;if(this.I_.get(t))return t;if(t<0||t>s)break}return t}}var Ii,Bi,Ai,Ei,zi;!function(t){t[t.OnTouchEnd=0]="OnTouchEnd",t[t.OnNextTap=1]="OnNextTap"}(Ii||(Ii={}));class Li{constructor(t,i,s){this.zu=[],this.Lu=[],this.fo=0,this.Ou=null,this.Nu=new d,this.Wu=new d,this.Fu=null,this.Hu=t,this.ys=i,this.i_=s,this.bl=new P(this.ys.layout.colorParsers),this.Uu=new C(this),this.uh=new Vi(this,i.timeScale,this.ys.localization,s),this.Ct=new X(this,i.crosshair),this.$u=new Xt(i.crosshair),this.qu(0),this.zu[0].Po(2e3),this.Yu=this.ju(0),this.Ku=this.ju(1)}Bh(){this.Xu(G.gn())}ar(){this.Xu(G.wn())}Zh(){this.Xu(new G(1))}Ah(t){const i=this.Zu(t);this.Xu(i)}Gu(){return this.Ou}Ju(t){if(this.Ou?.Xo===t?.Xo&&this.Ou?.Zo?.Kn===t?.Zo?.Kn)return;const i=this.Ou;this.Ou=t,null!==i&&this.Ah(i.Xo),null!==t&&t.Xo!==i?.Xo&&this.Ah(t.Xo)}N(){return this.ys}hr(t){f(this.ys,t),this.zu.forEach((i=>i.xo(t))),void 0!==t.timeScale&&this.uh.hr(t.timeScale),void 0!==t.localization&&this.uh.N_(t.localization),(t.leftPriceScale||t.rightPriceScale)&&this.Nu.p(),this.Yu=this.ju(0),this.Ku=this.ju(1),this.Bh()}Qu(t,i,s=0){const n=this.zu[s];if(void 0===n)return;if("left"===t)return f(this.ys,{leftPriceScale:i}),n.xo({leftPriceScale:i}),this.Nu.p(),void this.Bh();if("right"===t)return f(this.ys,{rightPriceScale:i}),n.xo({rightPriceScale:i}),this.Nu.p(),void this.Bh();const e=this.tc(t,s);null!==e&&(e.Wt.hr(i),this.Nu.p())}tc(t,i){const s=this.zu[i];if(void 0===s)return null;const n=s.Co(t);return null!==n?{Us:s,Wt:n}:null}At(){return this.uh}$s(){return this.zu}sc(){return this.Ct}nc(){return this.Wu}ec(t,i){t.Al(i),this.au()}To(t){this.fo=t,this.uh.To(this.fo),this.zu.forEach((i=>i.To(t))),this.au()}rc(t){1!==this.zu.length&&(o(t>=0&&t<this.zu.length,"Invalid pane index"),this.zu.splice(t,1),this.Bh())}hc(t,i){if(this.zu.length<2)return;o(t>=0&&t<this.zu.length,"Invalid pane index");const s=this.zu[t],n=this.zu.reduce(((t,i)=>t+i.ko()),0),e=this.zu.reduce(((t,i)=>t+i.$t()),0),r=e-30*(this.zu.length-1);i=Math.min(r,Math.max(30,i));const h=n/e,a=s.$t();s.Po(i*h);let l=i-a,_=this.zu.length-1;for(const t of this.zu)if(t!==s){const i=Math.min(r,Math.max(30,t.$t()-l/_));l-=t.$t()-i,_-=1;const s=i*h;t.Po(s)}this.Bh()}ac(t,i){o(t>=0&&t<this.zu.length&&i>=0&&i<this.zu.length,"Invalid pane index");const s=this.zu[t],n=this.zu[i];this.zu[t]=n,this.zu[i]=s,this.Bh()}Eo(t,i,s){t.Eo(i,s)}zo(t,i,s){t.zo(i,s),this.Eh(),this.Xu(this.lc(t,2))}Lo(t,i){t.Lo(i),this.Xu(this.lc(t,2))}Oo(t,i,s){i.Pl()||t.Oo(i,s)}No(t,i,s){i.Pl()||(t.No(i,s),this.Eh(),this.Xu(this.lc(t,2)))}Wo(t,i){i.Pl()||(t.Wo(i),this.Xu(this.lc(t,2)))}Ho(t,i){t.Ho(i),this.Xu(this.lc(t,2))}oc(t){this.uh.so(t)}_c(t,i){const s=this.At();if(s.Ki()||0===i)return;const n=s.Qi();t=Math.max(1,Math.min(t,n)),s.pu(t,i),this.au()}uc(t){this.cc(0),this.dc(t),this.fc()}vc(t){this.uh.no(t),this.au()}mc(){this.uh.eo(),this.ar()}cc(t){this.uh.ro(t)}dc(t){this.uh.ho(t),this.au()}fc(){this.uh.ao(),this.ar()}js(){return this.Lu}wc(t,i,s,n,e){this.Ct.Vs(t,i);let r=NaN,h=this.uh.nu(t,!0);const a=this.uh.ye();null!==a&&(h=Math.min(Math.max(a.Uh(),h),a.bi()));const l=n.Ps(),o=l.zt();if(null!==o&&(r=l.Ts(i,o)),r=this.$u.Ma(r,h,n),this.Ct.Es(h,r,n),this.Zh(),!e){const e=Ci(n,t,i);this.Ju(e&&{Xo:e.Xo,Zo:e.Zo,Jo:e.Jo||null}),this.Wu.p(this.Ct.Bt(),{x:t,y:i},s)}}gc(t,i,s){const n=s.Ps(),e=n.zt(),r=n.Nt(t,u(e)),h=this.uh.U_(i,!0),a=this.uh.qt(u(h));this.wc(a,r,null,s,!0)}Mc(t){this.sc().Ls(),this.Zh(),t||this.Wu.p(null,null,null)}Eh(){const t=this.Ct.Us();if(null!==t){const i=this.Ct.Bs(),s=this.Ct.As();this.wc(i,s,null,t)}this.Ct.Ns()}bc(t,i,s){const n=this.uh.Rs(0);void 0!==i&&void 0!==s&&this.uh.kt(i,s);const e=this.uh.Rs(0),r=this.uh.iu(),h=this.uh.ye();if(null!==h&&null!==n&&null!==e){const i=h.Te(r),a=this.i_.key(n)>this.i_.key(e),l=null!==t&&t>r&&!a,o=this.uh.N().allowShiftVisibleRangeOnWhitespaceReplacement,_=i&&(!(void 0===s)||o)&&this.uh.N().shiftVisibleRangeOnNewBar;if(l&&!_){const i=t-r;this.uh.fn(this.uh._u()-i)}}this.uh.fu(t)}Lh(t){null!==t&&t.$o()}Hn(t){if(function(t){return t instanceof Mi}(t))return t;const i=this.zu.find((i=>i.Dt().includes(t)));return void 0===i?null:i}au(){this.zu.forEach((t=>t.$o())),this.Eh()}m(){this.zu.forEach((t=>t.m())),this.zu.length=0,this.ys.localization.priceFormatter=void 0,this.ys.localization.percentageFormatter=void 0,this.ys.localization.timeFormatter=void 0}Sc(){return this.Uu}Yn(){return this.Uu.N()}yo(){return this.Nu}xc(t,i){const s=this.qu(i);this.Cc(t,s),this.Lu.push(t),1===this.Lu.length?this.Bh():this.ar()}yc(t){const i=this.Hn(t),s=this.Lu.indexOf(t);o(-1!==s,"Series not found");const n=u(i);this.Lu.splice(s,1),n.Jl(t),t.m&&t.m(),this.uh.O_(),this.kc(n)}Ih(t,i){const s=u(this.Hn(t));s.Jl(t,!0),s.Zl(t,i,!0)}ku(){const t=G.wn();t.rn(),this.Xu(t)}Pc(t){const i=G.wn();i.ln(t),this.Xu(i)}cn(){const t=G.wn();t.cn(),this.Xu(t)}dn(t){const i=G.wn();i.dn(t),this.Xu(i)}fn(t){const i=G.wn();i.fn(t),this.Xu(i)}_n(t){const i=G.wn();i._n(t),this.Xu(i)}hn(){const t=G.wn();t.hn(),this.Xu(t)}Tc(){return this.ys.rightPriceScale.visible?"right":"left"}Rc(t,i){o(i>=0,"Index should be greater or equal to 0");if(i===this.Dc(t))return;const s=u(this.Hn(t));s.Jl(t);const n=this.qu(i);this.Cc(t,n),0===s.ba().length&&this.kc(s)}Vc(){return this.Ku}$(){return this.Yu}Ut(t){const i=this.Ku,s=this.Yu;if(i===s)return i;if(t=Math.max(0,Math.min(100,Math.round(100*t))),null===this.Fu||this.Fu.mr!==s||this.Fu.wr!==i)this.Fu={mr:s,wr:i,Ic:new Map};else{const i=this.Fu.Ic.get(t);if(void 0!==i)return i}const n=this.bl.tt(s,i,t/100);return this.Fu.Ic.set(t,n),n}Bc(t){return this.zu.indexOf(t)}Xi(){return this.bl}qu(t){if(o(t>=0,"Index should be greater or equal to 0"),(t=Math.min(this.zu.length,t))<this.zu.length)return this.zu[t];const i=new Mi(this.uh,this);this.zu.push(i);const s=G.gn();return s.Qs(t,{tn:0,sn:!0}),this.Xu(s),i}Dc(t){return this.zu.findIndex((i=>i.Do().includes(t)))}lc(t,i){const s=new G(i);if(null!==t){const n=this.zu.indexOf(t);s.Qs(n,{tn:i})}return s}Zu(t,i){return void 0===i&&(i=2),this.lc(this.Hn(t),i)}Xu(t){this.Hu&&this.Hu(t),this.zu.forEach((t=>t.jo().lr().kt()))}Cc(t,i){const s=t.N().priceScaleId,n=void 0!==s?s:this.Tc();i.Zl(t,n),Z(n)||t.hr(t.N())}ju(t){const i=this.ys.layout;return"gradient"===i.background.type?0===t?i.background.topColor:i.background.bottomColor:i.background.color}kc(t){0===t.ba().length&&this.zu.length>1&&(this.zu.splice(this.Bc(t),1),this.Bh())}}function Oi(t){return!p(t)&&!m(t)}function Ni(t){return p(t)}!function(t){t[t.Disabled=0]="Disabled",t[t.Continuous=1]="Continuous",t[t.OnDataUpdate=2]="OnDataUpdate"}(Bi||(Bi={})),function(t){t[t.LastBar=0]="LastBar",t[t.LastVisible=1]="LastVisible"}(Ai||(Ai={})),function(t){t.Solid="solid",t.VerticalGradient="gradient"}(Ei||(Ei={})),function(t){t[t.Year=0]="Year",t[t.Month=1]="Month",t[t.DayOfMonth=2]="DayOfMonth",t[t.Time=3]="Time",t[t.TimeWithSeconds=4]="TimeWithSeconds"}(zi||(zi={}));const Wi=t=>t.getUTCFullYear();function Fi(t,i,s){return i.replace(/yyyy/g,(t=>Q(Wi(t),4))(t)).replace(/yy/g,(t=>Q(Wi(t)%100,2))(t)).replace(/MMMM/g,((t,i)=>new Date(t.getUTCFullYear(),t.getUTCMonth(),1).toLocaleString(i,{month:"long"}))(t,s)).replace(/MMM/g,((t,i)=>new Date(t.getUTCFullYear(),t.getUTCMonth(),1).toLocaleString(i,{month:"short"}))(t,s)).replace(/MM/g,(t=>Q((t=>t.getUTCMonth()+1)(t),2))(t)).replace(/dd/g,(t=>Q((t=>t.getUTCDate())(t),2))(t))}class Hi{constructor(t="yyyy-MM-dd",i="default"){this.Ac=t,this.Ec=i}s_(t){return Fi(t,this.Ac,this.Ec)}}class Ui{constructor(t){this.zc=t||"%h:%m:%s"}s_(t){return this.zc.replace("%h",Q(t.getUTCHours(),2)).replace("%m",Q(t.getUTCMinutes(),2)).replace("%s",Q(t.getUTCSeconds(),2))}}const $i={Lc:"yyyy-MM-dd",Oc:"%h:%m:%s",Nc:" ",Wc:"default"};class qi{constructor(t={}){const i={...$i,...t};this.Fc=new Hi(i.Lc,i.Wc),this.Hc=new Ui(i.Oc),this.Uc=i.Nc}s_(t){return`${this.Fc.s_(t)}${this.Uc}${this.Hc.s_(t)}`}}function Yi(t){return 60*t*60*1e3}function ji(t){return 60*t*1e3}const Ki=[{$c:(Xi=1,1e3*Xi),qc:10},{$c:ji(1),qc:20},{$c:ji(5),qc:21},{$c:ji(30),qc:22},{$c:Yi(1),qc:30},{$c:Yi(3),qc:31},{$c:Yi(6),qc:32},{$c:Yi(12),qc:33}];var Xi;function Zi(t,i){if(t.getUTCFullYear()!==i.getUTCFullYear())return 70;if(t.getUTCMonth()!==i.getUTCMonth())return 60;if(t.getUTCDate()!==i.getUTCDate())return 50;for(let s=Ki.length-1;s>=0;--s)if(Math.floor(i.getTime()/Ki[s].$c)!==Math.floor(t.getTime()/Ki[s].$c))return Ki[s].qc;return 0}function Gi(t){let i=t;if(m(t)&&(i=Qi(t)),!Oi(i))throw new Error("time must be of type BusinessDay");const s=new Date(Date.UTC(i.year,i.month-1,i.day,0,0,0,0));return{Yc:Math.round(s.getTime()/1e3),jc:i}}function Ji(t){if(!Ni(t))throw new Error("time must be of type isUTCTimestamp");return{Yc:t}}function Qi(t){const i=new Date(t);if(isNaN(i.getTime()))throw new Error(`Invalid date string=${t}, expected format=yyyy-mm-dd`);return{day:i.getUTCDate(),month:i.getUTCMonth()+1,year:i.getUTCFullYear()}}function ts(t){m(t.time)&&(t.time=Qi(t.time))}class is{options(){return this.ys}setOptions(t){this.ys=t,this.updateFormatter(t.localization)}preprocessData(t){Array.isArray(t)?function(t){t.forEach(ts)}(t):ts(t)}createConverterToInternalObj(t){return u(function(t){return 0===t.length?null:Oi(t[0].time)||m(t[0].time)?Gi:Ji}(t))}key(t){return"object"==typeof t&&"Yc"in t?t.Yc:this.key(this.convertHorzItemToInternal(t))}cacheKey(t){const i=t;return void 0===i.jc?new Date(1e3*i.Yc).getTime():new Date(Date.UTC(i.jc.year,i.jc.month-1,i.jc.day)).getTime()}convertHorzItemToInternal(t){return Ni(i=t)?Ji(i):Oi(i)?Gi(i):Gi(Qi(i));var i}updateFormatter(t){if(!this.ys)return;const i=t.dateFormat;this.ys.timeScale.timeVisible?this.Kc=new qi({Lc:i,Oc:this.ys.timeScale.secondsVisible?"%h:%m:%s":"%h:%m",Nc:"   ",Wc:t.locale}):this.Kc=new Hi(i,t.locale)}formatHorzItem(t){const i=t;return this.Kc.s_(new Date(1e3*i.Yc))}formatTickmark(t,i){const s=function(t,i,s){switch(t){case 0:case 10:return i?s?4:3:2;case 20:case 21:case 22:case 30:case 31:case 32:case 33:return i?3:2;case 50:return 2;case 60:return 1;case 70:return 0}}(t.weight,this.ys.timeScale.timeVisible,this.ys.timeScale.secondsVisible),n=this.ys.timeScale;if(void 0!==n.tickMarkFormatter){const e=n.tickMarkFormatter(t.originalTime,s,i.locale);if(null!==e)return e}return function(t,i,s){const n={};switch(i){case 0:n.year="numeric";break;case 1:n.month="short";break;case 2:n.day="numeric";break;case 3:n.hour12=!1,n.hour="2-digit",n.minute="2-digit";break;case 4:n.hour12=!1,n.hour="2-digit",n.minute="2-digit",n.second="2-digit"}const e=void 0===t.jc?new Date(1e3*t.Yc):new Date(Date.UTC(t.jc.year,t.jc.month-1,t.jc.day));return new Date(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds()).toLocaleString(s,n)}(t.time,s,i.locale)}maxTickMarkWeight(t){let i=t.reduce(Di,t[0]).weight;return i>30&&i<50&&(i=30),i}fillWeightsForPoints(t,i){!function(t,i=0){if(0===t.length)return;let s=0===i?null:t[i-1].time.Yc,n=null!==s?new Date(1e3*s):null,e=0;for(let r=i;r<t.length;++r){const i=t[r],h=new Date(1e3*i.time.Yc);null!==n&&(i.timeWeight=Zi(h,n)),e+=i.time.Yc-(s||i.time.Yc),s=i.time.Yc,n=h}if(0===i&&t.length>1){const i=Math.ceil(e/(t.length-1)),s=new Date(1e3*(t[0].time.Yc-i));t[0].timeWeight=Zi(new Date(1e3*t[0].time.Yc),s)}}(t,i)}static Xc(t){return f({localization:{dateFormat:"dd MMM 'yy"}},t??{})}}const ss="undefined"!=typeof window;function ns(){return!!ss&&window.navigator.userAgent.toLowerCase().indexOf("firefox")>-1}function es(){return!!ss&&/iPhone|iPad|iPod/.test(window.navigator.platform)}function rs(t){return t+t%2}function hs(t){ss&&void 0!==window.chrome&&t.addEventListener("mousedown",(t=>{if(1===t.button)return t.preventDefault(),!1}))}class as{constructor(t,i,s){this.Zc=0,this.Gc=null,this.Jc={_t:Number.NEGATIVE_INFINITY,ut:Number.POSITIVE_INFINITY},this.Qc=0,this.td=null,this.sd={_t:Number.NEGATIVE_INFINITY,ut:Number.POSITIVE_INFINITY},this.nd=null,this.ed=!1,this.rd=null,this.hd=null,this.ad=!1,this.ld=!1,this.od=!1,this._d=null,this.ud=null,this.dd=null,this.fd=null,this.pd=null,this.vd=null,this.md=null,this.wd=0,this.gd=!1,this.Md=!1,this.bd=!1,this.Sd=0,this.xd=null,this.Cd=!es(),this.yd=t=>{this.kd(t)},this.Pd=t=>{if(this.Td(t)){const i=this.Rd(t);if(++this.Qc,this.td&&this.Qc>1){const{Dd:s}=this.Vd(_s(t),this.sd);s<30&&!this.od&&this.Id(i,this.Ad.Bd),this.Ed()}}else{const i=this.Rd(t);if(++this.Zc,this.Gc&&this.Zc>1){const{Dd:s}=this.Vd(_s(t),this.Jc);s<5&&!this.ld&&this.zd(i,this.Ad.Ld),this.Od()}}},this.Nd=t,this.Ad=i,this.ys=s,this.Wd()}m(){null!==this._d&&(this._d(),this._d=null),null!==this.ud&&(this.ud(),this.ud=null),null!==this.fd&&(this.fd(),this.fd=null),null!==this.pd&&(this.pd(),this.pd=null),null!==this.vd&&(this.vd(),this.vd=null),null!==this.dd&&(this.dd(),this.dd=null),this.Fd(),this.Od()}Hd(t){this.fd&&this.fd();const i=this.Ud.bind(this);if(this.fd=()=>{this.Nd.removeEventListener("mousemove",i)},this.Nd.addEventListener("mousemove",i),this.Td(t))return;const s=this.Rd(t);this.zd(s,this.Ad.$d),this.Cd=!0}Od(){null!==this.Gc&&clearTimeout(this.Gc),this.Zc=0,this.Gc=null,this.Jc={_t:Number.NEGATIVE_INFINITY,ut:Number.POSITIVE_INFINITY}}Ed(){null!==this.td&&clearTimeout(this.td),this.Qc=0,this.td=null,this.sd={_t:Number.NEGATIVE_INFINITY,ut:Number.POSITIVE_INFINITY}}Ud(t){if(this.bd||null!==this.hd)return;if(this.Td(t))return;const i=this.Rd(t);this.zd(i,this.Ad.qd),this.Cd=!0}Yd(t){const i=cs(t.changedTouches,u(this.xd));if(null===i)return;if(this.Sd=us(t),null!==this.md)return;if(this.Md)return;this.gd=!0;const s=this.Vd(_s(i),u(this.hd)),{jd:n,Kd:e,Dd:r}=s;if(this.ad||!(r<5)){if(!this.ad){const t=.5*n,i=e>=t&&!this.ys.Xd(),s=t>e&&!this.ys.Zd();i||s||(this.Md=!0),this.ad=!0,this.od=!0,this.Fd(),this.Ed()}if(!this.Md){const s=this.Rd(t,i);this.Id(s,this.Ad.Gd),os(t)}}}Jd(t){if(0!==t.button)return;const i=this.Vd(_s(t),u(this.rd)),{Dd:s}=i;if(s>=5&&(this.ld=!0,this.Od()),this.ld){const i=this.Rd(t);this.zd(i,this.Ad.Qd)}}Vd(t,i){const s=Math.abs(i._t-t._t),n=Math.abs(i.ut-t.ut);return{jd:s,Kd:n,Dd:s+n}}tf(t){let i=cs(t.changedTouches,u(this.xd));if(null===i&&0===t.touches.length&&(i=t.changedTouches[0]),null===i)return;this.xd=null,this.Sd=us(t),this.Fd(),this.hd=null,this.vd&&(this.vd(),this.vd=null);const s=this.Rd(t,i);if(this.Id(s,this.Ad.if),++this.Qc,this.td&&this.Qc>1){const{Dd:t}=this.Vd(_s(i),this.sd);t<30&&!this.od&&this.Id(s,this.Ad.Bd),this.Ed()}else this.od||(this.Id(s,this.Ad.sf),this.Ad.sf&&os(t));0===this.Qc&&os(t),0===t.touches.length&&this.ed&&(this.ed=!1,os(t))}kd(t){if(0!==t.button)return;const i=this.Rd(t);if(this.rd=null,this.bd=!1,this.pd&&(this.pd(),this.pd=null),ns()){this.Nd.ownerDocument.documentElement.removeEventListener("mouseleave",this.yd)}if(!this.Td(t))if(this.zd(i,this.Ad.nf),++this.Zc,this.Gc&&this.Zc>1){const{Dd:s}=this.Vd(_s(t),this.Jc);s<5&&!this.ld&&this.zd(i,this.Ad.Ld),this.Od()}else this.ld||this.zd(i,this.Ad.ef)}Fd(){null!==this.nd&&(clearTimeout(this.nd),this.nd=null)}rf(t){if(null!==this.xd)return;const i=t.changedTouches[0];this.xd=i.identifier,this.Sd=us(t);const s=this.Nd.ownerDocument.documentElement;this.od=!1,this.ad=!1,this.Md=!1,this.hd=_s(i),this.vd&&(this.vd(),this.vd=null);{const i=this.Yd.bind(this),n=this.tf.bind(this);this.vd=()=>{s.removeEventListener("touchmove",i),s.removeEventListener("touchend",n)},s.addEventListener("touchmove",i,{passive:!1}),s.addEventListener("touchend",n,{passive:!1}),this.Fd(),this.nd=setTimeout(this.hf.bind(this,t),240)}const n=this.Rd(t,i);this.Id(n,this.Ad.af),this.td||(this.Qc=0,this.td=setTimeout(this.Ed.bind(this),500),this.sd=_s(i))}lf(t){if(0!==t.button)return;const i=this.Nd.ownerDocument.documentElement;ns()&&i.addEventListener("mouseleave",this.yd),this.ld=!1,this.rd=_s(t),this.pd&&(this.pd(),this.pd=null);{const t=this.Jd.bind(this),s=this.kd.bind(this);this.pd=()=>{i.removeEventListener("mousemove",t),i.removeEventListener("mouseup",s)},i.addEventListener("mousemove",t),i.addEventListener("mouseup",s)}if(this.bd=!0,this.Td(t))return;const s=this.Rd(t);this.zd(s,this.Ad._f),this.Gc||(this.Zc=0,this.Gc=setTimeout(this.Od.bind(this),500),this.Jc=_s(t))}Wd(){this.Nd.addEventListener("mouseenter",this.Hd.bind(this)),this.Nd.addEventListener("touchcancel",this.Fd.bind(this));{const t=this.Nd.ownerDocument,i=t=>{this.Ad.uf&&(t.composed&&this.Nd.contains(t.composedPath()[0])||t.target&&this.Nd.contains(t.target)||this.Ad.uf())};this.ud=()=>{t.removeEventListener("touchstart",i)},this._d=()=>{t.removeEventListener("mousedown",i)},t.addEventListener("mousedown",i),t.addEventListener("touchstart",i,{passive:!0})}es()&&(this.dd=()=>{this.Nd.removeEventListener("dblclick",this.Pd)},this.Nd.addEventListener("dblclick",this.Pd)),this.Nd.addEventListener("mouseleave",this.cf.bind(this)),this.Nd.addEventListener("touchstart",this.rf.bind(this),{passive:!0}),hs(this.Nd),this.Nd.addEventListener("mousedown",this.lf.bind(this)),this.df(),this.Nd.addEventListener("touchmove",(()=>{}),{passive:!1})}df(){void 0===this.Ad.ff&&void 0===this.Ad.pf&&void 0===this.Ad.vf||(this.Nd.addEventListener("touchstart",(t=>this.mf(t.touches)),{passive:!0}),this.Nd.addEventListener("touchmove",(t=>{if(2===t.touches.length&&null!==this.md&&void 0!==this.Ad.pf){const i=ls(t.touches[0],t.touches[1])/this.wd;this.Ad.pf(this.md,i),os(t)}}),{passive:!1}),this.Nd.addEventListener("touchend",(t=>{this.mf(t.touches)})))}mf(t){1===t.length&&(this.gd=!1),2!==t.length||this.gd||this.ed?this.wf():this.gf(t)}gf(t){const i=this.Nd.getBoundingClientRect()||{left:0,top:0};this.md={_t:(t[0].clientX-i.left+(t[1].clientX-i.left))/2,ut:(t[0].clientY-i.top+(t[1].clientY-i.top))/2},this.wd=ls(t[0],t[1]),void 0!==this.Ad.ff&&this.Ad.ff(),this.Fd()}wf(){null!==this.md&&(this.md=null,void 0!==this.Ad.vf&&this.Ad.vf())}cf(t){if(this.fd&&this.fd(),this.Td(t))return;if(!this.Cd)return;const i=this.Rd(t);this.zd(i,this.Ad.Mf),this.Cd=!es()}hf(t){const i=cs(t.touches,u(this.xd));if(null===i)return;const s=this.Rd(t,i);this.Id(s,this.Ad.bf),this.od=!0,this.ed=!0}Td(t){return t.sourceCapabilities&&void 0!==t.sourceCapabilities.firesTouchEvents?t.sourceCapabilities.firesTouchEvents:us(t)<this.Sd+500}Id(t,i){i&&i.call(this.Ad,t)}zd(t,i){i&&i.call(this.Ad,t)}Rd(t,i){const s=i||t,n=this.Nd.getBoundingClientRect()||{left:0,top:0};return{clientX:s.clientX,clientY:s.clientY,pageX:s.pageX,pageY:s.pageY,screenX:s.screenX,screenY:s.screenY,localX:s.clientX-n.left,localY:s.clientY-n.top,ctrlKey:t.ctrlKey,altKey:t.altKey,shiftKey:t.shiftKey,metaKey:t.metaKey,Sf:!t.type.startsWith("mouse")&&"contextmenu"!==t.type&&"click"!==t.type,xf:t.type,Cf:s.target,Qo:t.view,yf:()=>{"touchstart"!==t.type&&os(t)}}}}function ls(t,i){const s=t.clientX-i.clientX,n=t.clientY-i.clientY;return Math.sqrt(s*s+n*n)}function os(t){t.cancelable&&t.preventDefault()}function _s(t){return{_t:t.pageX,ut:t.pageY}}function us(t){return t.timeStamp||performance.now()}function cs(t,i){for(let s=0;s<t.length;++s)if(t[s].identifier===i)return t[s];return null}class ds{constructor(t,i,s){this.kf=null,this.Pf=null,this.Tf=!0,this.Rf=null,this.Df=t,this.Vf=t.If()[i],this.Bf=t.If()[s],this.Af=document.createElement("tr"),this.Af.style.height="1px",this.Ef=document.createElement("td"),this.Ef.style.position="relative",this.Ef.style.padding="0",this.Ef.style.margin="0",this.Ef.setAttribute("colspan","3"),this.zf(),this.Af.appendChild(this.Ef),this.Tf=this.Df.N().layout.panes.enableResize,this.Tf?this.Lf():(this.kf=null,this.Pf=null)}m(){null!==this.Pf&&this.Pf.m()}Of(){return this.Af}Nf(){return t({width:this.Vf.Nf().width,height:1})}Wf(){return t({width:this.Vf.Wf().width,height:1*window.devicePixelRatio})}Ff(t,i,s){const n=this.Wf();t.fillStyle=this.Df.N().layout.panes.separatorColor,t.fillRect(i,s,n.width,n.height)}kt(){this.zf(),this.Df.N().layout.panes.enableResize!==this.Tf&&(this.Tf=this.Df.N().layout.panes.enableResize,this.Tf?this.Lf():(null!==this.kf&&(this.Ef.removeChild(this.kf.Hf),this.Ef.removeChild(this.kf.Uf),this.kf=null),null!==this.Pf&&(this.Pf.m(),this.Pf=null)))}Lf(){const t=document.createElement("div"),i=t.style;i.position="fixed",i.display="none",i.zIndex="49",i.top="0",i.left="0",i.width="100%",i.height="100%",i.cursor="row-resize",this.Ef.appendChild(t);const s=document.createElement("div"),n=s.style;n.position="absolute",n.zIndex="50",n.top="-4px",n.height="9px",n.width="100%",n.backgroundColor="",n.cursor="row-resize",this.Ef.appendChild(s);const e={$d:this.$f.bind(this),Mf:this.qf.bind(this),_f:this.Yf.bind(this),af:this.Yf.bind(this),Qd:this.jf.bind(this),Gd:this.jf.bind(this),nf:this.Kf.bind(this),if:this.Kf.bind(this)};this.Pf=new as(s,e,{Xd:()=>!1,Zd:()=>!0}),this.kf={Uf:s,Hf:t}}zf(){this.Ef.style.background=this.Df.N().layout.panes.separatorColor}$f(t){null!==this.kf&&(this.kf.Uf.style.backgroundColor=this.Df.N().layout.panes.separatorHoverColor)}qf(t){null!==this.kf&&null===this.Rf&&(this.kf.Uf.style.backgroundColor="")}Yf(t){if(null===this.kf)return;const i=this.Vf.Xf().ko()+this.Bf.Xf().ko(),s=i/(this.Vf.Nf().height+this.Bf.Nf().height),n=30*s;i<=2*n||(this.Rf={Zf:t.pageY,Gf:this.Vf.Xf().ko(),Jf:i-n,Qf:i,tp:s,ip:n},this.kf.Hf.style.display="block")}jf(t){const i=this.Rf;if(null===i)return;const s=(t.pageY-i.Zf)*i.tp,n=Zt(i.Gf+s,i.ip,i.Jf);this.Vf.Xf().Po(n),this.Bf.Xf().Po(i.Qf-n),this.Df.Qt().Bh()}Kf(t){null!==this.Rf&&null!==this.kf&&(this.Rf=null,this.kf.Hf.style.display="none")}}function fs(t,i){return t.sp-i.sp}function ps(t,i,s){const n=(t.sp-i.sp)/(t.wt-i.wt);return Math.sign(n)*Math.min(Math.abs(n),s)}class vs{constructor(t,i,s,n){this.np=null,this.ep=null,this.rp=null,this.hp=null,this.ap=null,this.lp=0,this.op=0,this._p=t,this.up=i,this.cp=s,this.Mn=n}dp(t,i){if(null!==this.np){if(this.np.wt===i)return void(this.np.sp=t);if(Math.abs(this.np.sp-t)<this.Mn)return}this.hp=this.rp,this.rp=this.ep,this.ep=this.np,this.np={wt:i,sp:t}}le(t,i){if(null===this.np||null===this.ep)return;if(i-this.np.wt>50)return;let s=0;const n=ps(this.np,this.ep,this.up),e=fs(this.np,this.ep),r=[n],h=[e];if(s+=e,null!==this.rp){const t=ps(this.ep,this.rp,this.up);if(Math.sign(t)===Math.sign(n)){const i=fs(this.ep,this.rp);if(r.push(t),h.push(i),s+=i,null!==this.hp){const t=ps(this.rp,this.hp,this.up);if(Math.sign(t)===Math.sign(n)){const i=fs(this.rp,this.hp);r.push(t),h.push(i),s+=i}}}}let a=0;for(let t=0;t<r.length;++t)a+=h[t]/s*r[t];Math.abs(a)<this._p||(this.ap={sp:t,wt:i},this.op=a,this.lp=function(t,i){const s=Math.log(i);return Math.log(1*s/-t)/s}(Math.abs(a),this.cp))}bu(t){const i=u(this.ap),s=t-i.wt;return i.sp+this.op*(Math.pow(this.cp,s)-1)/Math.log(this.cp)}Mu(t){return null===this.ap||this.fp(t)===this.lp}fp(t){const i=t-u(this.ap).wt;return Math.min(i,this.lp)}}class ms{constructor(t,i){this.pp=void 0,this.vp=void 0,this.mp=void 0,this.ps=!1,this.wp=t,this.gp=i,this.Mp()}kt(){this.Mp()}bp(){this.pp&&this.wp.removeChild(this.pp),this.vp&&this.wp.removeChild(this.vp),this.pp=void 0,this.vp=void 0}Sp(){return this.ps!==this.xp()||this.mp!==this.Cp()}Cp(){return this.gp.Qt().Xi().J(this.gp.N().layout.textColor)>160?"dark":"light"}xp(){return this.gp.N().layout.attributionLogo}yp(){const t=new URL(location.href);return t.hostname?"&utm_source="+t.hostname+t.pathname:""}Mp(){this.Sp()&&(this.bp(),this.ps=this.xp(),this.ps&&(this.mp=this.Cp(),this.vp=document.createElement("style"),this.vp.innerText="a#tv-attr-logo{--fill:#131722;--stroke:#fff;position:absolute;left:10px;bottom:10px;height:19px;width:35px;margin:0;padding:0;border:0;z-index:3;}a#tv-attr-logo[data-dark]{--fill:#D1D4DC;--stroke:#131722;}",this.pp=document.createElement("a"),this.pp.href=`https://www.tradingview.com/?utm_medium=lwc-link&utm_campaign=lwc-chart${this.yp()}`,this.pp.title="Charting by TradingView",this.pp.id="tv-attr-logo",this.pp.target="_blank",this.pp.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" width="35" height="19" fill="none"><g fill-rule="evenodd" clip-path="url(#a)" clip-rule="evenodd"><path fill="var(--stroke)" d="M2 0H0v10h6v9h21.4l.5-1.3 6-15 1-2.7H23.7l-.5 1.3-.2.6a5 5 0 0 0-7-.9V0H2Zm20 17h4l5.2-13 .8-2h-7l-1 2.5-.2.5-1.5 3.8-.3.7V17Zm-.8-10a3 3 0 0 0 .7-2.7A3 3 0 1 0 16.8 7h4.4ZM14 7V2H2v6h6v9h4V7h2Z"/><path fill="var(--fill)" d="M14 2H2v6h6v9h6V2Zm12 15h-7l6-15h7l-6 15Zm-7-9a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/></g><defs><clipPath id="a"><path fill="var(--stroke)" d="M0 0h35v19H0z"/></clipPath></defs></svg>',this.pp.toggleAttribute("data-dark","dark"===this.mp),this.wp.appendChild(this.vp),this.wp.appendChild(this.pp)))}}function ws(t,s){const n=u(t.ownerDocument).createElement("canvas");t.appendChild(n);const e=i(n,{type:"device-pixel-content-box",options:{allowResizeObserver:!0},transform:(t,i)=>({width:Math.max(t.width,i.width),height:Math.max(t.height,i.height)})});return e.resizeCanvasElement(s),e}function gs(t){t.width=1,t.height=1,t.getContext("2d")?.clearRect(0,0,1,1)}function Ms(t,i,s,n){t.ih&&t.ih(i,s,n)}function bs(t,i,s,n){t.nt(i,s,n)}function Ss(t,i,s,n){const e=t(s,n);for(const t of e){const s=t.Tt(n);null!==s&&i(s)}}function xs(t,i){return s=>{if(!function(t){return void 0!==t.Wt}(s))return[];return(s.Wt()?.wa()??"")!==i?[]:s.ta?.(t)??[]}}function Cs(t,i,s,n){if(!t.length)return;let e=0;const r=t[0].$t(n,!0);let h=1===i?s/2-(t[0].Wi()-r/2):t[0].Wi()-r/2-s/2;h=Math.max(0,h);for(let r=1;r<t.length;r++){const a=t[r],l=t[r-1],o=l.$t(n,!1),_=a.Wi(),u=l.Wi();if(1===i?_>u-o:_<u+o){const n=u-o*i;a.Fi(n);const r=n-i*o/2;if((1===i?r<0:r>s)&&h>0){const n=1===i?-1-r:r-s,a=Math.min(n,h);for(let s=e;s<t.length;s++)t[s].Fi(t[s].Wi()+i*a);h-=a}}else e=r,h=1===i?u-o-_:_-(u+o)}}class ys{constructor(i,s,n,e){this.Yi=null,this.kp=null,this.Pp=!1,this.Tp=new et(200),this.Rp=null,this.Dp=0,this.Vp=!1,this.Ip=()=>{this.Vp||this.yt.Bp().Qt().ar()},this.Ap=()=>{this.Vp||this.yt.Bp().Qt().ar()},this.yt=i,this.ys=s,this.gl=s.layout,this.Uu=n,this.Ep="left"===e,this.zp=xs("normal",e),this.Lp=xs("top",e),this.Op=xs("bottom",e),this.Ef=document.createElement("div"),this.Ef.style.height="100%",this.Ef.style.overflow="hidden",this.Ef.style.width="25px",this.Ef.style.left="0",this.Ef.style.position="relative",this.Np=ws(this.Ef,t({width:16,height:16})),this.Np.subscribeSuggestedBitmapSizeChanged(this.Ip);const r=this.Np.canvasElement;r.style.position="absolute",r.style.zIndex="1",r.style.left="0",r.style.top="0",this.Wp=ws(this.Ef,t({width:16,height:16})),this.Wp.subscribeSuggestedBitmapSizeChanged(this.Ap);const h=this.Wp.canvasElement;h.style.position="absolute",h.style.zIndex="2",h.style.left="0",h.style.top="0";const a={_f:this.Yf.bind(this),af:this.Yf.bind(this),Qd:this.jf.bind(this),Gd:this.jf.bind(this),uf:this.Fp.bind(this),nf:this.Kf.bind(this),if:this.Kf.bind(this),Ld:this.Hp.bind(this),Bd:this.Hp.bind(this),$d:this.Up.bind(this),Mf:this.qf.bind(this)};this.Pf=new as(this.Wp.canvasElement,a,{Xd:()=>!this.ys.handleScroll.vertTouchDrag,Zd:()=>!0})}m(){this.Pf.m(),this.Wp.unsubscribeSuggestedBitmapSizeChanged(this.Ap),gs(this.Wp.canvasElement),this.Wp.dispose(),this.Np.unsubscribeSuggestedBitmapSizeChanged(this.Ip),gs(this.Np.canvasElement),this.Np.dispose(),null!==this.Yi&&this.Yi.io().u(this),this.Yi=null}Of(){return this.Ef}k(){return this.gl.fontSize}$p(){const t=this.Uu.N();return this.Rp!==t.P&&(this.Tp.In(),this.Rp=t.P),t}qp(){if(null===this.Yi)return 0;let t=0;const i=this.$p(),s=u(this.Np.canvasElement.getContext("2d",{colorSpace:this.yt.Bp().N().layout.colorSpace}));s.save();const n=this.Yi.Va();s.font=this.Yp(),n.length>0&&(t=Math.max(this.Tp.Vi(s,n[0].Xa),this.Tp.Vi(s,n[n.length-1].Xa)));const e=this.jp();for(let i=e.length;i--;){const n=this.Tp.Vi(s,e[i].ri());n>t&&(t=n)}const r=this.Yi.zt();if(null!==r&&null!==this.kp&&(2!==(h=this.ys.crosshair).mode&&h.horzLine.visible&&h.horzLine.labelVisible)){const i=this.Yi.Ts(1,r),n=this.Yi.Ts(this.kp.height-2,r);t=Math.max(t,this.Tp.Vi(s,this.Yi.Zi(Math.floor(Math.min(i,n))+.11111111111111,r)),this.Tp.Vi(s,this.Yi.Zi(Math.ceil(Math.max(i,n))-.11111111111111,r)))}var h;s.restore();const a=t||34;return rs(Math.ceil(i.S+i.C+i.I+i.B+5+a))}Kp(t){null!==this.kp&&s(this.kp,t)||(this.kp=t,this.Vp=!0,this.Np.resizeCanvasElement(t),this.Wp.resizeCanvasElement(t),this.Vp=!1,this.Ef.style.width=`${t.width}px`,this.Ef.style.height=`${t.height}px`)}Xp(){return u(this.kp).width}_s(t){this.Yi!==t&&(null!==this.Yi&&this.Yi.io().u(this),this.Yi=t,t.io().i(this.ol.bind(this),this))}Wt(){return this.Yi}In(){const t=this.yt.Xf();this.yt.Bp().Qt().Ho(t,u(this.Wt()))}Zp(t){if(null===this.kp)return;const i={colorSpace:this.yt.Bp().N().layout.colorSpace};if(1!==t){this.Gp(),this.Np.applySuggestedBitmapSize();const t=n(this.Np,i);null!==t&&(t.useBitmapCoordinateSpace((t=>{this.Jp(t),this.Qp(t)})),this.yt.tv(t,this.Op),this.iv(t),this.yt.tv(t,this.zp),this.sv(t))}this.Wp.applySuggestedBitmapSize();const s=n(this.Wp,i);null!==s&&(s.useBitmapCoordinateSpace((({context:t,bitmapSize:i})=>{t.clearRect(0,0,i.width,i.height)})),this.nv(s),this.yt.tv(s,this.Lp))}Wf(){return this.Np.bitmapSize}Ff(t,i,s){const n=this.Wf();n.width>0&&n.height>0&&t.drawImage(this.Np.canvasElement,i,s)}kt(){this.Yi?.Va()}Yf(t){if(null===this.Yi||this.Yi.Ki()||!this.ys.handleScale.axisPressedMouseMove.price)return;const i=this.yt.Bp().Qt(),s=this.yt.Xf();this.Pp=!0,i.Eo(s,this.Yi,t.localY)}jf(t){if(null===this.Yi||!this.ys.handleScale.axisPressedMouseMove.price)return;const i=this.yt.Bp().Qt(),s=this.yt.Xf(),n=this.Yi;i.zo(s,n,t.localY)}Fp(){if(null===this.Yi||!this.ys.handleScale.axisPressedMouseMove.price)return;const t=this.yt.Bp().Qt(),i=this.yt.Xf(),s=this.Yi;this.Pp&&(this.Pp=!1,t.Lo(i,s))}Kf(t){if(null===this.Yi||!this.ys.handleScale.axisPressedMouseMove.price)return;const i=this.yt.Bp().Qt(),s=this.yt.Xf();this.Pp=!1,i.Lo(s,this.Yi)}Hp(t){this.ys.handleScale.axisDoubleClickReset.price&&this.In()}Up(t){if(null===this.Yi)return;!this.yt.Bp().Qt().N().handleScale.axisPressedMouseMove.price||this.Yi.Le()||this.Yi.Rl()||this.ev(1)}qf(t){this.ev(0)}jp(){const t=[],i=null===this.Yi?void 0:this.Yi;return(s=>{for(let n=0;n<s.length;++n){const e=s[n].Fs(this.yt.Xf(),i);for(let i=0;i<e.length;i++)t.push(e[i])}})(this.yt.Xf().Dt()),t}Jp({context:t,bitmapSize:i}){const{width:s,height:n}=i,e=this.yt.Xf().Qt(),r=e.$(),h=e.Vc();r===h?L(t,0,0,s,n,r):W(t,0,0,s,n,r,h)}Qp({context:t,bitmapSize:i,horizontalPixelRatio:s}){if(null===this.kp||null===this.Yi||!this.Yi.N().borderVisible)return;t.fillStyle=this.Yi.N().borderColor;const n=Math.max(1,Math.floor(this.$p().S*s));let e;e=this.Ep?i.width-n:0,t.fillRect(e,0,n,i.height)}iv(t){if(null===this.kp||null===this.Yi)return;const i=this.Yi.Va(),s=this.Yi.N(),n=this.$p(),e=this.Ep?this.kp.width-n.C:0;s.borderVisible&&s.ticksVisible&&t.useBitmapCoordinateSpace((({context:t,horizontalPixelRatio:r,verticalPixelRatio:h})=>{t.fillStyle=s.borderColor;const a=Math.max(1,Math.floor(h)),l=Math.floor(.5*h),o=Math.round(n.C*r);t.beginPath();for(const s of i)t.rect(Math.floor(e*r),Math.round(s.ka*h)-l,o,a);t.fill()})),t.useMediaCoordinateSpace((({context:t})=>{t.font=this.Yp(),t.fillStyle=s.textColor??this.gl.textColor,t.textAlign=this.Ep?"right":"left",t.textBaseline="middle";const r=this.Ep?Math.round(e-n.I):Math.round(e+n.C+n.I),h=i.map((i=>this.Tp.Di(t,i.Xa)));for(let s=i.length;s--;){const n=i[s];t.fillText(n.Xa,r,n.ka+h[s])}}))}Gp(){if(null===this.kp||null===this.Yi)return;let t=this.kp.height/2;const i=[],s=this.Yi.Dt().slice(),n=this.yt.Xf(),e=this.$p();this.Yi===n.$n()&&this.yt.Xf().Dt().forEach((t=>{n.Un(t)&&s.push(t)}));const r=this.Yi.ba()[0],h=this.Yi;s.forEach((s=>{const e=s.Fs(n,h);e.forEach((t=>{t.Fi(null),t.Hi()&&i.push(t)})),r===s&&e.length>0&&(t=e[0].Bi())})),i.forEach((t=>t.Fi(t.Bi())));this.Yi.N().alignLabels&&this.rv(i,e,t)}rv(t,i,s){if(null===this.kp)return;const n=t.filter((t=>t.Bi()<=s)),e=t.filter((t=>t.Bi()>s));n.sort(((t,i)=>i.Bi()-t.Bi())),n.length&&e.length&&e.push(n[0]),e.sort(((t,i)=>t.Bi()-i.Bi()));for(const s of t){const t=Math.floor(s.$t(i)/2),n=s.Bi();n>-t&&n<t&&s.Fi(t),n>this.kp.height-t&&n<this.kp.height+t&&s.Fi(this.kp.height-t)}Cs(n,1,this.kp.height,i),Cs(e,-1,this.kp.height,i)}sv(t){if(null===this.kp)return;const i=this.jp(),s=this.$p(),n=this.Ep?"right":"left";i.forEach((i=>{if(i.Ui()){i.Tt(u(this.Yi)).nt(t,s,this.Tp,n)}}))}nv(t){if(null===this.kp||null===this.Yi)return;const i=this.yt.Bp().Qt(),s=[],n=this.yt.Xf(),e=i.sc().Fs(n,this.Yi);e.length&&s.push(e);const r=this.$p(),h=this.Ep?"right":"left";s.forEach((i=>{i.forEach((i=>{i.Tt(u(this.Yi)).nt(t,r,this.Tp,h)}))}))}ev(t){this.Ef.style.cursor=1===t?"ns-resize":"default"}ol(){const t=this.qp();this.Dp<t&&this.yt.Bp().Qt().Bh(),this.Dp=t}Yp(){return x(this.gl.fontSize,this.gl.fontFamily)}}function ks(t,i){return t.Jh?.(i)??[]}function Ps(t,i){return t.Ws?.(i)??[]}function Ts(t,i){return t.us?.(i)??[]}function Rs(t,i){return t.Xh?.(i)??[]}class Ds{constructor(i,s){this.kp=t({width:0,height:0}),this.hv=null,this.av=null,this.lv=null,this.ov=null,this._v=!1,this.uv=new d,this.cv=new d,this.dv=0,this.fv=!1,this.pv=null,this.vv=!1,this.mv=null,this.wv=null,this.Vp=!1,this.Ip=()=>{this.Vp||null===this.gv||this.ts().ar()},this.Ap=()=>{this.Vp||null===this.gv||this.ts().ar()},this.gp=i,this.gv=s,this.gv.Yo().i(this.Mv.bind(this),this,!0),this.bv=document.createElement("td"),this.bv.style.padding="0",this.bv.style.position="relative";const n=document.createElement("div");n.style.width="100%",n.style.height="100%",n.style.position="relative",n.style.overflow="hidden",this.Sv=document.createElement("td"),this.Sv.style.padding="0",this.xv=document.createElement("td"),this.xv.style.padding="0",this.bv.appendChild(n),this.Np=ws(n,t({width:16,height:16})),this.Np.subscribeSuggestedBitmapSizeChanged(this.Ip);const e=this.Np.canvasElement;e.style.position="absolute",e.style.zIndex="1",e.style.left="0",e.style.top="0",this.Wp=ws(n,t({width:16,height:16})),this.Wp.subscribeSuggestedBitmapSizeChanged(this.Ap);const r=this.Wp.canvasElement;r.style.position="absolute",r.style.zIndex="2",r.style.left="0",r.style.top="0",this.Af=document.createElement("tr"),this.Af.appendChild(this.Sv),this.Af.appendChild(this.bv),this.Af.appendChild(this.xv),this.Cv(),this.Pf=new as(this.Wp.canvasElement,this,{Xd:()=>null===this.pv&&!this.gp.N().handleScroll.vertTouchDrag,Zd:()=>null===this.pv&&!this.gp.N().handleScroll.horzTouchDrag})}m(){null!==this.hv&&this.hv.m(),null!==this.av&&this.av.m(),this.lv=null,this.Wp.unsubscribeSuggestedBitmapSizeChanged(this.Ap),gs(this.Wp.canvasElement),this.Wp.dispose(),this.Np.unsubscribeSuggestedBitmapSizeChanged(this.Ip),gs(this.Np.canvasElement),this.Np.dispose(),null!==this.gv&&(this.gv.Yo().u(this),this.gv.m()),this.Pf.m()}Xf(){return u(this.gv)}yv(t){null!==this.gv&&this.gv.Yo().u(this),this.gv=t,null!==this.gv&&this.gv.Yo().i(Ds.prototype.Mv.bind(this),this,!0),this.Cv(),this.gp.If().indexOf(this)===this.gp.If().length-1?(this.lv=this.lv??new ms(this.bv,this.gp),this.lv.kt()):(this.lv?.bp(),this.lv=null)}Bp(){return this.gp}Of(){return this.Af}Cv(){if(null!==this.gv&&(this.kv(),0!==this.ts().js().length)){if(null!==this.hv){const t=this.gv.Bo();this.hv._s(u(t))}if(null!==this.av){const t=this.gv.Ao();this.av._s(u(t))}}}Pv(){null!==this.hv&&this.hv.kt(),null!==this.av&&this.av.kt()}ko(){return null!==this.gv?this.gv.ko():0}Po(t){this.gv&&this.gv.Po(t)}$d(t){if(!this.gv)return;this.Tv();const i=t.localX,s=t.localY;this.Rv(i,s,t)}_f(t){this.Tv(),this.Dv(),this.Rv(t.localX,t.localY,t)}qd(t){if(!this.gv)return;this.Tv();const i=t.localX,s=t.localY;this.Rv(i,s,t)}ef(t){null!==this.gv&&(this.Tv(),this.Vv(t))}Ld(t){null!==this.gv&&this.Iv(this.cv,t)}Bd(t){this.Ld(t)}Qd(t){this.Tv(),this.Bv(t),this.Rv(t.localX,t.localY,t)}nf(t){null!==this.gv&&(this.Tv(),this.fv=!1,this.Av(t))}sf(t){null!==this.gv&&this.Vv(t)}bf(t){if(this.fv=!0,null===this.pv){const i={x:t.localX,y:t.localY};this.Ev(i,i,t)}}Mf(t){null!==this.gv&&(this.Tv(),this.gv.Qt().Ju(null),this.zv())}Lv(){return this.uv}Ov(){return this.cv}ff(){this.dv=1,this.ts().hn()}pf(t,i){if(!this.gp.N().handleScale.pinch)return;const s=5*(i-this.dv);this.dv=i,this.ts()._c(t._t,s)}af(t){this.fv=!1,this.vv=null!==this.pv,this.Dv();const i=this.ts().sc();null!==this.pv&&i.Vt()&&(this.mv={x:i.si(),y:i.ni()},this.pv={x:t.localX,y:t.localY})}Gd(t){if(null===this.gv)return;const i=t.localX,s=t.localY;if(null===this.pv)this.Bv(t);else{this.vv=!1;const n=u(this.mv),e=n.x+(i-this.pv.x),r=n.y+(s-this.pv.y);this.Rv(e,r,t)}}if(t){0===this.Bp().N().trackingMode.exitMode&&(this.vv=!0),this.Nv(),this.Av(t)}jn(t,i){const s=this.gv;return null===s?null:Ci(s,t,i)}Wv(i,s){u("left"===s?this.hv:this.av).Kp(t({width:i,height:this.kp.height}))}Nf(){return this.kp}Kp(t){s(this.kp,t)||(this.kp=t,this.Vp=!0,this.Np.resizeCanvasElement(t),this.Wp.resizeCanvasElement(t),this.Vp=!1,this.bv.style.width=t.width+"px",this.bv.style.height=t.height+"px")}Fv(){const t=u(this.gv);t.Io(t.Bo()),t.Io(t.Ao());for(const i of t.ba())if(t.Un(i)){const s=i.Wt();null!==s&&t.Io(s),i.Ns()}for(const i of t.Ko())i.Ns()}Wf(){return this.Np.bitmapSize}Ff(t,i,s){const n=this.Wf();n.width>0&&n.height>0&&t.drawImage(this.Np.canvasElement,i,s)}Zp(t){if(0===t)return;if(null===this.gv)return;t>1&&this.Fv(),null!==this.hv&&this.hv.Zp(t),null!==this.av&&this.av.Zp(t);const i={colorSpace:this.gp.N().layout.colorSpace};if(1!==t){this.Np.applySuggestedBitmapSize();const t=n(this.Np,i);null!==t&&(t.useBitmapCoordinateSpace((t=>{this.Jp(t)})),this.gv&&(this.Hv(t,ks),this.Uv(t),this.Hv(t,Ps),this.Hv(t,Ts)))}this.Wp.applySuggestedBitmapSize();const s=n(this.Wp,i);null!==s&&(s.useBitmapCoordinateSpace((({context:t,bitmapSize:i})=>{t.clearRect(0,0,i.width,i.height)})),this.$v(s),this.Hv(s,Rs),this.Hv(s,Ts))}qv(){return this.hv}Yv(){return this.av}tv(t,i){this.Hv(t,i)}Mv(){null!==this.gv&&this.gv.Yo().u(this),this.gv=null}Vv(t){this.Iv(this.uv,t)}Iv(t,i){const s=i.localX,n=i.localY;t.v()&&t.p(this.ts().At().nu(s),{x:s,y:n},i)}Jp({context:t,bitmapSize:i}){const{width:s,height:n}=i,e=this.ts(),r=e.$(),h=e.Vc();r===h?L(t,0,0,s,n,h):W(t,0,0,s,n,r,h)}Uv(t){const i=u(this.gv),s=i.jo().lr().Tt(i);null!==s&&s.nt(t,!1)}$v(t){this.jv(t,Ps,bs,this.ts().sc())}Hv(t,i){const s=u(this.gv),n=s.Dt(),e=s.Ko();for(const s of e)this.jv(t,i,Ms,s);for(const s of n)this.jv(t,i,Ms,s);for(const s of e)this.jv(t,i,bs,s);for(const s of n)this.jv(t,i,bs,s)}jv(t,i,s,n){const e=u(this.gv),r=e.Qt().Gu(),h=null!==r&&r.Xo===n,a=null!==r&&h&&void 0!==r.Zo?r.Zo.Xn:void 0;Ss(i,(i=>s(i,t,h,a)),n,e)}kv(){if(null===this.gv)return;const t=this.gp,i=this.gv.Bo().N().visible,s=this.gv.Ao().N().visible;i||null===this.hv||(this.Sv.removeChild(this.hv.Of()),this.hv.m(),this.hv=null),s||null===this.av||(this.xv.removeChild(this.av.Of()),this.av.m(),this.av=null);const n=t.Qt().Sc();i&&null===this.hv&&(this.hv=new ys(this,t.N(),n,"left"),this.Sv.appendChild(this.hv.Of())),s&&null===this.av&&(this.av=new ys(this,t.N(),n,"right"),this.xv.appendChild(this.av.Of()))}Kv(t){return t.Sf&&this.fv||null!==this.pv}Xv(t){return Math.max(0,Math.min(t,this.kp.width-1))}Zv(t){return Math.max(0,Math.min(t,this.kp.height-1))}Rv(t,i,s){this.ts().wc(this.Xv(t),this.Zv(i),s,u(this.gv))}zv(){this.ts().Mc()}Nv(){this.vv&&(this.pv=null,this.zv())}Ev(t,i,s){this.pv=t,this.vv=!1,this.Rv(i.x,i.y,s);const n=this.ts().sc();this.mv={x:n.si(),y:n.ni()}}ts(){return this.gp.Qt()}Av(t){if(!this._v)return;const i=this.ts(),s=this.Xf();if(i.Wo(s,s.Ps()),this.ov=null,this._v=!1,i.fc(),null!==this.wv){const t=performance.now(),s=i.At();this.wv.le(s._u(),t),this.wv.Mu(t)||i._n(this.wv)}}Tv(){this.pv=null}Dv(){if(!this.gv)return;if(this.ts().hn(),document.activeElement!==document.body&&document.activeElement!==document.documentElement)u(document.activeElement).blur();else{const t=document.getSelection();null!==t&&t.removeAllRanges()}!this.gv.Ps().Ki()&&this.ts().At().Ki()}Bv(t){if(null===this.gv)return;const i=this.ts(),s=i.At();if(s.Ki())return;const n=this.gp.N(),e=n.handleScroll,r=n.kineticScroll;if((!e.pressedMouseMove||t.Sf)&&(!e.horzTouchDrag&&!e.vertTouchDrag||!t.Sf))return;const h=this.gv.Ps(),a=performance.now();if(null!==this.ov||this.Kv(t)||(this.ov={x:t.clientX,y:t.clientY,Yc:a,Gv:t.localX,Jv:t.localY}),null!==this.ov&&!this._v&&(this.ov.x!==t.clientX||this.ov.y!==t.clientY)){if(t.Sf&&r.touch||!t.Sf&&r.mouse){const t=s.lu();this.wv=new vs(.2/t,7/t,.997,15/t),this.wv.dp(s._u(),this.ov.Yc)}else this.wv=null;h.Ki()||i.Oo(this.gv,h,t.localY),i.cc(t.localX),this._v=!0}this._v&&(h.Ki()||i.No(this.gv,h,t.localY),i.dc(t.localX),null!==this.wv&&this.wv.dp(s._u(),a))}}class Vs{constructor(i,s,n,e,r){this.xt=!0,this.kp=t({width:0,height:0}),this.Ip=()=>this.Zp(3),this.Ep="left"===i,this.Uu=n.Sc,this.ys=s,this.Qv=e,this.tm=r,this.Ef=document.createElement("div"),this.Ef.style.width="25px",this.Ef.style.height="100%",this.Ef.style.overflow="hidden",this.Np=ws(this.Ef,t({width:16,height:16})),this.Np.subscribeSuggestedBitmapSizeChanged(this.Ip)}m(){this.Np.unsubscribeSuggestedBitmapSizeChanged(this.Ip),gs(this.Np.canvasElement),this.Np.dispose()}Of(){return this.Ef}Nf(){return this.kp}Kp(t){s(this.kp,t)||(this.kp=t,this.Np.resizeCanvasElement(t),this.Ef.style.width=`${t.width}px`,this.Ef.style.height=`${t.height}px`,this.xt=!0)}Zp(t){if(t<3&&!this.xt)return;if(0===this.kp.width||0===this.kp.height)return;this.xt=!1,this.Np.applySuggestedBitmapSize();const i=n(this.Np,{colorSpace:this.ys.layout.colorSpace});null!==i&&i.useBitmapCoordinateSpace((t=>{this.Jp(t),this.Qp(t)}))}Wf(){return this.Np.bitmapSize}Ff(t,i,s){const n=this.Wf();n.width>0&&n.height>0&&t.drawImage(this.Np.canvasElement,i,s)}Qp({context:t,bitmapSize:i,horizontalPixelRatio:s,verticalPixelRatio:n}){if(!this.Qv())return;t.fillStyle=this.ys.timeScale.borderColor;const e=Math.floor(this.Uu.N().S*s),r=Math.floor(this.Uu.N().S*n),h=this.Ep?i.width-e:0;t.fillRect(h,0,e,r)}Jp({context:t,bitmapSize:i}){L(t,0,0,i.width,i.height,this.tm())}}function Is(t){return i=>i.ia?.(t)??[]}const Bs=Is("normal"),As=Is("top"),Es=Is("bottom");class zs{constructor(i,s){this.im=null,this.sm=null,this.M=null,this.nm=!1,this.kp=t({width:0,height:0}),this.rm=new d,this.Tp=new et(5),this.Vp=!1,this.Ip=()=>{this.Vp||this.gp.Qt().ar()},this.Ap=()=>{this.Vp||this.gp.Qt().ar()},this.gp=i,this.i_=s,this.ys=i.N().layout,this.pp=document.createElement("tr"),this.hm=document.createElement("td"),this.hm.style.padding="0",this.am=document.createElement("td"),this.am.style.padding="0",this.Ef=document.createElement("td"),this.Ef.style.height="25px",this.Ef.style.padding="0",this.lm=document.createElement("div"),this.lm.style.width="100%",this.lm.style.height="100%",this.lm.style.position="relative",this.lm.style.overflow="hidden",this.Ef.appendChild(this.lm),this.Np=ws(this.lm,t({width:16,height:16})),this.Np.subscribeSuggestedBitmapSizeChanged(this.Ip);const n=this.Np.canvasElement;n.style.position="absolute",n.style.zIndex="1",n.style.left="0",n.style.top="0",this.Wp=ws(this.lm,t({width:16,height:16})),this.Wp.subscribeSuggestedBitmapSizeChanged(this.Ap);const e=this.Wp.canvasElement;e.style.position="absolute",e.style.zIndex="2",e.style.left="0",e.style.top="0",this.pp.appendChild(this.hm),this.pp.appendChild(this.Ef),this.pp.appendChild(this.am),this.om(),this.gp.Qt().yo().i(this.om.bind(this),this),this.Pf=new as(this.Wp.canvasElement,this,{Xd:()=>!0,Zd:()=>!this.gp.N().handleScroll.horzTouchDrag})}m(){this.Pf.m(),null!==this.im&&this.im.m(),null!==this.sm&&this.sm.m(),this.Wp.unsubscribeSuggestedBitmapSizeChanged(this.Ap),gs(this.Wp.canvasElement),this.Wp.dispose(),this.Np.unsubscribeSuggestedBitmapSizeChanged(this.Ip),gs(this.Np.canvasElement),this.Np.dispose()}Of(){return this.pp}_m(){return this.im}um(){return this.sm}_f(t){if(this.nm)return;this.nm=!0;const i=this.gp.Qt();!i.At().Ki()&&this.gp.N().handleScale.axisPressedMouseMove.time&&i.oc(t.localX)}af(t){this._f(t)}uf(){const t=this.gp.Qt();!t.At().Ki()&&this.nm&&(this.nm=!1,this.gp.N().handleScale.axisPressedMouseMove.time&&t.mc())}Qd(t){const i=this.gp.Qt();!i.At().Ki()&&this.gp.N().handleScale.axisPressedMouseMove.time&&i.vc(t.localX)}Gd(t){this.Qd(t)}nf(){this.nm=!1;const t=this.gp.Qt();t.At().Ki()&&!this.gp.N().handleScale.axisPressedMouseMove.time||t.mc()}if(){this.nf()}Ld(){this.gp.N().handleScale.axisDoubleClickReset.time&&this.gp.Qt().cn()}Bd(){this.Ld()}$d(){this.gp.Qt().N().handleScale.axisPressedMouseMove.time&&this.ev(1)}Mf(){this.ev(0)}Nf(){return this.kp}dm(){return this.rm}fm(i,n,e){s(this.kp,i)||(this.kp=i,this.Vp=!0,this.Np.resizeCanvasElement(i),this.Wp.resizeCanvasElement(i),this.Vp=!1,this.Ef.style.width=`${i.width}px`,this.Ef.style.height=`${i.height}px`,this.rm.p(i)),null!==this.im&&this.im.Kp(t({width:n,height:i.height})),null!==this.sm&&this.sm.Kp(t({width:e,height:i.height}))}pm(){const t=this.vm();return Math.ceil(t.S+t.C+t.k+t.A+t.V+t.wm)}kt(){this.gp.Qt().At().Va()}Wf(){return this.Np.bitmapSize}Ff(t,i,s){const n=this.Wf();n.width>0&&n.height>0&&t.drawImage(this.Np.canvasElement,i,s)}Zp(t){if(0===t)return;const i={colorSpace:this.ys.colorSpace};if(1!==t){this.Np.applySuggestedBitmapSize();const s=n(this.Np,i);null!==s&&(s.useBitmapCoordinateSpace((t=>{this.Jp(t),this.Qp(t),this.gm(s,Es)})),this.iv(s),this.gm(s,Bs)),null!==this.im&&this.im.Zp(t),null!==this.sm&&this.sm.Zp(t)}this.Wp.applySuggestedBitmapSize();const s=n(this.Wp,i);null!==s&&(s.useBitmapCoordinateSpace((({context:t,bitmapSize:i})=>{t.clearRect(0,0,i.width,i.height)})),this.Mm([...this.gp.Qt().js(),this.gp.Qt().sc()],s),this.gm(s,As))}gm(t,i){const s=this.gp.Qt().js();for(const n of s)Ss(i,(i=>Ms(i,t,!1,void 0)),n,void 0);for(const n of s)Ss(i,(i=>bs(i,t,!1,void 0)),n,void 0)}Jp({context:t,bitmapSize:i}){L(t,0,0,i.width,i.height,this.gp.Qt().Vc())}Qp({context:t,bitmapSize:i,verticalPixelRatio:s}){if(this.gp.N().timeScale.borderVisible){t.fillStyle=this.bm();const n=Math.max(1,Math.floor(this.vm().S*s));t.fillRect(0,0,i.width,n)}}iv(t){const i=this.gp.Qt().At(),s=i.Va();if(!s||0===s.length)return;const n=this.i_.maxTickMarkWeight(s),e=this.vm(),r=i.N();r.borderVisible&&r.ticksVisible&&t.useBitmapCoordinateSpace((({context:t,horizontalPixelRatio:i,verticalPixelRatio:n})=>{t.strokeStyle=this.bm(),t.fillStyle=this.bm();const r=Math.max(1,Math.floor(i)),h=Math.floor(.5*i);t.beginPath();const a=Math.round(e.C*n);for(let n=s.length;n--;){const e=Math.round(s[n].coord*i);t.rect(e-h,0,r,a)}t.fill()})),t.useMediaCoordinateSpace((({context:t})=>{const i=e.S+e.C+e.A+e.k/2;t.textAlign="center",t.textBaseline="middle",t.fillStyle=this.H(),t.font=this.Yp();for(const e of s)if(e.weight<n){const s=e.needAlignCoordinate?this.Sm(t,e.coord,e.label):e.coord;t.fillText(e.label,s,i)}this.gp.N().timeScale.allowBoldLabels&&(t.font=this.xm());for(const e of s)if(e.weight>=n){const s=e.needAlignCoordinate?this.Sm(t,e.coord,e.label):e.coord;t.fillText(e.label,s,i)}}))}Sm(t,i,s){const n=this.Tp.Vi(t,s),e=n/2,r=Math.floor(i-e)+.5;return r<0?i+=Math.abs(0-r):r+n>this.kp.width&&(i-=Math.abs(this.kp.width-(r+n))),i}Mm(t,i){const s=this.vm();for(const n of t)for(const t of n.cs())t.Tt().nt(i,s)}bm(){return this.gp.N().timeScale.borderColor}H(){return this.ys.textColor}W(){return this.ys.fontSize}Yp(){return x(this.W(),this.ys.fontFamily)}xm(){return x(this.W(),this.ys.fontFamily,"bold")}vm(){null===this.M&&(this.M={S:1,L:NaN,A:NaN,V:NaN,Ji:NaN,C:5,k:NaN,P:"",Gi:new et,wm:0});const t=this.M,i=this.Yp();if(t.P!==i){const s=this.W();t.k=s,t.P=i,t.A=3*s/12,t.V=3*s/12,t.Ji=9*s/12,t.L=0,t.wm=4*s/12,t.Gi.In()}return this.M}ev(t){this.Ef.style.cursor=1===t?"ew-resize":"default"}om(){const t=this.gp.Qt(),i=t.N();i.leftPriceScale.visible||null===this.im||(this.hm.removeChild(this.im.Of()),this.im.m(),this.im=null),i.rightPriceScale.visible||null===this.sm||(this.am.removeChild(this.sm.Of()),this.sm.m(),this.sm=null);const s={Sc:this.gp.Qt().Sc()},n=()=>i.leftPriceScale.borderVisible&&t.At().N().borderVisible,e=()=>t.Vc();i.leftPriceScale.visible&&null===this.im&&(this.im=new Vs("left",i,s,n,e),this.hm.appendChild(this.im.Of())),i.rightPriceScale.visible&&null===this.sm&&(this.sm=new Vs("right",i,s,n,e),this.am.appendChild(this.sm.Of()))}}const Ls=!!ss&&!!navigator.userAgentData&&navigator.userAgentData.brands.some((t=>t.brand.includes("Chromium")))&&!!ss&&(navigator?.userAgentData?.platform?"Windows"===navigator.userAgentData.platform:navigator.userAgent.toLowerCase().indexOf("win")>=0);class Os{constructor(t,i,s){var n;this.Cm=[],this.ym=[],this.km=0,this.tl=0,this.fo=0,this.Pm=0,this.Tm=0,this.Rm=null,this.Dm=!1,this.uv=new d,this.cv=new d,this.Wu=new d,this.Vm=null,this.Im=null,this.wp=t,this.ys=i,this.i_=s,this.pp=document.createElement("div"),this.pp.classList.add("tv-lightweight-charts"),this.pp.style.overflow="hidden",this.pp.style.direction="ltr",this.pp.style.width="100%",this.pp.style.height="100%",(n=this.pp).style.userSelect="none",n.style.webkitUserSelect="none",n.style.msUserSelect="none",n.style.MozUserSelect="none",n.style.webkitTapHighlightColor="transparent",this.Bm=document.createElement("table"),this.Bm.setAttribute("cellspacing","0"),this.pp.appendChild(this.Bm),this.Am=this.Em.bind(this),Ns(this.ys)&&this.zm(!0),this.ts=new Li(this.Hu.bind(this),this.ys,s),this.Qt().nc().i(this.Lm.bind(this),this),this.Om=new zs(this,this.i_),this.Bm.appendChild(this.Om.Of());const e=i.autoSize&&this.Nm();let r=this.ys.width,h=this.ys.height;if(e||0===r||0===h){const i=t.getBoundingClientRect();r=r||i.width,h=h||i.height}this.Wm(r,h),this.Fm(),t.appendChild(this.pp),this.Hm(),this.ts.At().Cu().i(this.ts.Bh.bind(this.ts),this),this.ts.yo().i(this.ts.Bh.bind(this.ts),this)}Qt(){return this.ts}N(){return this.ys}If(){return this.Cm}Um(){return this.Om}m(){this.zm(!1),0!==this.km&&window.cancelAnimationFrame(this.km),this.ts.nc().u(this),this.ts.At().Cu().u(this),this.ts.yo().u(this),this.ts.m();for(const t of this.Cm)this.Bm.removeChild(t.Of()),t.Lv().u(this),t.Ov().u(this),t.m();this.Cm=[];for(const t of this.ym)this.$m(t);this.ym=[],u(this.Om).m(),null!==this.pp.parentElement&&this.pp.parentElement.removeChild(this.pp),this.Wu.m(),this.uv.m(),this.cv.m(),this.qm()}Wm(i,s,n=!1){if(this.tl===s&&this.fo===i)return;const e=function(i){const s=Math.floor(i.width),n=Math.floor(i.height);return t({width:s-s%2,height:n-n%2})}(t({width:i,height:s}));this.tl=e.height,this.fo=e.width;const r=this.tl+"px",h=this.fo+"px";u(this.pp).style.height=r,u(this.pp).style.width=h,this.Bm.style.height=r,this.Bm.style.width=h,n?this.Ym(G.gn(),performance.now()):this.ts.Bh()}Zp(t){void 0===t&&(t=G.gn());for(let i=0;i<this.Cm.length;i++)this.Cm[i].Zp(t.en(i).tn);this.ys.timeScale.visible&&this.Om.Zp(t.nn())}hr(t){const i=Ns(this.ys);this.ts.hr(t);const s=Ns(this.ys);s!==i&&this.zm(s),t.layout?.panes&&this.jm(),this.Hm(),this.Km(t)}Lv(){return this.uv}Ov(){return this.cv}nc(){return this.Wu}Xm(){null!==this.Rm&&(this.Ym(this.Rm,performance.now()),this.Rm=null);const t=this.Zm(null),i=document.createElement("canvas");i.width=t.width,i.height=t.height;const s=u(i.getContext("2d"));return this.Zm(s),i}Gm(t){if("left"===t&&!this.Jm())return 0;if("right"===t&&!this.Qm())return 0;if(0===this.Cm.length)return 0;return u("left"===t?this.Cm[0].qv():this.Cm[0].Yv()).Xp()}tw(){return this.ys.autoSize&&null!==this.Vm}Uf(){return this.pp}iw(t){this.Im=t,this.Im?this.Uf().style.setProperty("cursor",t):this.Uf().style.removeProperty("cursor")}sw(){return this.Im}nw(t){return _(this.Cm[t]).Nf()}jm(){this.ym.forEach((t=>{t.kt()}))}Km(t){(void 0!==t.autoSize||!this.Vm||void 0===t.width&&void 0===t.height)&&(t.autoSize&&!this.Vm&&this.Nm(),!1===t.autoSize&&null!==this.Vm&&this.qm(),t.autoSize||void 0===t.width&&void 0===t.height||this.Wm(t.width||this.fo,t.height||this.tl))}Zm(i){let s=0,n=0;const e=this.Cm[0],r=(t,s)=>{let n=0;for(let e=0;e<this.Cm.length;e++){const r=this.Cm[e],h=u("left"===t?r.qv():r.Yv()),a=h.Wf();if(null!==i&&h.Ff(i,s,n),n+=a.height,e<this.Cm.length-1){const t=this.ym[e],r=t.Wf();null!==i&&t.Ff(i,s,n),n+=r.height}}};if(this.Jm()){r("left",0);s+=u(e.qv()).Wf().width}for(let t=0;t<this.Cm.length;t++){const e=this.Cm[t],r=e.Wf();if(null!==i&&e.Ff(i,s,n),n+=r.height,t<this.Cm.length-1){const e=this.ym[t],r=e.Wf();null!==i&&e.Ff(i,s,n),n+=r.height}}if(s+=e.Wf().width,this.Qm()){r("right",s);s+=u(e.Yv()).Wf().width}const h=(t,s,n)=>{u("left"===t?this.Om._m():this.Om.um()).Ff(u(i),s,n)};if(this.ys.timeScale.visible){const t=this.Om.Wf();if(null!==i){let s=0;this.Jm()&&(h("left",s,n),s=u(e.qv()).Wf().width),this.Om.Ff(i,s,n),s+=t.width,this.Qm()&&h("right",s,n)}n+=t.height}return t({width:s,height:n})}ew(){let i=0,s=0,n=0;for(const t of this.Cm)this.Jm()&&(s=Math.max(s,u(t.qv()).qp(),this.ys.leftPriceScale.minimumWidth)),this.Qm()&&(n=Math.max(n,u(t.Yv()).qp(),this.ys.rightPriceScale.minimumWidth)),i+=t.ko();s=rs(s),n=rs(n);const e=this.fo,r=this.tl,h=Math.max(e-s-n,0),a=1*this.ym.length,l=this.ys.timeScale.visible;let o=l?Math.max(this.Om.pm(),this.ys.timeScale.minimumHeight):0;var _;o=(_=o)+_%2;const c=a+o,d=r<c?0:r-c,f=d/i;let p=0;const v=window.devicePixelRatio||1;for(let i=0;i<this.Cm.length;++i){const e=this.Cm[i];e.yv(this.ts.$s()[i]);let r=0,a=0;a=i===this.Cm.length-1?Math.ceil((d-p)*v)/v:Math.round(e.ko()*f*v)/v,r=Math.max(a,2),p+=r,e.Kp(t({width:h,height:r})),this.Jm()&&e.Wv(s,"left"),this.Qm()&&e.Wv(n,"right"),e.Xf()&&this.ts.ec(e.Xf(),r)}this.Om.fm(t({width:l?h:0,height:o}),l?s:0,l?n:0),this.ts.To(h),this.Pm!==s&&(this.Pm=s),this.Tm!==n&&(this.Tm=n)}zm(t){t?this.pp.addEventListener("wheel",this.Am,{passive:!1}):this.pp.removeEventListener("wheel",this.Am)}rw(t){switch(t.deltaMode){case t.DOM_DELTA_PAGE:return 120;case t.DOM_DELTA_LINE:return 32}return Ls?1/window.devicePixelRatio:1}Em(t){if(!(0!==t.deltaX&&this.ys.handleScroll.mouseWheel||0!==t.deltaY&&this.ys.handleScale.mouseWheel))return;const i=this.rw(t),s=i*t.deltaX/100,n=-i*t.deltaY/100;if(t.cancelable&&t.preventDefault(),0!==n&&this.ys.handleScale.mouseWheel){const i=Math.sign(n)*Math.min(1,Math.abs(n)),s=t.clientX-this.pp.getBoundingClientRect().left;this.Qt()._c(s,i)}0!==s&&this.ys.handleScroll.mouseWheel&&this.Qt().uc(-80*s)}Ym(t,i){const s=t.nn();3===s&&this.hw(),3!==s&&2!==s||(this.aw(t),this.lw(t,i),this.Om.kt(),this.Cm.forEach((t=>{t.Pv()})),3===this.Rm?.nn()&&(this.Rm.vn(t),this.hw(),this.aw(this.Rm),this.lw(this.Rm,i),t=this.Rm,this.Rm=null)),this.Zp(t)}lw(t,i){for(const s of t.pn())this.mn(s,i)}aw(t){const i=this.ts.$s();for(let s=0;s<i.length;s++)t.en(s).sn&&i[s].Uo()}mn(t,i){const s=this.ts.At();switch(t.an){case 0:s.ku();break;case 1:s.Pu(t.Ft);break;case 2:s.dn(t.Ft);break;case 3:s.fn(t.Ft);break;case 4:s.du();break;case 5:t.Ft.Mu(i)||s.fn(t.Ft.bu(i))}}Hu(t){null!==this.Rm?this.Rm.vn(t):this.Rm=t,this.Dm||(this.Dm=!0,this.km=window.requestAnimationFrame((t=>{if(this.Dm=!1,this.km=0,null!==this.Rm){const i=this.Rm;this.Rm=null,this.Ym(i,t);for(const s of i.pn())if(5===s.an&&!s.Ft.Mu(t)){this.Qt()._n(s.Ft);break}}})))}hw(){this.Fm()}$m(t){this.Bm.removeChild(t.Of()),t.m()}Fm(){const t=this.ts.$s(),i=t.length,s=this.Cm.length;for(let t=i;t<s;t++){const t=_(this.Cm.pop());this.Bm.removeChild(t.Of()),t.Lv().u(this),t.Ov().u(this),t.m();const i=this.ym.pop();void 0!==i&&this.$m(i)}for(let n=s;n<i;n++){const i=new Ds(this,t[n]);if(i.Lv().i(this.ow.bind(this,i),this),i.Ov().i(this._w.bind(this,i),this),this.Cm.push(i),n>0){const t=new ds(this,n-1,n);this.ym.push(t),this.Bm.insertBefore(t.Of(),this.Om.Of())}this.Bm.insertBefore(i.Of(),this.Om.Of())}for(let s=0;s<i;s++){const i=t[s],n=this.Cm[s];n.Xf()!==i?n.yv(i):n.Cv()}this.Hm(),this.ew()}uw(t,i,s,n){const e=new Map;if(null!==t){this.ts.js().forEach((i=>{const s=i.Xs().Wr(t);null!==s&&e.set(i,s)}))}let r;if(null!==t){const i=this.ts.At().ss(t)?.originalTime;void 0!==i&&(r=i)}const h=this.Qt().Gu(),a=null!==h&&h.Xo instanceof Yt?h.Xo:void 0,l=null!==h&&void 0!==h.Zo?h.Zo.Kn:void 0,o=this.cw(n);return{dw:r,Re:t??void 0,fw:i??void 0,pw:-1!==o?o:void 0,mw:a,ww:e,gw:l,Mw:s??void 0}}cw(t){let i=-1;if(t)i=this.Cm.indexOf(t);else{const t=this.Qt().sc().Us();null!==t&&(i=this.Qt().$s().indexOf(t))}return i}ow(t,i,s,n){this.uv.p((()=>this.uw(i,s,n,t)))}_w(t,i,s,n){this.cv.p((()=>this.uw(i,s,n,t)))}Lm(t,i,s){this.iw(this.Qt().Gu()?.Jo??null),this.Wu.p((()=>this.uw(t,i,s)))}Hm(){const t=this.ys.timeScale.visible?"":"none";this.Om.Of().style.display=t}Jm(){return this.Cm[0].Xf().Bo().N().visible}Qm(){return this.Cm[0].Xf().Ao().N().visible}Nm(){return"ResizeObserver"in window&&(this.Vm=new ResizeObserver((t=>{const i=t[t.length-1];i&&this.Wm(i.contentRect.width,i.contentRect.height)})),this.Vm.observe(this.wp,{box:"border-box"}),!0)}qm(){null!==this.Vm&&this.Vm.disconnect(),this.Vm=null}}function Ns(t){return Boolean(t.handleScroll.mouseWheel||t.handleScale.mouseWheel)}function Ws(t){return void 0===t.open&&void 0===t.value}function Fs(t){return function(t){return void 0!==t.open}(t)||function(t){return void 0!==t.value}(t)}function Hs(t,i,s,n){const e=s.value,r={Re:i,wt:t,Ft:[e,e,e,e],dw:n};return void 0!==s.color&&(r.R=s.color),r}function Us(t,i,s,n){const e=s.value,r={Re:i,wt:t,Ft:[e,e,e,e],dw:n};return void 0!==s.lineColor&&(r.vt=s.lineColor),void 0!==s.topColor&&(r.mr=s.topColor),void 0!==s.bottomColor&&(r.wr=s.bottomColor),r}function $s(t,i,s,n){const e=s.value,r={Re:i,wt:t,Ft:[e,e,e,e],dw:n};return void 0!==s.topLineColor&&(r.gr=s.topLineColor),void 0!==s.bottomLineColor&&(r.Mr=s.bottomLineColor),void 0!==s.topFillColor1&&(r.br=s.topFillColor1),void 0!==s.topFillColor2&&(r.Sr=s.topFillColor2),void 0!==s.bottomFillColor1&&(r.Cr=s.bottomFillColor1),void 0!==s.bottomFillColor2&&(r.yr=s.bottomFillColor2),r}function qs(t,i,s,n){const e={Re:i,wt:t,Ft:[s.open,s.high,s.low,s.close],dw:n};return void 0!==s.color&&(e.R=s.color),e}function Ys(t,i,s,n){const e={Re:i,wt:t,Ft:[s.open,s.high,s.low,s.close],dw:n};return void 0!==s.color&&(e.R=s.color),void 0!==s.borderColor&&(e.Ht=s.borderColor),void 0!==s.wickColor&&(e.vr=s.wickColor),e}function js(t,i,s,n,e){const r=_(e)(s),h=Math.max(...r),a=Math.min(...r),l=r[r.length-1],o=[l,h,a,l],{time:u,color:c,...d}=s;return{Re:i,wt:t,Ft:o,dw:n,se:d,R:c}}function Ks(t){return void 0!==t.Ft}function Xs(t,i){return void 0!==i.customValues&&(t.bw=i.customValues),t}function Zs(t){return(i,s,n,e,r,h)=>function(t,i){return i?i(t):Ws(t)}(n,h)?Xs({wt:i,Re:s,dw:e},n):Xs(t(i,s,n,e,r),n)}function Gs(t){return{Candlestick:Zs(Ys),Bar:Zs(qs),Area:Zs(Us),Baseline:Zs($s),Histogram:Zs(Hs),Line:Zs(Hs),Custom:Zs(js)}[t]}function Js(t){return{Re:0,Sw:new Map,Hh:t}}function Qs(t,i){if(void 0!==t&&0!==t.length)return{xw:i.key(t[0].wt),Cw:i.key(t[t.length-1].wt)}}function tn(t){let i;return t.forEach((t=>{void 0===i&&(i=t.dw)})),_(i)}class sn{constructor(t){this.yw=new Map,this.kw=new Map,this.Pw=new Map,this.Tw=[],this.i_=t}m(){this.yw.clear(),this.kw.clear(),this.Pw.clear(),this.Tw=[]}Rw(t,i){let s=0!==this.yw.size,n=!1;const e=this.kw.get(t);if(void 0!==e)if(1===this.kw.size)s=!1,n=!0,this.yw.clear();else for(const i of this.Tw)i.pointData.Sw.delete(t)&&(n=!0);let r=[];if(0!==i.length){const s=i.map((t=>t.time)),e=this.i_.createConverterToInternalObj(i),h=Gs(t.Rr()),a=t.da(),l=t.pa();r=i.map(((i,r)=>{const o=e(i.time),_=this.i_.key(o);let u=this.yw.get(_);void 0===u&&(u=Js(o),this.yw.set(_,u),n=!0);const c=h(o,u.Re,i,s[r],a,l);return u.Sw.set(t,c),c}))}s&&this.Dw(),this.Vw(t,r);let h=-1;if(n){const t=[];this.yw.forEach((i=>{t.push({timeWeight:0,time:i.Hh,pointData:i,originalTime:tn(i.Sw)})})),t.sort(((t,i)=>this.i_.key(t.time)-this.i_.key(i.time))),h=this.Iw(t)}return this.Bw(t,h,function(t,i,s){const n=Qs(t,s),e=Qs(i,s);if(void 0!==n&&void 0!==e)return{Aw:!1,zh:n.Cw>=e.Cw&&n.xw>=e.xw}}(this.kw.get(t),e,this.i_))}yc(t){return this.Rw(t,[])}Ew(t,i,s){const n=i;!function(t){void 0===t.dw&&(t.dw=t.time)}(n),this.i_.preprocessData(i);const e=this.i_.createConverterToInternalObj([i])(i.time),r=this.Pw.get(t);if(!s&&void 0!==r&&this.i_.key(e)<this.i_.key(r))throw new Error(`Cannot update oldest data, last time=${r}, new time=${e}`);let h=this.yw.get(this.i_.key(e));if(s&&void 0===h)throw new Error("Cannot update non-existing data point when historicalUpdate is true");const a=void 0===h;void 0===h&&(h=Js(e),this.yw.set(this.i_.key(e),h));const l=Gs(t.Rr()),o=t.da(),_=t.pa(),u=l(e,h.Re,i,n.dw,o,_);h.Sw.set(t,u),s?this.zw(t,u,h.Re):this.Lw(t,u);const c={zh:Ks(u),Aw:s};if(!a)return this.Bw(t,-1,c);const d={timeWeight:0,time:h.Hh,pointData:h,originalTime:tn(h.Sw)},f=yt(this.Tw,this.i_.key(d.time),((t,i)=>this.i_.key(t.time)<i));this.Tw.splice(f,0,d);for(let t=f;t<this.Tw.length;++t)nn(this.Tw[t].pointData,t);return this.i_.fillWeightsForPoints(this.Tw,f),this.Bw(t,f,c)}Lw(t,i){let s=this.kw.get(t);void 0===s&&(s=[],this.kw.set(t,s));const n=0!==s.length?s[s.length-1]:null;null===n||this.i_.key(i.wt)>this.i_.key(n.wt)?Ks(i)&&s.push(i):Ks(i)?s[s.length-1]=i:s.splice(-1,1),this.Pw.set(t,i.wt)}zw(t,i,s){const n=this.kw.get(t);if(void 0===n)return;const e=yt(n,s,((t,i)=>t.Re<i));Ks(i)?n[e]=i:n.splice(e,1)}Vw(t,i){0!==i.length?(this.kw.set(t,i.filter(Ks)),this.Pw.set(t,i[i.length-1].wt)):(this.kw.delete(t),this.Pw.delete(t))}Dw(){for(const t of this.Tw)0===t.pointData.Sw.size&&this.yw.delete(this.i_.key(t.time))}Iw(t){let i=-1;for(let s=0;s<this.Tw.length&&s<t.length;++s){const n=this.Tw[s],e=t[s];if(this.i_.key(n.time)!==this.i_.key(e.time)){i=s;break}e.timeWeight=n.timeWeight,nn(e.pointData,s)}if(-1===i&&this.Tw.length!==t.length&&(i=Math.min(this.Tw.length,t.length)),-1===i)return-1;for(let s=i;s<t.length;++s)nn(t[s].pointData,s);return this.i_.fillWeightsForPoints(t,i),this.Tw=t,i}Ow(){if(0===this.kw.size)return null;let t=0;return this.kw.forEach((i=>{0!==i.length&&(t=Math.max(t,i[i.length-1].Re))})),t}Bw(t,i,s){const n={Do:new Map,At:{iu:this.Ow()}};if(-1!==i)this.kw.forEach(((i,e)=>{n.Do.set(e,{se:i,Nw:e===t?s:void 0})})),this.kw.has(t)||n.Do.set(t,{se:[],Nw:s}),n.At.Ww=this.Tw,n.At.Fw=i;else{const i=this.kw.get(t);n.Do.set(t,{se:i||[],Nw:s})}return n}}function nn(t,i){t.Re=i,t.Sw.forEach((t=>{t.Re=i}))}function en(t,i){return t.wt<i}function rn(t,i){return i<t.wt}function hn(t,i,s){const n=i.Uh(),e=i.bi(),r=yt(t,n,en),h=kt(t,e,rn);if(!s)return{from:r,to:h};let a=r,l=h;return r>0&&r<t.length&&t[r].wt>=n&&(a=r-1),h>0&&h<t.length&&t[h-1].wt<=e&&(l=h+1),{from:a,to:l}}class an{constructor(t,i,s){this.Hw=!0,this.Uw=!0,this.$w=!0,this.qw=[],this.Yw=null,this.Jn=t,this.Qn=i,this.jw=s}kt(t){this.Hw=!0,"data"===t&&(this.Uw=!0),"options"===t&&(this.$w=!0)}Tt(){return this.Jn.Vt()?(this.Kw(),null===this.Yw?null:this.Xw):null}Zw(){this.qw=this.qw.map((t=>({...t,...this.Jn.Rh().Dr(t.wt)})))}Gw(){this.Yw=null}Kw(){this.Uw&&(this.Jw(),this.Uw=!1),this.$w&&(this.Zw(),this.$w=!1),this.Hw&&(this.Qw(),this.Hw=!1)}Qw(){const t=this.Jn.Wt(),i=this.Qn.At();if(this.Gw(),i.Ki()||t.Ki())return;const s=i.ye();if(null===s)return;if(0===this.Jn.Xs().zr())return;const n=this.Jn.zt();null!==n&&(this.Yw=hn(this.qw,s,this.jw),this.tg(t,i,n.Ft),this.ig())}}class ln{constructor(t,i){this.sg=t,this.Yi=i}nt(t,i,s){this.sg.draw(t,this.Yi,i,s)}}class on extends an{constructor(t,i,s){super(t,i,!1),this.sh=s,this.Xw=new ln(this.sh.renderer(),(i=>{const s=t.zt();return null===s?null:t.Wt().Nt(i,s.Ft)}))}fa(t){return this.sh.priceValueBuilder(t)}va(t){return this.sh.isWhitespace(t)}Jw(){const t=this.Jn.Rh();this.qw=this.Jn.Xs().Hr().map((i=>({wt:i.Re,_t:NaN,...t.Dr(i.Re),ng:i.se})))}tg(t,i){i.su(this.qw,b(this.Yw))}ig(){this.sh.update({bars:this.qw.map(_n),barSpacing:this.Qn.At().lu(),visibleRange:this.Yw},this.Jn.N())}}function _n(t){return{x:t._t,time:t.wt,originalData:t.ng,barColor:t.cr}}const un={color:"#2196f3"},cn=(t,i,s)=>{const n=c(s);return new on(t,i,n)};function dn(t){const i={value:t.Ft[3],time:t.dw};return void 0!==t.bw&&(i.customValues=t.bw),i}function fn(t){const i=dn(t);return void 0!==t.R&&(i.color=t.R),i}function pn(t){const i=dn(t);return void 0!==t.vt&&(i.lineColor=t.vt),void 0!==t.mr&&(i.topColor=t.mr),void 0!==t.wr&&(i.bottomColor=t.wr),i}function vn(t){const i=dn(t);return void 0!==t.gr&&(i.topLineColor=t.gr),void 0!==t.Mr&&(i.bottomLineColor=t.Mr),void 0!==t.br&&(i.topFillColor1=t.br),void 0!==t.Sr&&(i.topFillColor2=t.Sr),void 0!==t.Cr&&(i.bottomFillColor1=t.Cr),void 0!==t.yr&&(i.bottomFillColor2=t.yr),i}function mn(t){const i={open:t.Ft[0],high:t.Ft[1],low:t.Ft[2],close:t.Ft[3],time:t.dw};return void 0!==t.bw&&(i.customValues=t.bw),i}function wn(t){const i=mn(t);return void 0!==t.R&&(i.color=t.R),i}function gn(t){const i=mn(t),{R:s,Ht:n,vr:e}=t;return void 0!==s&&(i.color=s),void 0!==n&&(i.borderColor=n),void 0!==e&&(i.wickColor=e),i}function Mn(t){return{Area:pn,Line:fn,Baseline:vn,Histogram:fn,Bar:wn,Candlestick:gn,Custom:bn}[t]}function bn(t){const i=t.dw;return{...t.se,time:i}}const Sn={vertLine:{color:"#9598A1",width:1,style:3,visible:!0,labelVisible:!0,labelBackgroundColor:"#131722"},horzLine:{color:"#9598A1",width:1,style:3,visible:!0,labelVisible:!0,labelBackgroundColor:"#131722"},mode:1},xn={vertLines:{color:"#D6DCDE",style:0,visible:!0},horzLines:{color:"#D6DCDE",style:0,visible:!0}},Cn={background:{type:"solid",color:"#FFFFFF"},textColor:"#191919",fontSize:12,fontFamily:S,panes:{enableResize:!0,separatorColor:"#E0E3EB",separatorHoverColor:"rgba(178, 181, 189, 0.2)"},attributionLogo:!0,colorSpace:"srgb",colorParsers:[]},yn={autoScale:!0,mode:0,invertScale:!1,alignLabels:!0,borderVisible:!0,borderColor:"#2B2B43",entireTextOnly:!1,visible:!1,ticksVisible:!1,scaleMargins:{bottom:.1,top:.2},minimumWidth:0,ensureEdgeTickMarksVisible:!1},kn={rightOffset:0,barSpacing:6,minBarSpacing:.5,maxBarSpacing:0,fixLeftEdge:!1,fixRightEdge:!1,lockVisibleTimeRangeOnResize:!1,rightBarStaysOnScroll:!1,borderVisible:!0,borderColor:"#2B2B43",visible:!0,timeVisible:!1,secondsVisible:!0,shiftVisibleRangeOnNewBar:!0,allowShiftVisibleRangeOnWhitespaceReplacement:!1,ticksVisible:!1,uniformDistribution:!1,minimumHeight:0,allowBoldLabels:!0,ignoreWhitespaceIndices:!1};function Pn(){return{width:0,height:0,autoSize:!1,layout:Cn,crosshair:Sn,grid:xn,overlayPriceScales:{...yn},leftPriceScale:{...yn,visible:!1},rightPriceScale:{...yn,visible:!0},timeScale:kn,localization:{locale:ss?navigator.language:"",dateFormat:"dd MMM 'yy"},handleScroll:{mouseWheel:!0,pressedMouseMove:!0,horzTouchDrag:!0,vertTouchDrag:!0},handleScale:{axisPressedMouseMove:{time:!0,price:!0},axisDoubleClickReset:{time:!0,price:!0},mouseWheel:!0,pinch:!0},kineticScroll:{mouse:!1,touch:!0},trackingMode:{exitMode:1}}}class Tn{constructor(t,i,s){this.Df=t,this.eg=i,this.rg=s??0}applyOptions(t){this.Df.Qt().Qu(this.eg,t,this.rg)}options(){return this.Yi().N()}width(){return Z(this.eg)?this.Df.Gm(this.eg):0}setVisibleRange(t){this.setAutoScale(!1),this.Yi().Nl(new vt(t.from,t.to))}getVisibleRange(){const t=this.Yi().Qe();return null===t?null:{from:t.$e(),to:t.qe()}}setAutoScale(t){this.applyOptions({autoScale:t})}Yi(){return u(this.Df.Qt().tc(this.eg,this.rg)).Wt}}class Rn{constructor(t,i,s,n){this.Df=t,this.yt=s,this.hg=i,this.ag=n}getHeight(){return this.yt.$t()}setHeight(t){const i=this.Df.Qt(),s=i.Bc(this.yt);i.hc(s,t)}paneIndex(){return this.Df.Qt().Bc(this.yt)}moveTo(t){const i=this.paneIndex();i!==t&&(o(t>=0&&t<this.Df.If().length,"Invalid pane index"),this.Df.Qt().ac(i,t))}getSeries(){return this.yt.Do().map((t=>this.hg(t)))??[]}getHTMLElement(){return this.Df.If()[this.paneIndex()].Of()}attachPrimitive(t){this.yt.ua(t),t.attached&&t.attached({chart:this.ag,requestUpdate:()=>this.yt.Qt().Bh()})}detachPrimitive(t){this.yt.ca(t)}priceScale(t){if(null===this.yt.Co(t))throw new Error(`Cannot find price scale with id: ${t}`);return new Tn(this.Df,t,this.paneIndex())}}const Dn={color:"#FF0000",price:0,lineStyle:2,lineWidth:1,lineVisible:!0,axisLabelVisible:!0,title:"",axisLabelColor:"",axisLabelTextColor:""};class Vn{constructor(t){this.ir=t}applyOptions(t){this.ir.hr(t)}options(){return this.ir.N()}lg(){return this.ir}}class In{constructor(t,i,s,n,e,r){this.og=new d,this.Jn=t,this._g=i,this.ug=s,this.i_=e,this.ag=n,this.cg=r}m(){this.og.m()}priceFormatter(){return this.Jn.ra()}priceToCoordinate(t){const i=this.Jn.zt();return null===i?null:this.Jn.Wt().Nt(t,i.Ft)}coordinateToPrice(t){const i=this.Jn.zt();return null===i?null:this.Jn.Wt().Ts(t,i.Ft)}barsInLogicalRange(t){if(null===t)return null;const i=new Ri(new ki(t.from,t.to)).w_(),s=this.Jn.Xs();if(s.Ki())return null;const n=s.Wr(i.Uh(),1),e=s.Wr(i.bi(),-1),r=u(s.Lr()),h=u(s.Ks());if(null!==n&&null!==e&&n.Re>e.Re)return{barsBefore:t.from-r,barsAfter:h-t.to};const a={barsBefore:null===n||n.Re===r?t.from-r:n.Re-r,barsAfter:null===e||e.Re===h?h-t.to:h-e.Re};return null!==n&&null!==e&&(a.from=n.dw,a.to=e.dw),a}setData(t){this.i_,this.Jn.Rr(),this._g.dg(this.Jn,t),this.fg("full")}update(t,i=!1){this.Jn.Rr(),this._g.pg(this.Jn,t,i),this.fg("update")}dataByIndex(t,i){const s=this.Jn.Xs().Wr(t,i);if(null===s)return null;return Mn(this.seriesType())(s)}data(){const t=Mn(this.seriesType());return this.Jn.Xs().Hr().map((i=>t(i)))}subscribeDataChanged(t){this.og.i(t)}unsubscribeDataChanged(t){this.og._(t)}applyOptions(t){this.Jn.hr(t)}options(){return g(this.Jn.N())}priceScale(){return this.ug.priceScale(this.Jn.Wt().wa(),this.getPane().paneIndex())}createPriceLine(t){const i=f(g(Dn),t),s=this.Jn.Oh(i);return new Vn(s)}removePriceLine(t){this.Jn.Nh(t.lg())}priceLines(){return this.Jn.Wh().map((t=>new Vn(t)))}seriesType(){return this.Jn.Rr()}attachPrimitive(t){this.Jn.ua(t),t.attached&&t.attached({chart:this.ag,series:this,requestUpdate:()=>this.Jn.Qt().Bh(),horzScaleBehavior:this.i_})}detachPrimitive(t){this.Jn.ca(t),t.detached&&t.detached(),this.Jn.Qt().Bh()}getPane(){const t=this.Jn,i=u(this.Jn.Qt().Hn(t));return this.cg(i)}moveToPane(t){this.Jn.Qt().Rc(this.Jn,t)}seriesOrder(){const t=this.Jn.Qt().Hn(this.Jn);return null===t?-1:t.Do().indexOf(this.Jn)}setSeriesOrder(t){const i=this.Jn.Qt().Hn(this.Jn);null!==i&&i.qo(this.Jn,t)}fg(t){this.og.v()&&this.og.p(t)}}class Bn{constructor(t,i,s){this.vg=new d,this.T_=new d,this.rm=new d,this.ts=t,this.uh=t.At(),this.Om=i,this.uh.Su().i(this.mg.bind(this)),this.uh.xu().i(this.wg.bind(this)),this.Om.dm().i(this.gg.bind(this)),this.i_=s}m(){this.uh.Su().u(this),this.uh.xu().u(this),this.Om.dm().u(this),this.vg.m(),this.T_.m(),this.rm.m()}scrollPosition(){return this.uh._u()}scrollToPosition(t,i){i?this.uh.gu(t,1e3):this.ts.fn(t)}scrollToRealTime(){this.uh.wu()}getVisibleRange(){const t=this.uh.K_();return null===t?null:{from:t.from.originalTime,to:t.to.originalTime}}setVisibleRange(t){const i={from:this.i_.convertHorzItemToInternal(t.from),to:this.i_.convertHorzItemToInternal(t.to)},s=this.uh.J_(i);this.ts.Pc(s)}getVisibleLogicalRange(){const t=this.uh.j_();return null===t?null:{from:t.Uh(),to:t.bi()}}setVisibleLogicalRange(t){o(t.from<=t.to,"The from index cannot be after the to index."),this.ts.Pc(t)}resetTimeScale(){this.ts.cn()}fitContent(){this.ts.ku()}logicalToCoordinate(t){const i=this.ts.At();return i.Ki()?null:i.qt(t)}coordinateToLogical(t){return this.uh.Ki()?null:this.uh.nu(t)}timeToIndex(t,i){const s=this.i_.convertHorzItemToInternal(t);return this.uh.U_(s,i)}timeToCoordinate(t){const i=this.timeToIndex(t,!1);return null===i?null:this.uh.qt(i)}coordinateToTime(t){const i=this.ts.At(),s=i.nu(t),n=i.ss(s);return null===n?null:n.originalTime}width(){return this.Om.Nf().width}height(){return this.Om.Nf().height}subscribeVisibleTimeRangeChange(t){this.vg.i(t)}unsubscribeVisibleTimeRangeChange(t){this.vg._(t)}subscribeVisibleLogicalRangeChange(t){this.T_.i(t)}unsubscribeVisibleLogicalRangeChange(t){this.T_._(t)}subscribeSizeChange(t){this.rm.i(t)}unsubscribeSizeChange(t){this.rm._(t)}applyOptions(t){this.uh.hr(t)}options(){return{...g(this.uh.N()),barSpacing:this.uh.lu()}}mg(){this.vg.v()&&this.vg.p(this.getVisibleRange())}wg(){this.T_.v()&&this.T_.p(this.getVisibleLogicalRange())}gg(t){this.rm.p(t.width,t.height)}}function An(t){if(void 0===t||"custom"===t.type)return;const i=t;void 0!==i.minMove&&void 0===i.precision&&(i.precision=function(t){if(t>=1)return 0;let i=0;for(;i<8;i++){const s=Math.round(t);if(Math.abs(s-t)<1e-8)return i;t*=10}return i}(i.minMove))}function En(t){return function(t){if(w(t.handleScale)){const i=t.handleScale;t.handleScale={axisDoubleClickReset:{time:i,price:i},axisPressedMouseMove:{time:i,price:i},mouseWheel:i,pinch:i}}else if(void 0!==t.handleScale){const{axisPressedMouseMove:i,axisDoubleClickReset:s}=t.handleScale;w(i)&&(t.handleScale.axisPressedMouseMove={time:i,price:i}),w(s)&&(t.handleScale.axisDoubleClickReset={time:s,price:s})}const i=t.handleScroll;w(i)&&(t.handleScroll={horzTouchDrag:i,vertTouchDrag:i,mouseWheel:i,pressedMouseMove:i})}(t),t}class zn{constructor(t,i,s){this.Mg=new Map,this.bg=new Map,this.Sg=new d,this.xg=new d,this.Cg=new d,this.zu=new WeakMap,this.yg=new sn(i);const n=void 0===s?g(Pn()):f(g(Pn()),En(s));this.kg=i,this.Df=new Os(t,n,i),this.Df.Lv().i((t=>{this.Sg.v()&&this.Sg.p(this.Pg(t()))}),this),this.Df.Ov().i((t=>{this.xg.v()&&this.xg.p(this.Pg(t()))}),this),this.Df.nc().i((t=>{this.Cg.v()&&this.Cg.p(this.Pg(t()))}),this);const e=this.Df.Qt();this.Tg=new Bn(e,this.Df.Um(),this.kg)}remove(){this.Df.Lv().u(this),this.Df.Ov().u(this),this.Df.nc().u(this),this.Tg.m(),this.Df.m(),this.Mg.clear(),this.bg.clear(),this.Sg.m(),this.xg.m(),this.Cg.m(),this.yg.m()}resize(t,i,s){this.autoSizeActive()||this.Df.Wm(t,i,s)}addCustomSeries(t,i={},s=0){const n=(t=>({type:"Custom",isBuiltIn:!1,defaultOptions:{...un,...t.defaultOptions()},Rg:cn,Dg:t}))(c(t));return this.Vg(n,i,s)}addSeries(t,i={},s=0){return this.Vg(t,i,s)}removeSeries(t){const i=_(this.Mg.get(t)),s=this.yg.yc(i);this.Df.Qt().yc(i),this.Ig(s),this.Mg.delete(t),this.bg.delete(i)}dg(t,i){this.Ig(this.yg.Rw(t,i))}pg(t,i,s){this.Ig(this.yg.Ew(t,i,s))}subscribeClick(t){this.Sg.i(t)}unsubscribeClick(t){this.Sg._(t)}subscribeCrosshairMove(t){this.Cg.i(t)}unsubscribeCrosshairMove(t){this.Cg._(t)}subscribeDblClick(t){this.xg.i(t)}unsubscribeDblClick(t){this.xg._(t)}priceScale(t,i=0){return new Tn(this.Df,t,i)}timeScale(){return this.Tg}applyOptions(t){this.Df.hr(En(t))}options(){return this.Df.N()}takeScreenshot(){return this.Df.Xm()}removePane(t){this.Df.Qt().rc(t)}swapPanes(t,i){this.Df.Qt().ac(t,i)}autoSizeActive(){return this.Df.tw()}chartElement(){return this.Df.Uf()}panes(){return this.Df.Qt().$s().map((t=>this.Bg(t)))}paneSize(t=0){const i=this.Df.nw(t);return{height:i.height,width:i.width}}setCrosshairPosition(t,i,s){const n=this.Mg.get(s);if(void 0===n)return;const e=this.Df.Qt().Hn(n);null!==e&&this.Df.Qt().gc(t,i,e)}clearCrosshairPosition(){this.Df.Qt().Mc(!0)}horzBehaviour(){return this.kg}Vg(t,i={},s=0){o(void 0!==t.Rg),An(i.priceFormat),"Candlestick"===t.type&&function(t){void 0!==t.borderColor&&(t.borderUpColor=t.borderColor,t.borderDownColor=t.borderColor),void 0!==t.wickColor&&(t.wickUpColor=t.wickColor,t.wickDownColor=t.wickColor)}(i);const n=f(g(e),g(t.defaultOptions),i),r=t.Rg,h=new Yt(this.Df.Qt(),t.type,n,r,t.Dg);this.Df.Qt().xc(h,s);const a=new In(h,this,this,this,this.kg,(t=>this.Bg(t)));return this.Mg.set(a,h),this.bg.set(h,a),a}Ig(t){const i=this.Df.Qt();i.bc(t.At.iu,t.At.Ww,t.At.Fw),t.Do.forEach(((t,i)=>i.ht(t.se,t.Nw))),i.At().O_(),i.au()}Ag(t){return _(this.bg.get(t))}Pg(t){const i=new Map;t.ww.forEach(((t,s)=>{const n=s.Rr(),e=Mn(n)(t);if("Custom"!==n)o(Fs(e));else{const t=s.pa();o(!t||!1===t(e))}i.set(this.Ag(s),e)}));const s=void 0!==t.mw&&this.bg.has(t.mw)?this.Ag(t.mw):void 0;return{time:t.dw,logical:t.Re,point:t.fw,paneIndex:t.pw,hoveredSeries:s,hoveredObjectId:t.gw,seriesData:i,sourceEvent:t.Mw}}Bg(t){let i=this.zu.get(t);return i||(i=new Rn(this.Df,(t=>this.Ag(t)),t,this),this.zu.set(t,i)),i}}function Ln(t){if(m(t)){const i=document.getElementById(t);return o(null!==i,`Cannot find element in DOM with id=${t}`),i}return t}function On(t,i,s){const n=Ln(t),e=new zn(n,i,s);return i.setOptions(e.options()),e}function Nn(t,i){return On(t,new is,is.Xc(i))}function Wn(){return is}class Fn extends an{constructor(t,i){super(t,i,!0)}tg(t,i,s){i.su(this.qw,b(this.Yw)),t.Hl(this.qw,s,b(this.Yw))}Eg(t,i){return{wt:t,gt:i,_t:NaN,ut:NaN}}Jw(){const t=this.Jn.Rh();this.qw=this.Jn.Xs().Hr().map((i=>{const s=i.Ft[3];return this.zg(i.Re,s,t)}))}}function Hn(t,i,s,n,e,r,h){if(0===i.length||n.from>=i.length||n.to<=0)return;const{context:a,horizontalPixelRatio:l,verticalPixelRatio:o}=t,_=i[n.from];let u=r(t,_),c=_;if(n.to-n.from<2){const i=e/2;a.beginPath();const s={_t:_._t-i,ut:_.ut},n={_t:_._t+i,ut:_.ut};a.moveTo(s._t*l,s.ut*o),a.lineTo(n._t*l,n.ut*o),h(t,u,s,n)}else{const e=(i,s)=>{h(t,u,c,s),a.beginPath(),u=i,c=s};let d=c;a.beginPath(),a.moveTo(_._t*l,_.ut*o);for(let h=n.from+1;h<n.to;++h){d=i[h];const n=r(t,d);switch(s){case 0:a.lineTo(d._t*l,d.ut*o);break;case 1:a.lineTo(d._t*l,i[h-1].ut*o),n!==u&&(e(n,d),a.lineTo(d._t*l,i[h-1].ut*o)),a.lineTo(d._t*l,d.ut*o);break;case 2:{const[t,s]=Yn(i,h-1,h);a.bezierCurveTo(t._t*l,t.ut*o,s._t*l,s.ut*o,d._t*l,d.ut*o);break}}1!==s&&n!==u&&(e(n,d),a.moveTo(d._t*l,d.ut*o))}(c!==d||c===d&&1===s)&&h(t,u,c,d)}}const Un=6;function $n(t,i){return{_t:t._t-i._t,ut:t.ut-i.ut}}function qn(t,i){return{_t:t._t/i,ut:t.ut/i}}function Yn(t,i,s){const n=Math.max(0,i-1),e=Math.min(t.length-1,s+1);var r,h;return[(r=t[i],h=qn($n(t[s],t[n]),Un),{_t:r._t+h._t,ut:r.ut+h.ut}),$n(t[s],qn($n(t[e],t[i]),Un))]}function jn(t,i){const s=t.context;s.strokeStyle=i,s.stroke()}class Kn extends R{constructor(){super(...arguments),this.rt=null}ht(t){this.rt=t}et(t){if(null===this.rt)return;const{ot:i,lt:s,Lg:n,Og:e,ct:r,Xt:h,Ng:l}=this.rt;if(null===s)return;const o=t.context;o.lineCap="butt",o.lineWidth=r*t.verticalPixelRatio,a(o,h),o.lineJoin="round";const _=this.Wg.bind(this);void 0!==e&&Hn(t,i,e,s,n,_,jn),l&&function(t,i,s,n,e){if(n.to-n.from<=0)return;const{horizontalPixelRatio:r,verticalPixelRatio:h,context:a}=t;let l=null;const o=Math.max(1,Math.floor(r))%2/2,_=s*h+o;for(let s=n.to-1;s>=n.from;--s){const n=i[s];if(n){const i=e(t,n);i!==l&&(a.beginPath(),null!==l&&a.fill(),a.fillStyle=i,l=i);const s=Math.round(n._t*r)+o,u=n.ut*h;a.moveTo(s,u),a.arc(s,u,_,0,2*Math.PI)}}a.fill()}(t,i,l,s,_)}}class Xn extends Kn{Wg(t,i){return i.vt}}class Zn extends Fn{constructor(){super(...arguments),this.Xw=new Xn}zg(t,i,s){return{...this.Eg(t,i),...s.Dr(t)}}ig(){const t=this.Jn.N(),i={ot:this.qw,Xt:t.lineStyle,Og:t.lineVisible?t.lineType:void 0,ct:t.lineWidth,Ng:t.pointMarkersVisible?t.pointMarkersRadius||t.lineWidth/2+2:void 0,lt:this.Yw,Lg:this.Qn.At().lu()};this.Xw.ht(i)}}const Gn={type:"Line",isBuiltIn:!0,defaultOptions:{color:"#2196f3",lineStyle:0,lineWidth:3,lineType:0,lineVisible:!0,crosshairMarkerVisible:!0,crosshairMarkerRadius:4,crosshairMarkerBorderColor:"",crosshairMarkerBorderWidth:2,crosshairMarkerBackgroundColor:"",lastPriceAnimation:0,pointMarkersVisible:!1},Rg:(t,i)=>new Zn(t,i)};function Jn(t,i){return t.weight>i.weight?t:i}class Qn{constructor(){this.Fg=new d,this.Hg=function(t){let i=!1;return function(...s){i||(i=!0,queueMicrotask((()=>{t(...s),i=!1})))}}((()=>this.Fg.p(this.Ug))),this.Ug=0}$g(){return this.Fg}m(){this.Fg.m()}options(){return this.ys}setOptions(t){this.ys=t}preprocessData(t){}updateFormatter(t){this.ys&&(this.ys.localization=t)}createConverterToInternalObj(t){return this.Hg(),t=>(t>this.Ug&&(this.Ug=t),t)}key(t){return t}cacheKey(t){return t}convertHorzItemToInternal(t){return t}formatHorzItem(t){return this.qg(t)}formatTickmark(t){return this.qg(t.time)}maxTickMarkWeight(t){return t.reduce(Jn,t[0]).weight}fillWeightsForPoints(t,i){for(let n=i;n<t.length;++n)t[n].timeWeight=(s=t[n].time)%120==0?10:s%60==0?9:s%36==0?8:s%12==0?7:s%6==0?6:s%3==0?5:s%1==0?4:0;var s;this.Ug=t[t.length-1].time,this.Hg()}qg(t){if(this.ys.localization?.timeFormatter)return this.ys.localization.timeFormatter(t);if(t<12)return`${t}M`;const i=Math.floor(t/12),s=t%12;return 0===s?`${i}Y`:`${i}Y${s}M`}}const te={yieldCurve:{baseResolution:1,minimumTimeRange:120,startTimeRange:0},timeScale:{ignoreWhitespaceIndices:!0},leftPriceScale:{visible:!0},rightPriceScale:{visible:!1},localization:{priceFormatter:t=>t.toFixed(3)+"%"}},ie={lastValueVisible:!1,priceLineVisible:!1};class se extends zn{constructor(t,i){const s=f(te,i||{}),n=new Qn;super(t,n,s),n.setOptions(this.options()),this._initWhitespaceSeries()}addSeries(t,i={},s=0){if(t.isBuiltIn&&!1===["Area","Line"].includes(t.type))throw new Error("Yield curve only support Area and Line series");const n={...ie,...i};return super.addSeries(t,n,s)}_initWhitespaceSeries(){const t=this.horzBehaviour(),i=this.addSeries(Gn);let s;function n(n){const e=function(t,i){return{le:Math.max(0,t.startTimeRange),oe:Math.max(0,t.minimumTimeRange,i||0),Yg:Math.max(1,t.baseResolution)}}(t.options().yieldCurve,n),r=(({le:t,oe:i,Yg:s})=>`${t}~${i}~${s}`)(e);r!==s&&(s=r,i.setData(function({le:t,oe:i,Yg:s}){return Array.from({length:Math.floor((i-t)/s)+1},((i,n)=>({time:t+n*s})))}(e)))}n(0),t.$g().i(n)}}function ne(t,i){const s=Ln(t);return new se(s,i)}function ee(t,i){return t.weight>i.weight?t:i}class re{options(){return this.ys}setOptions(t){this.ys=t}preprocessData(t){}updateFormatter(t){this.ys&&(this.ys.localization=t)}createConverterToInternalObj(t){return t=>t}key(t){return t}cacheKey(t){return t}convertHorzItemToInternal(t){return t}formatHorzItem(t){return t.toFixed(this.Cn())}formatTickmark(t,i){return t.time.toFixed(this.Cn())}maxTickMarkWeight(t){return t.reduce(ee,t[0]).weight}fillWeightsForPoints(t,i){for(let n=i;n<t.length;++n)t[n].timeWeight=(s=t[n].time)===100*Math.ceil(s/100)?8:s===50*Math.ceil(s/50)?7:s===25*Math.ceil(s/25)?6:s===10*Math.ceil(s/10)?5:s===5*Math.ceil(s/5)?4:s===Math.ceil(s)?3:2*s===Math.ceil(2*s)?1:0;var s}Cn(){return this.ys.localization.precision}}function he(t,i){return On(t,new re,i)}function ae(t,i,s,n,e){const{context:r,horizontalPixelRatio:h,verticalPixelRatio:a}=i;r.lineTo(e._t*h,t*a),r.lineTo(n._t*h,t*a),r.closePath(),r.fillStyle=s,r.fill()}class le extends R{constructor(){super(...arguments),this.rt=null}ht(t){this.rt=t}et(t){if(null===this.rt)return;const{ot:i,lt:s,Lg:n,ct:e,Xt:r,Og:h}=this.rt,l=this.rt.jg??(this.rt.Kg?0:t.mediaSize.height);if(null===s)return;const o=t.context;o.lineCap="butt",o.lineJoin="round",o.lineWidth=e,a(o,r),o.lineWidth=1,Hn(t,i,h,s,n,this.Xg.bind(this),ae.bind(null,l))}}class oe{Zg(t,i){const s=this.Gg,{Jg:n,Qg:e,tM:r,iM:h,jg:a,sM:l,nM:o}=i;if(void 0===this.eM||void 0===s||s.Jg!==n||s.Qg!==e||s.tM!==r||s.iM!==h||s.jg!==a||s.sM!==l||s.nM!==o){const{verticalPixelRatio:s}=t,_=a||l>0?s:1,u=l*_,c=o===t.bitmapSize.height?o:o*_,d=(a??0)*_,f=t.context.createLinearGradient(0,u,0,c);if(f.addColorStop(0,n),null!=a){const t=Zt((d-u)/(c-u),0,1);f.addColorStop(t,e),f.addColorStop(t,r)}f.addColorStop(1,h),this.eM=f,this.Gg=i}return this.eM}}class _e extends le{constructor(){super(...arguments),this.rM=new oe}Xg(t,i){const s=this.rt;return this.rM.Zg(t,{Jg:i.br,Qg:i.Sr,tM:i.Cr,iM:i.yr,jg:s.jg,sM:s.sM??0,nM:s.nM??t.bitmapSize.height})}}class ue extends Kn{constructor(){super(...arguments),this.hM=new oe}Wg(t,i){const s=this.rt;return this.hM.Zg(t,{Jg:i.gr,Qg:i.gr,tM:i.Mr,iM:i.Mr,jg:s.jg,sM:s.sM??0,nM:s.nM??t.bitmapSize.height})}}class ce extends Fn{constructor(t,i){super(t,i),this.Xw=new T,this.aM=new _e,this.lM=new ue,this.Xw.st([this.aM,this.lM])}zg(t,i,s){return{...this.Eg(t,i),...s.Dr(t)}}ig(){const t=this.Jn.zt();if(null===t)return;const i=this.Jn.N(),s=this.Jn.Wt().Nt(i.baseValue.price,t.Ft),n=this.Qn.At().lu();if(null===this.Yw||0===this.qw.length)return;let e,r;if(i.relativeGradient){e=this.qw[this.Yw.from].ut,r=this.qw[this.Yw.from].ut;for(let t=this.Yw.from;t<this.Yw.to;t++){const i=this.qw[t];i.ut<e&&(e=i.ut),i.ut>r&&(r=i.ut)}}this.aM.ht({ot:this.qw,ct:i.lineWidth,Xt:i.lineStyle,Og:i.lineType,jg:s,sM:e,nM:r,Kg:!1,lt:this.Yw,Lg:n}),this.lM.ht({ot:this.qw,ct:i.lineWidth,Xt:i.lineStyle,Og:i.lineVisible?i.lineType:void 0,Ng:i.pointMarkersVisible?i.pointMarkersRadius||i.lineWidth/2+2:void 0,jg:s,sM:e,nM:r,lt:this.Yw,Lg:n})}}const de={type:"Baseline",isBuiltIn:!0,defaultOptions:{baseValue:{type:"price",price:0},relativeGradient:!1,topFillColor1:"rgba(38, 166, 154, 0.28)",topFillColor2:"rgba(38, 166, 154, 0.05)",topLineColor:"rgba(38, 166, 154, 1)",bottomFillColor1:"rgba(239, 83, 80, 0.05)",bottomFillColor2:"rgba(239, 83, 80, 0.28)",bottomLineColor:"rgba(239, 83, 80, 1)",lineWidth:3,lineStyle:0,lineType:0,lineVisible:!0,crosshairMarkerVisible:!0,crosshairMarkerRadius:4,crosshairMarkerBorderColor:"",crosshairMarkerBorderWidth:2,crosshairMarkerBackgroundColor:"",lastPriceAnimation:0,pointMarkersVisible:!1},Rg:(t,i)=>new ce(t,i)};class fe extends le{constructor(){super(...arguments),this.rM=new oe}Xg(t,i){return this.rM.Zg(t,{Jg:i.mr,Qg:"",tM:"",iM:i.wr,sM:this.rt?.sM??0,nM:t.bitmapSize.height})}}class pe extends Fn{constructor(t,i){super(t,i),this.Xw=new T,this.oM=new fe,this._M=new Xn,this.Xw.st([this.oM,this._M])}zg(t,i,s){return{...this.Eg(t,i),...s.Dr(t)}}ig(){const t=this.Jn.N();if(null===this.Yw||0===this.qw.length)return;let i;if(t.relativeGradient){i=this.qw[this.Yw.from].ut;for(let t=this.Yw.from;t<this.Yw.to;t++){const s=this.qw[t];s.ut<i&&(i=s.ut)}}this.oM.ht({Og:t.lineType,ot:this.qw,Xt:t.lineStyle,ct:t.lineWidth,jg:null,sM:i,Kg:t.invertFilledArea,lt:this.Yw,Lg:this.Qn.At().lu()}),this._M.ht({Og:t.lineVisible?t.lineType:void 0,ot:this.qw,Xt:t.lineStyle,ct:t.lineWidth,lt:this.Yw,Lg:this.Qn.At().lu(),Ng:t.pointMarkersVisible?t.pointMarkersRadius||t.lineWidth/2+2:void 0})}}const ve={type:"Area",isBuiltIn:!0,defaultOptions:{topColor:"rgba( 46, 220, 135, 0.4)",bottomColor:"rgba( 40, 221, 100, 0)",invertFilledArea:!1,relativeGradient:!1,lineColor:"#33D778",lineStyle:0,lineWidth:3,lineType:0,lineVisible:!0,crosshairMarkerVisible:!0,crosshairMarkerRadius:4,crosshairMarkerBorderColor:"",crosshairMarkerBorderWidth:2,crosshairMarkerBackgroundColor:"",lastPriceAnimation:0,pointMarkersVisible:!1},Rg:(t,i)=>new pe(t,i)};class me extends R{constructor(){super(...arguments),this.Yt=null,this.uM=0,this.cM=0}ht(t){this.Yt=t}et({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){if(null===this.Yt||0===this.Yt.Xs.length||null===this.Yt.lt)return;if(this.uM=this.dM(i),this.uM>=2){Math.max(1,Math.floor(i))%2!=this.uM%2&&this.uM--}this.cM=this.Yt.fM?Math.min(this.uM,Math.floor(i)):this.uM;let n=null;const e=this.cM<=this.uM&&this.Yt.lu>=Math.floor(1.5*i);for(let r=this.Yt.lt.from;r<this.Yt.lt.to;++r){const h=this.Yt.Xs[r];n!==h.cr&&(t.fillStyle=h.cr,n=h.cr);const a=Math.floor(.5*this.cM),l=Math.round(h._t*i),o=l-a,_=this.cM,u=o+_-1,c=Math.min(h.Yl,h.jl),d=Math.max(h.Yl,h.jl),f=Math.round(c*s)-a,p=Math.round(d*s)+a,v=Math.max(p-f,this.cM);t.fillRect(o,f,_,v);const m=Math.ceil(1.5*this.uM);if(e){if(this.Yt.pM){const i=l-m;let n=Math.max(f,Math.round(h.ql*s)-a),e=n+_-1;e>f+v-1&&(e=f+v-1,n=e-_+1),t.fillRect(i,n,o-i,e-n+1)}const i=l+m;let n=Math.max(f,Math.round(h.Kl*s)-a),e=n+_-1;e>f+v-1&&(e=f+v-1,n=e-_+1),t.fillRect(u+1,n,i-u,e-n+1)}}}dM(t){const i=Math.floor(t);return Math.max(i,Math.floor(function(t,i){return Math.floor(.3*t*i)}(u(this.Yt).lu,t)))}}class we extends an{constructor(t,i){super(t,i,!1)}tg(t,i,s){i.su(this.qw,b(this.Yw)),t.$l(this.qw,s,b(this.Yw))}vM(t,i,s){return{wt:t,qh:i.Ft[0],Yh:i.Ft[1],jh:i.Ft[2],Kh:i.Ft[3],_t:NaN,ql:NaN,Yl:NaN,jl:NaN,Kl:NaN}}Jw(){const t=this.Jn.Rh();this.qw=this.Jn.Xs().Hr().map((i=>this.zg(i.Re,i,t)))}}class ge extends we{constructor(){super(...arguments),this.Xw=new me}zg(t,i,s){return{...this.vM(t,i,s),...s.Dr(t)}}ig(){const t=this.Jn.N();this.Xw.ht({Xs:this.qw,lu:this.Qn.At().lu(),pM:t.openVisible,fM:t.thinBars,lt:this.Yw})}}const Me={type:"Bar",isBuiltIn:!0,defaultOptions:{upColor:"#26a69a",downColor:"#ef5350",openVisible:!0,thinBars:!0},Rg:(t,i)=>new ge(t,i)};class be extends R{constructor(){super(...arguments),this.Yt=null,this.uM=0}ht(t){this.Yt=t}et(t){if(null===this.Yt||0===this.Yt.Xs.length||null===this.Yt.lt)return;const{horizontalPixelRatio:i}=t;if(this.uM=function(t,i){if(t>=2.5&&t<=4)return Math.floor(3*i);const s=1-.2*Math.atan(Math.max(4,t)-4)/(.5*Math.PI),n=Math.floor(t*s*i),e=Math.floor(t*i),r=Math.min(n,e);return Math.max(Math.floor(i),r)}(this.Yt.lu,i),this.uM>=2){Math.floor(i)%2!=this.uM%2&&this.uM--}const s=this.Yt.Xs;this.Yt.mM&&this.wM(t,s,this.Yt.lt),this.Yt.Mi&&this.Qp(t,s,this.Yt.lt);const n=this.gM(i);(!this.Yt.Mi||this.uM>2*n)&&this.MM(t,s,this.Yt.lt)}wM(t,i,s){if(null===this.Yt)return;const{context:n,horizontalPixelRatio:e,verticalPixelRatio:r}=t;let h="",a=Math.min(Math.floor(e),Math.floor(this.Yt.lu*e));a=Math.max(Math.floor(e),Math.min(a,this.uM));const l=Math.floor(.5*a);let o=null;for(let t=s.from;t<s.to;t++){const s=i[t];s.pr!==h&&(n.fillStyle=s.pr,h=s.pr);const _=Math.round(Math.min(s.ql,s.Kl)*r),u=Math.round(Math.max(s.ql,s.Kl)*r),c=Math.round(s.Yl*r),d=Math.round(s.jl*r);let f=Math.round(e*s._t)-l;const p=f+a-1;null!==o&&(f=Math.max(o+1,f),f=Math.min(f,p));const v=p-f+1;n.fillRect(f,c,v,_-c),n.fillRect(f,u+1,v,d-u),o=p}}gM(t){let i=Math.floor(1*t);this.uM<=2*i&&(i=Math.floor(.5*(this.uM-1)));const s=Math.max(Math.floor(t),i);return this.uM<=2*s?Math.max(Math.floor(t),Math.floor(1*t)):s}Qp(t,i,s){if(null===this.Yt)return;const{context:n,horizontalPixelRatio:e,verticalPixelRatio:r}=t;let h="";const a=this.gM(e);let l=null;for(let t=s.from;t<s.to;t++){const s=i[t];s.dr!==h&&(n.fillStyle=s.dr,h=s.dr);let o=Math.round(s._t*e)-Math.floor(.5*this.uM);const _=o+this.uM-1,u=Math.round(Math.min(s.ql,s.Kl)*r),c=Math.round(Math.max(s.ql,s.Kl)*r);if(null!==l&&(o=Math.max(l+1,o),o=Math.min(o,_)),this.Yt.lu*e>2*a)z(n,o,u,_-o+1,c-u+1,a);else{const t=_-o+1;n.fillRect(o,u,t,c-u+1)}l=_}}MM(t,i,s){if(null===this.Yt)return;const{context:n,horizontalPixelRatio:e,verticalPixelRatio:r}=t;let h="";const a=this.gM(e);for(let t=s.from;t<s.to;t++){const s=i[t];let l=Math.round(Math.min(s.ql,s.Kl)*r),o=Math.round(Math.max(s.ql,s.Kl)*r),_=Math.round(s._t*e)-Math.floor(.5*this.uM),u=_+this.uM-1;if(s.cr!==h){const t=s.cr;n.fillStyle=t,h=t}this.Yt.Mi&&(_+=a,l+=a,u-=a,o-=a),l>o||n.fillRect(_,l,u-_+1,o-l+1)}}}class Se extends we{constructor(){super(...arguments),this.Xw=new be}zg(t,i,s){return{...this.vM(t,i,s),...s.Dr(t)}}ig(){const t=this.Jn.N();this.Xw.ht({Xs:this.qw,lu:this.Qn.At().lu(),mM:t.wickVisible,Mi:t.borderVisible,lt:this.Yw})}}const xe={type:"Candlestick",isBuiltIn:!0,defaultOptions:{upColor:"#26a69a",downColor:"#ef5350",wickVisible:!0,borderVisible:!0,borderColor:"#378658",borderUpColor:"#26a69a",borderDownColor:"#ef5350",wickColor:"#737375",wickUpColor:"#26a69a",wickDownColor:"#ef5350"},Rg:(t,i)=>new Se(t,i)};class Ce extends R{constructor(){super(...arguments),this.Yt=null,this.bM=[]}ht(t){this.Yt=t,this.bM=[]}et({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){if(null===this.Yt||0===this.Yt.ot.length||null===this.Yt.lt)return;this.bM.length||this.SM(i);const n=Math.max(1,Math.floor(s)),e=Math.round(this.Yt.xM*s)-Math.floor(n/2),r=e+n;for(let i=this.Yt.lt.from;i<this.Yt.lt.to;i++){const h=this.Yt.ot[i],a=this.bM[i-this.Yt.lt.from],l=Math.round(h.ut*s);let o,_;t.fillStyle=h.cr,l<=e?(o=l,_=r):(o=e,_=l-Math.floor(n/2)+n),t.fillRect(a.Uh,o,a.bi-a.Uh+1,_-o)}}SM(t){if(null===this.Yt||0===this.Yt.ot.length||null===this.Yt.lt)return void(this.bM=[]);const i=Math.ceil(this.Yt.lu*t)<=1?0:Math.max(1,Math.floor(t)),s=Math.round(this.Yt.lu*t)-i;this.bM=new Array(this.Yt.lt.to-this.Yt.lt.from);for(let i=this.Yt.lt.from;i<this.Yt.lt.to;i++){const n=this.Yt.ot[i],e=Math.round(n._t*t);let r,h;if(s%2){const t=(s-1)/2;r=e-t,h=e+t}else{const t=s/2;r=e-t,h=e+t-1}this.bM[i-this.Yt.lt.from]={Uh:r,bi:h,CM:e,ne:n._t*t,wt:n.wt}}for(let t=this.Yt.lt.from+1;t<this.Yt.lt.to;t++){const s=this.bM[t-this.Yt.lt.from],n=this.bM[t-this.Yt.lt.from-1];s.wt===n.wt+1&&(s.Uh-n.bi!==i+1&&(n.CM>n.ne?n.bi=s.Uh-i-1:s.Uh=n.bi+i+1))}let n=Math.ceil(this.Yt.lu*t);for(let t=this.Yt.lt.from;t<this.Yt.lt.to;t++){const i=this.bM[t-this.Yt.lt.from];i.bi<i.Uh&&(i.bi=i.Uh);const s=i.bi-i.Uh+1;n=Math.min(s,n)}if(i>0&&n<4)for(let t=this.Yt.lt.from;t<this.Yt.lt.to;t++){const i=this.bM[t-this.Yt.lt.from];i.bi-i.Uh+1>n&&(i.CM>i.ne?i.bi-=1:i.Uh+=1)}}}class ye extends Fn{constructor(){super(...arguments),this.Xw=new Ce}zg(t,i,s){return{...this.Eg(t,i),...s.Dr(t)}}ig(){const t={ot:this.qw,lu:this.Qn.At().lu(),lt:this.Yw,xM:this.Jn.Wt().Nt(this.Jn.N().base,u(this.Jn.zt()).Ft)};this.Xw.ht(t)}}const ke={type:"Histogram",isBuiltIn:!0,defaultOptions:{color:"#26a69a",base:0},Rg:(t,i)=>new ye(t,i)};class Pe{constructor(t,i){this.yt=t,this.yM=i,this.kM()}detach(){this.yt.detachPrimitive(this.yM)}getPane(){return this.yt}applyOptions(t){this.yM.hr?.(t)}kM(){this.yt.attachPrimitive(this.yM)}}const Te={visible:!0,horzAlign:"center",vertAlign:"center",lines:[]},Re={color:"rgba(0, 0, 0, 0.5)",fontSize:48,fontFamily:S,fontStyle:"",text:""};class De{constructor(t){this.PM=new Map,this.Yt=t}draw(t){t.useMediaCoordinateSpace((t=>{if(!this.Yt.visible)return;const{context:i,mediaSize:s}=t;let n=0;for(const t of this.Yt.lines){if(0===t.text.length)continue;i.font=t.P;const e=this.TM(i,t.text);e>s.width?t.pu=s.width/e:t.pu=1,n+=t.lineHeight*t.pu}let e=0;switch(this.Yt.vertAlign){case"top":e=0;break;case"center":e=Math.max((s.height-n)/2,0);break;case"bottom":e=Math.max(s.height-n,0)}for(const t of this.Yt.lines){i.save(),i.fillStyle=t.color;let n=0;switch(this.Yt.horzAlign){case"left":i.textAlign="left",n=t.lineHeight/2;break;case"center":i.textAlign="center",n=s.width/2;break;case"right":i.textAlign="right",n=s.width-1-t.lineHeight/2}i.translate(n,e),i.textBaseline="top",i.font=t.P,i.scale(t.pu,t.pu),i.fillText(t.text,0,t.RM),i.restore(),e+=t.lineHeight*t.pu}}))}TM(t,i){const s=this.DM(t.font);let n=s.get(i);return void 0===n&&(n=t.measureText(i).width,s.set(i,n)),n}DM(t){let i=this.PM.get(t);return void 0===i&&(i=new Map,this.PM.set(t,i)),i}}class Ve{constructor(t){this.ys=Be(t)}kt(t){this.ys=Be(t)}renderer(){return new De(this.ys)}}function Ie(t){return{...t,P:x(t.fontSize,t.fontFamily,t.fontStyle),lineHeight:t.lineHeight||1.2*t.fontSize,RM:0,pu:0}}function Be(t){return{...t,lines:t.lines.map(Ie)}}function Ae(t){return{...Re,...t}}function Ee(t){return{...Te,...t,lines:t.lines?.map(Ae)??[]}}class ze{constructor(t){this.ys=Ee(t),this.VM=[new Ve(this.ys)]}updateAllViews(){this.VM.forEach((t=>t.kt(this.ys)))}paneViews(){return this.VM}attached({requestUpdate:t}){this.IM=t}detached(){this.IM=void 0}hr(t){this.ys=Ee({...this.ys,...t}),this.IM&&this.IM()}}function Le(t,i){return new Pe(t,new ze(i))}const Oe={alpha:1,padding:0};class Ne{constructor(t){this.Yt=t}draw(t){t.useMediaCoordinateSpace((t=>{const i=t.context,s=this.BM(this.Yt,t.mediaSize);s&&this.Yt.AM&&(i.globalAlpha=this.Yt.alpha??1,i.drawImage(this.Yt.AM,s._t,s.ut,s.Qi,s.$t))}))}BM(t,i){const{maxHeight:s,maxWidth:n,EM:e,zM:r,padding:h}=t,a=Math.round(i.width/2),l=Math.round(i.height/2),o=h??0;let _=i.width-2*o,u=i.height-2*o;s&&(u=Math.min(u,s)),n&&(_=Math.min(_,n));const c=_/r,d=u/e,f=Math.min(c,d),p=r*f,v=e*f;return{_t:a-.5*p,ut:l-.5*v,$t:v,Qi:p}}}class We{constructor(t){this.LM=null,this.OM=0,this.NM=0,this.ys=t,this.M=Fe(this.ys,this.LM,this.OM,this.NM)}WM(t){void 0!==t.FM&&(this.OM=t.FM),void 0!==t.HM&&(this.NM=t.HM),void 0!==t.UM&&(this.LM=t.UM),this.kt()}$M(t){this.ys=t,this.kt()}zOrder(){return"bottom"}kt(){this.M=Fe(this.ys,this.LM,this.OM,this.NM)}renderer(){return new Ne(this.M)}}function Fe(t,i,s,n){return{...t,AM:i,zM:s,EM:n}}function He(t){return{...Oe,...t}}class Ue{constructor(t,i){this.qM=null,this.YM=t,this.ys=He(i),this.VM=[new We(this.ys)]}updateAllViews(){this.VM.forEach((t=>t.kt()))}paneViews(){return this.VM}attached(t){const{requestUpdate:i}=t;this.jM=i,this.qM=new Image,this.qM.onload=()=>{const t=this.qM?.naturalHeight??1,i=this.qM?.naturalWidth??1;this.VM.forEach((s=>s.WM({HM:t,FM:i,UM:this.qM}))),this.jM&&this.jM()},this.qM.src=this.YM}detached(){this.jM=void 0,this.qM=null}hr(t){this.ys=He({...this.ys,...t}),this.KM(),this.IM&&this.IM()}IM(){this.jM&&this.jM()}KM(){this.VM.forEach((t=>t.$M(this.ys)))}}function $e(t,i,s){return new Pe(t,new Ue(i,s))}class qe{constructor(t,i){this.Jn=t,this.ah=i,this.kM()}detach(){this.Jn.detachPrimitive(this.ah)}getSeries(){return this.Jn}applyOptions(t){this.ah&&this.ah.hr&&this.ah.hr(t)}kM(){this.Jn.attachPrimitive(this.ah)}}const Ye={zOrder:"normal"};function je(t,i){return Jt(Math.min(Math.max(t,12),30)*i)}function Ke(t,i){switch(t){case"arrowDown":case"arrowUp":return je(i,1);case"circle":return je(i,.8);case"square":return je(i,.7)}}function Xe(t){return function(t){const i=Math.ceil(t);return i%2!=0?i-1:i}(je(t,1))}function Ze(t){return Math.max(je(t,.1),3)}function Ge(t,i,s){return i?t:s?Math.ceil(t/2):0}function Je(t,i,s,n){const e=(Ke("arrowUp",n)-1)/2*s.XM,r=(Jt(n/2)-1)/2*s.XM;i.beginPath(),t?(i.moveTo(s._t-e,s.ut),i.lineTo(s._t,s.ut-e),i.lineTo(s._t+e,s.ut),i.lineTo(s._t+r,s.ut),i.lineTo(s._t+r,s.ut+e),i.lineTo(s._t-r,s.ut+e),i.lineTo(s._t-r,s.ut)):(i.moveTo(s._t-e,s.ut),i.lineTo(s._t,s.ut+e),i.lineTo(s._t+e,s.ut),i.lineTo(s._t+r,s.ut),i.lineTo(s._t+r,s.ut-e),i.lineTo(s._t-r,s.ut-e),i.lineTo(s._t-r,s.ut)),i.fill()}function Qe(t,i,s,n,e,r){const h=(Ke("arrowUp",n)-1)/2,a=(Jt(n/2)-1)/2;if(e>=i-a-2&&e<=i+a+2&&r>=(t?s:s-h)-2&&r<=(t?s+h:s)+2)return!0;return(()=>{if(e<i-h-3||e>i+h+3||r<(t?s-h-3:s)||r>(t?s:s+h+3))return!1;const n=Math.abs(e-i);return Math.abs(r-s)+3>=n/2})()}class tr{constructor(){this.Yt=null,this.On=new et,this.W=-1,this.F="",this.Rp="",this.ZM="normal"}ht(t){this.Yt=t}Nn(t,i,s){this.W===t&&this.F===i||(this.W=t,this.F=i,this.Rp=x(t,i),this.On.In()),this.ZM=s}jn(t,i){if(null===this.Yt||null===this.Yt.lt)return null;for(let s=this.Yt.lt.from;s<this.Yt.lt.to;s++){const n=this.Yt.ot[s];if(n&&sr(n,t,i))return{zOrder:"normal",externalId:n.Kn??""}}return null}draw(t){"aboveSeries"!==this.ZM&&t.useBitmapCoordinateSpace((t=>{this.et(t)}))}drawBackground(t){"aboveSeries"===this.ZM&&t.useBitmapCoordinateSpace((t=>{this.et(t)}))}et({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){if(null!==this.Yt&&null!==this.Yt.lt){t.textBaseline="middle",t.font=this.Rp;for(let n=this.Yt.lt.from;n<this.Yt.lt.to;n++){const e=this.Yt.ot[n];void 0!==e.ri&&(e.ri.Qi=this.On.Vi(t,e.ri.GM),e.ri.$t=this.W,e.ri._t=e._t-e.ri.Qi/2),ir(e,t,i,s)}}}}function ir(t,i,s,n){i.fillStyle=t.R,void 0!==t.ri&&function(t,i,s,n,e,r){t.save(),t.scale(e,r),t.fillText(i,s,n),t.restore()}(i,t.ri.GM,t.ri._t,t.ri.ut,s,n),function(t,i,s){if(0===t.zr)return;switch(t.JM){case"arrowDown":return void Je(!1,i,s,t.zr);case"arrowUp":return void Je(!0,i,s,t.zr);case"circle":return void function(t,i,s){const n=(Ke("circle",s)-1)/2;t.beginPath(),t.arc(i._t,i.ut,n*i.XM,0,2*Math.PI,!1),t.fill()}(i,s,t.zr);case"square":return void function(t,i,s){const n=Ke("square",s),e=(n-1)*i.XM/2,r=i._t-e,h=i.ut-e;t.fillRect(r,h,n*i.XM,n*i.XM)}(i,s,t.zr)}t.JM}(t,i,function(t,i,s){const n=Math.max(1,Math.floor(i))%2/2;return{_t:Math.round(t._t*i)+n,ut:t.ut*s,XM:i}}(t,s,n))}function sr(t,i,s){return!(void 0===t.ri||!function(t,i,s,n,e,r){const h=n/2;return e>=t&&e<=t+s&&r>=i-h&&r<=i+h}(t.ri._t,t.ri.ut,t.ri.Qi,t.ri.$t,i,s))||function(t,i,s){if(0===t.zr)return!1;switch(t.JM){case"arrowDown":return Qe(!0,t._t,t.ut,t.zr,i,s);case"arrowUp":return Qe(!1,t._t,t.ut,t.zr,i,s);case"circle":return function(t,i,s,n,e){const r=2+Ke("circle",s)/2,h=t-n,a=i-e;return Math.sqrt(h*h+a*a)<=r}(t._t,t.ut,t.zr,i,s);case"square":return function(t,i,s,n,e){const r=Ke("square",s),h=(r-1)/2,a=t-h,l=i-h;return n>=a&&n<=a+r&&e>=l&&e<=l+r}(t._t,t.ut,t.zr,i,s)}}(t,i,s)}function nr(t){return"atPriceTop"===t||"atPriceBottom"===t||"atPriceMiddle"===t}function er(t,i,s,n,e,r,h,a){const l=function(t,i){if(nr(i.position)&&void 0!==i.price)return i.price;if("value"in(s=t)&&"number"==typeof s.value)return t.value;var s;if(function(t){return"open"in t&&"high"in t&&"low"in t&&"close"in t}(t)){if("inBar"===i.position)return t.close;if("aboveBar"===i.position)return t.high;if("belowBar"===i.position)return t.low}}(s,i);if(void 0===l)return;const o=nr(i.position),_=a.timeScale(),c=p(i.size)?Math.max(i.size,0):1,d=Xe(_.options().barSpacing)*c,f=d/2;t.zr=d;switch(i.position){case"inBar":case"atPriceMiddle":return t.ut=u(h.priceToCoordinate(l)),void(void 0!==t.ri&&(t.ri.ut=t.ut+f+r+.6*e));case"aboveBar":case"atPriceTop":{const i=o?0:n.QM;return t.ut=u(h.priceToCoordinate(l))-f-i,void 0!==t.ri&&(t.ri.ut=t.ut-f-.6*e,n.QM+=1.2*e),void(o||(n.QM+=d+r))}case"belowBar":case"atPriceBottom":{const i=o?0:n.tb;return t.ut=u(h.priceToCoordinate(l))+f+i,void 0!==t.ri&&(t.ri.ut=t.ut+f+r+.6*e,n.tb+=1.2*e),void(o||(n.tb+=d+r))}}}class rr{constructor(t,i,s){this.ib=[],this.xt=!0,this.sb=!0,this.Gt=new tr,this.ge=t,this.gp=i,this.Yt={ot:[],lt:null},this.ys=s}renderer(){if(!this.ge.options().visible)return null;this.xt&&this.nb();const t=this.gp.options().layout;return this.Gt.Nn(t.fontSize,t.fontFamily,this.ys.zOrder),this.Gt.ht(this.Yt),this.Gt}eb(t){this.ib=t,this.kt("data")}kt(t){this.xt=!0,"data"===t&&(this.sb=!0)}rb(t){this.xt=!0,this.ys=t}zOrder(){return"aboveSeries"===this.ys.zOrder?"top":this.ys.zOrder}nb(){const t=this.gp.timeScale(),i=this.ib;this.sb&&(this.Yt.ot=i.map((t=>({wt:t.time,_t:0,ut:0,zr:0,JM:t.shape,R:t.color,Kn:t.id,hb:t.hb,ri:void 0}))),this.sb=!1);const s=this.gp.options().layout;this.Yt.lt=null;const n=t.getVisibleLogicalRange();if(null===n)return;const e=new ki(Math.floor(n.from),Math.ceil(n.to));if(null===this.ge.data()[0])return;if(0===this.Yt.ot.length)return;let r=NaN;const h=Ze(t.options().barSpacing),a={QM:h,tb:h};this.Yt.lt=hn(this.Yt.ot,e,!0);for(let n=this.Yt.lt.from;n<this.Yt.lt.to;n++){const e=i[n];e.time!==r&&(a.QM=h,a.tb=h,r=e.time);const l=this.Yt.ot[n];l._t=u(t.logicalToCoordinate(e.time)),void 0!==e.text&&e.text.length>0&&(l.ri={GM:e.text,_t:0,ut:0,Qi:0,$t:0});const o=this.ge.dataByIndex(e.time,0);null!==o&&er(l,e,o,a,s.fontSize,h,this.ge,this.gp)}this.xt=!1}}function hr(t){return{...Ye,...t}}class ar{constructor(t){this.sh=null,this.ib=[],this.ab=[],this.lb=null,this.ge=null,this.gp=null,this.ob=!0,this._b=null,this.ub=null,this.cb=null,this.fb=!0,this.ys=hr(t)}attached(t){this.pb(),this.gp=t.chart,this.ge=t.series,this.sh=new rr(this.ge,u(this.gp),this.ys),this.jM=t.requestUpdate,this.ge.subscribeDataChanged((t=>this.fg(t))),this.fb=!0,this.IM()}IM(){this.jM&&this.jM()}detached(){this.ge&&this.lb&&this.ge.unsubscribeDataChanged(this.lb),this.gp=null,this.ge=null,this.sh=null,this.lb=null}eb(t){this.fb=!0,this.ib=t,this.pb(),this.ob=!0,this.ub=null,this.IM()}mb(){return this.ib}paneViews(){return this.sh?[this.sh]:[]}updateAllViews(){this.wb()}hitTest(t,i){return this.sh?this.sh.renderer()?.jn(t,i)??null:null}autoscaleInfo(t,i){if(this.sh){const t=this.gb();if(t)return{priceRange:null,margins:t}}return null}hr(t){this.ys=hr({...this.ys,...t}),this.IM&&this.IM()}gb(){const t=u(this.gp).timeScale().options().barSpacing;if(this.ob||t!==this.cb){if(this.cb=t,this.ib.length>0){const i=Ze(t),s=1.5*Xe(t)+2*i,n=this.Mb();this._b={above:Ge(s,n.aboveBar,n.inBar),below:Ge(s,n.belowBar,n.inBar)}}else this._b=null;this.ob=!1}return this._b}Mb(){return null===this.ub&&(this.ub=this.ib.reduce(((t,i)=>(t[i.position]||(t[i.position]=!0),t)),{inBar:!1,aboveBar:!1,belowBar:!1,atPriceTop:!1,atPriceBottom:!1,atPriceMiddle:!1})),this.ub}pb(){if(!this.fb||!this.gp||!this.ge)return;const t=this.gp.timeScale(),i=this.ge?.data();if(null==t.getVisibleLogicalRange()||!this.ge||0===i.length)return void(this.ab=[]);const s=t.timeToIndex(u(i[0].time),!0);this.ab=this.ib.map(((i,n)=>{const e=t.timeToIndex(i.time,!0),r=e<s?1:-1,h=u(this.ge).dataByIndex(e,r),a={time:t.timeToIndex(u(h).time,!1),position:i.position,shape:i.shape,color:i.color,id:i.id,hb:n,text:i.text,size:i.size,price:i.price,dw:i.time};if("atPriceTop"===i.position||"atPriceBottom"===i.position||"atPriceMiddle"===i.position){if(void 0===i.price)throw new Error(`Price is required for position ${i.position}`);return{...a,position:i.position,price:i.price}}return{...a,position:i.position,price:i.price}})),this.fb=!1}wb(t){this.sh&&(this.pb(),this.sh.eb(this.ab),this.sh.rb(this.ys),this.sh.kt(t))}fg(t){this.fb=!0,this.IM()}}class lr extends qe{constructor(t,i,s){super(t,i),s&&this.setMarkers(s)}setMarkers(t){this.ah.eb(t)}markers(){return this.ah.mb()}}function or(t,i,s){const n=new lr(t,new ar(s??{}));return i&&n.setMarkers(i),n}class _r{constructor(t){this.ib=new Map,this.bb=t}Sb(t,i,s){if(this.xb(i),void 0!==s){const n=window.setTimeout((()=>{this.ib.delete(i),this.Cb()}),s),e={...t,yb:n,kb:Date.now()+s};this.ib.set(i,e)}else this.ib.set(i,{...t,yb:void 0,kb:void 0});this.Cb()}xb(t){const i=this.ib.get(t);i&&void 0!==i.yb&&window.clearTimeout(i.yb),this.ib.delete(t),this.Cb()}Pb(){for(const[t]of this.ib)this.xb(t)}Tb(){const t=Date.now(),i=[];for(const[s,n]of this.ib)!n.kb||n.kb>t?i.push({time:n.time,sign:n.sign,value:n.value}):this.xb(s);return i}Rb(t){this.bb=t}Cb(){this.bb&&this.bb()}}const ur={positiveColor:"#22AB94",negativeColor:"#F7525F",updateVisibilityDuration:5e3};class cr{constructor(t,i,s,n){this.Yt=t,this.Db=i,this.Vb=s,this.Ib=n}draw(t){t.useBitmapCoordinateSpace((t=>{const i=t.context,s=Math.max(1,Math.floor(t.horizontalPixelRatio))%2/2,n=4*t.verticalPixelRatio+s;this.Yt.forEach((e=>{const r=Math.round(e._t*t.horizontalPixelRatio)+s;i.beginPath();const h=this.Bb(e.Ab);i.fillStyle=h,i.arc(r,e.ut*t.verticalPixelRatio,n,0,2*Math.PI,!1),i.fill(),e.Ab&&(i.strokeStyle=h,i.lineWidth=Math.floor(2*t.horizontalPixelRatio),i.beginPath(),i.moveTo((e._t-4.7)*t.horizontalPixelRatio+s,(e.ut-7*e.Ab)*t.verticalPixelRatio),i.lineTo(e._t*t.horizontalPixelRatio+s,(e.ut-7*e.Ab-7*e.Ab*.5)*t.verticalPixelRatio),i.lineTo((e._t+4.7)*t.horizontalPixelRatio+s,(e.ut-7*e.Ab)*t.verticalPixelRatio),i.stroke())}))}))}Bb(t){return 0===t?this.Db:t>0?this.Ib:this.Vb}}class dr{constructor(t,i,s){this.Yt=[],this.ge=t,this.uh=i,this.ys=s}kt(t){this.Yt=t.map((t=>{const i=this.ge.priceToCoordinate(t.value);if(null===i)return null;return{_t:u(this.uh.timeToCoordinate(t.time)),ut:i,Ab:t.sign}})).filter(M)}renderer(){const t=function(t,i){return function(t,i){return"Area"===i}(0,i)?t.lineColor:t.color}(this.ge.options(),this.ge.seriesType());return new cr(this.Yt,t,this.ys.negativeColor,this.ys.positiveColor)}}function fr(t,i){return"Line"===i||"Area"===i}class pr{constructor(t){this.gp=void 0,this.ge=void 0,this.VM=[],this.i_=null,this.Eb=new Map,this.zb=new _r((()=>this.IM())),this.ys={...ur,...t}}hr(t){this.ys={...this.ys,...t},this.IM()}eb(t){this.zb.Pb();const i=this.i_;i&&t.forEach((t=>{this.zb.Sb(t,i.key(t.time))}))}mb(){return this.zb.Tb()}IM(){this.jM?.()}attached(t){const{chart:i,series:s,requestUpdate:n,horzScaleBehavior:e}=t;this.gp=i,this.ge=s,this.i_=e;const r=this.ge.seriesType();if("Area"!==r&&"Line"!==r)throw new Error("UpDownMarkersPrimitive is only supported for Area and Line series types");this.VM=[new dr(this.ge,this.gp.timeScale(),this.ys)],this.jM=n,this.IM()}detached(){this.gp=void 0,this.ge=void 0,this.jM=void 0}Bp(){return _(this.gp)}Do(){return _(this.ge)}updateAllViews(){this.VM.forEach((t=>t.kt(this.mb())))}paneViews(){return this.VM}ht(t){if(!this.ge)throw new Error("Primitive not attached to series");const i=this.ge.seriesType();this.Eb.clear();const s=this.i_;s&&t.forEach((t=>{Fs(t)&&fr(0,i)&&this.Eb.set(s.key(t.time),t.value)})),_(this.ge).setData(t)}kt(t,i){if(!this.ge||!this.i_)throw new Error("Primitive not attached to series");const s=this.ge.seriesType(),n=this.i_.key(t.time);if(Ws(t)&&this.Eb.delete(n),Fs(t)&&fr(0,s)){const i=this.Eb.get(n);i&&this.zb.Sb({time:t.time,value:t.value,sign:vr(t.value,i)},n,this.ys.updateVisibilityDuration)}_(this.ge).update(t,i)}Lb(){this.zb.Pb()}}function vr(t,i){return t===i?0:t-i>0?1:-1}class mr extends qe{setData(t){return this.ah.ht(t)}update(t,i){return this.ah.kt(t,i)}markers(){return this.ah.mb()}setMarkers(t){return this.ah.eb(t)}clearMarkers(){return this.ah.Lb()}}function wr(t,i={}){return new mr(t,new pr(i))}const gr={...e,color:"#2196f3"};function Mr(){return"5.0.7"}export{ve as AreaSeries,Me as BarSeries,de as BaselineSeries,xe as CandlestickSeries,Ei as ColorType,K as CrosshairMode,ke as HistogramSeries,Bi as LastPriceAnimationMode,Gn as LineSeries,h as LineStyle,r as LineType,Pt as MismatchDirection,Ai as PriceLineSource,pi as PriceScaleMode,zi as TickMarkType,Ii as TrackingModeExitMode,Nn as createChart,On as createChartEx,$e as createImageWatermark,he as createOptionsChart,or as createSeriesMarkers,Le as createTextWatermark,wr as createUpDownMarkers,ne as createYieldCurveChart,gr as customSeriesDefaultOptions,Wn as defaultHorzScaleBehavior,Oi as isBusinessDay,Ni as isUTCTimestamp,Mr as version};
