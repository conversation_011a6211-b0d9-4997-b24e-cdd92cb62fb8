import MetaTrader5 as mt5
import pandas as pd
import pandas_ta as ta
from app.config import config

class DataFeeder:
    def __init__(self):
        self.initialize_mt5()
        
    def initialize_mt5(self):
        """Initialize MT5 connection"""
        if not mt5.initialize(
            path=config.MT5_PATH,
            login=config.MT5_LOGIN,
            password=config.MT5_PASSWORD,
            server=config.MT5_SERVER
        ):
            raise ConnectionError(f"MT5 initialization failed: {mt5.last_error()}")
    
    def fetch_data(self, symbol: str, timeframe=mt5.TIMEFRAME_M15, num_candles=300) -> pd.DataFrame:
        """Fetch historical data for a symbol"""
        rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, num_candles)
        if rates is None:
            raise ValueError(f"No data returned for {symbol}")
            
        df = pd.DataFrame(rates)
        df['time'] = pd.to_datetime(df['time'], unit='s')
        df.set_index('time', inplace=True)
        return df
    
    def _find_bbands_col(self, bbands_df, prefix):
        # Find the first column that starts with the given prefix
        for col in bbands_df.columns:
            if col.startswith(prefix):
                return col
        raise KeyError(f"No BBANDS column found with prefix {prefix}")
    
    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators"""
        # RSI
        df['rsi'] = ta.rsi(df['close'], length=config.RSI_PERIOD)
        
        # MACD
        macd = ta.macd(
            df['close'], 
            fast=config.MACD_FAST,
            slow=config.MACD_SLOW,
            signal=config.MACD_SIGNAL
        )
        df['macd'] = macd['MACD_' + str(config.MACD_FAST) + '_' + str(config.MACD_SLOW) + '_' + str(config.MACD_SIGNAL)]
        df['macd_signal'] = macd['MACDs_' + str(config.MACD_FAST) + '_' + str(config.MACD_SLOW) + '_' + str(config.MACD_SIGNAL)]
        df['macd_diff'] = macd['MACDh_' + str(config.MACD_FAST) + '_' + str(config.MACD_SLOW) + '_' + str(config.MACD_SIGNAL)]
        
        # Bollinger Bands
        bbands = ta.bbands(
            df['close'],
            length=config.BOLLINGER_PERIOD,
            std=config.BOLLINGER_STD
        )
        df['bb_upper'] = bbands[self._find_bbands_col(bbands, 'BBU_')]
        df['bb_middle'] = bbands[self._find_bbands_col(bbands, 'BBM_')]
        df['bb_lower'] = bbands[self._find_bbands_col(bbands, 'BBL_')]
        df['bb_pct'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # Stochastic Oscillator
        stoch = ta.stoch(
            df['high'], 
            df['low'], 
            df['close'],
            k=config.STOCHASTIC_PERIOD
        )
        df['stoch_k'] = stoch['STOCHk_' + str(config.STOCHASTIC_PERIOD) + '_3_3']
        df['stoch_d'] = stoch['STOCHd_' + str(config.STOCHASTIC_PERIOD) + '_3_3']
        
        # ATR
        df['atr'] = ta.atr(df['high'], df['low'], df['close'], length=14)
        
        return df.dropna()
    
    def get_features(self, symbol: str) -> pd.DataFrame:
        """Get processed data with indicators for a symbol"""
        raw_data = self.fetch_data(symbol)
        return self.calculate_indicators(raw_data) 