from app.postgresql_memory import PostgresMemory
from app.config import config
import openai
from datetime import datetime

class SelfReviewAgent:
    def __init__(self):
        self.memory = PostgresMemory()
        self.openai_api_key = config.OPENAI_API_KEY
        if self.openai_api_key:
            self.openai_client = openai.OpenAI(api_key=self.openai_api_key)
        else:
            self.openai_client = None

    def review_trade(self, symbol: str, decision: dict, outcome: dict):
        """
        Ask GPT to review the trade decision and outcome.
        decision: dict with keys like action, confidence, reason
        outcome: dict with keys like profit_loss, entry_price, exit_price
        """
        prompt = f"""
        Trade Review:
        Symbol: {symbol}
        Decision: {decision}
        Outcome: {outcome}
        Was this decision correct? What could be improved? Provide a short self-critique and a confidence adjustment (-1 to +1).
        Respond in JSON: {{"review": str, "confidence_adjustment": float}}
        """
        if not self.openai_client:
            review = {
                "review": "No OpenAI key provided. Skipping self-review.",
                "confidence_adjustment": 0.0
            }
        else:
            try:
                response = self.openai_client.chat.completions.create(
                    model="gpt-4-turbo",
                    messages=[{"role": "user", "content": prompt}],
                    response_format={"type": "json_object"},
                    temperature=0.5,
                )
                review = response.choices[0].message.content
            except Exception as e:
                review = {
                    "review": f"[GPT error: {e}]",
                    "confidence_adjustment": 0.0
                }
        # Store in memory
        self.memory.store_memory(
            'REVIEW',
            str(review),
            {
                'symbol': symbol,
                'decision': decision,
                'outcome': outcome,
                'timestamp': datetime.now().isoformat()
            }
        )
        return review

# Usage example:
# agent = SelfReviewAgent()
# agent.review_trade('EURUSD', {'action': 'BUY', ...}, {'profit_loss': 100, ...}) 