# NexGen Forex Trading Bot

An advanced AI-powered forex trading bot with memory, reasoning, and voice capabilities.

## Overview

NexGen Forex Trading Bot is a sophisticated automated trading system that combines machine learning, LLM reasoning, and technical analysis to make trading decisions. It features:

- **AI Trading Engine**: Hybrid ML + GPT-4 decision making
- **Memory System**: PostgreSQL-backed memory for trade decisions and reasoning
- **Vector Store**: ChromaDB-based semantic search for similar situations
- **Self-Review**: Automated post-trade analysis and learning
- **Voice Interface**: ElevenLabs or pyttsx3 voice output
- **Modern UI**: React/TypeScript dashboard with real-time data

## Architecture

The system consists of:

1. **Backend (Python)**
   - Core trading logic and ML models
   - Memory and reasoning systems
   - FastAPI server for frontend communication

2. **Frontend (React/TypeScript)**
   - Modern trading dashboard
   - Chat interface for querying the bot
   - Real-time trade monitoring

## Setup

### Prerequisites
- Python 3.8+
- Node.js 16+
- PostgreSQL database
- MetaTrader 5 terminal

### Backend Setup
1. Install Python dependencies:
   ```
   pip install -r requirements.txt
   ```

2. Create a `.env` file with the following variables:
   ```
   MT5_LOGIN=your_login
   MT5_PASSWORD=your_password
   MT5_SERVER=your_server
   MT5_PATH=path_to_mt5_terminal
   RISK_PERCENT=2.0
   SYMBOLS=EURUSD,GBPUSD
   OPENAI_API_KEY=your_openai_key
   POSTGRES_HOST=localhost
   POSTGRES_PORT=5432
   POSTGRES_DB=forex_memory
   POSTGRES_USER=postgres
   POSTGRES_PASSWORD=your_password
   ELEVENLABS_API_KEY=your_elevenlabs_key
   VOICE_ENABLED=false
   ```

3. Start the API server:
   ```
   python app/run_api.py
   ```

### Frontend Setup
1. Navigate to the UI directory:
   ```
   cd NexGen\ UI/
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Start the development server:
   ```
   npm run dev
   ```

## Usage

1. **Training the ML Model**
   ```
   python train_model.py
   ```

2. **Running the Trading Bot**
   ```
   python main.py
   ```

3. **Using the Web Interface**
   - Open your browser to `http://localhost:5173`
   - Use the dashboard to monitor trades
   - Chat with the AI assistant for insights and explanations

## Key Components

- **FIASS Core**: Forex Intelligence Agent Stack System orchestrator
- **PostgreSQL Memory**: Long-term memory storage for decisions and reviews
- **ChromaDB Vector Store**: Semantic memory for similar situations
- **Self-Review Agent**: Post-trade analysis and learning
- **Memory Summarizer**: Weekly summaries of trading activity
- **Voice Agent**: Text-to-speech capabilities
- **Multi-Agent Orchestrator**: Combines GPT and Claude for better decisions

## License

This project is proprietary software. All rights reserved. 