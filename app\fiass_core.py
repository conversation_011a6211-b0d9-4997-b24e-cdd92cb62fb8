from app.data_feeder import DataFeeder
from app.ai_brain import <PERSON>rain, HybridBrain
from app.trade_executor import TradeExecutor
from app.news_filter import NewsFilter
from app.anomaly_detector import AnomalyDetector
from app.explainability import ExplainabilityEngine
from app.config import config
from app.postgresql_memory import <PERSON>gresMemory
from app.chromadb_vector_store import VectorStore
import numpy as np
from datetime import datetime

class FIASS:
    def __init__(self):
        self.feeder = DataFeeder()
        self.ml_brain = MLBrain()
        self.hybrid_brain = HybridBrain(self.ml_brain)
        self.executor = TradeExecutor()
        self.news_filter = NewsFilter()
        self.anomaly_detector = AnomalyDetector()
        self.explain_engine = ExplainabilityEngine(self.ml_brain)
        self.memory = PostgresMemory()
        self.vector_store = VectorStore()

    def run_reasoning_cycle(self, symbol: str):
        # 1. Fetch data
        latest_data = self.feeder.get_features(symbol)
        if latest_data.empty:
            print(f"No data for {symbol}, skipping.")
            return

        # 2. Safety checks (news, anomaly, etc.)
        if self.news_filter.is_news_imminent(symbol):
            self.memory.store_memory('SAFETY', f'News imminent for {symbol}, skipping trade.', {'symbol': symbol})
            return
        if self.anomaly_detector.should_reduce_position():
            self.memory.store_memory('ANOMALY', f'Anomaly detected for {symbol}, reducing position.', {'symbol': symbol})

        # 3. AI Decision
        if self.ml_brain.model is None:
            self.memory.store_memory('ERROR', 'ML model not trained.', {})
            return
        decision = self.hybrid_brain.get_decision(symbol, latest_data)
        self.memory.store_memory('LLM', f"AI Decision for {symbol}: {decision.action}", {
            'symbol': symbol,
            'confidence': decision.confidence,
            'reason': decision.reason
        })

        # 4. Store vector embedding (using OpenAI or other embedding model)
        # Placeholder: use random vector for now
        embedding = np.random.rand(1536)
        self.vector_store.add_embedding(
            id_=f"{symbol}_{datetime.now().isoformat()}",
            embedding=embedding,
            metadata={'type': 'LLM', 'symbol': symbol, 'action': decision.action}
        )

        # 5. Execute trade if not HOLD
        if decision.action != "HOLD":
            result = self.executor.execute_trade(symbol, decision, latest_data.iloc[-1])
            self.memory.store_memory('TRADE', f"Executed trade for {symbol}", {
                'symbol': symbol,
                'result': str(result)
            })
            # Optionally: trigger explainability
            if self.explain_engine.should_explain():
                features = self.ml_brain.features_from_df(latest_data).iloc[[-1]]
                self.explain_engine.explain_trade(
                    trade_id=None,  # Not logging to SQLite here
                    features=features.iloc[-1].to_dict(),
                    X_row=features.values,
                    model_confidence=decision.confidence,
                    action=decision.action,
                    symbol=symbol
                )

    def run_all(self):
        for symbol in config.SYMBOLS:
            self.run_reasoning_cycle(symbol)

# Usage example:
# fiass = FIASS()
# fiass.run_all() 