from app.config import config
import os
from typing import Optional

class VoiceAgent:
    def __init__(self):
        self.voice_enabled = os.getenv('VOICE_ENABLED', 'false').lower() == 'true'
        self.elevenlabs_api_key = os.getenv('ELEVENLABS_API_KEY', '')
        self.use_elevenlabs = bool(self.elevenlabs_api_key)
        if not self.use_elevenlabs:
            try:
                import pyttsx3
                self.engine = pyttsx3.init()
            except ImportError:
                self.engine = None

    def speak(self, text: str, voice_id: Optional[str] = None, save_path: Optional[str] = None):
        if not self.voice_enabled:
            print('[VoiceAgent] Voice output is disabled.')
            return None
        if self.use_elevenlabs:
            import requests
            url = 'https://api.elevenlabs.io/v1/text-to-speech/'
            voice = voice_id or 'EXAVITQu4vr4xnSDxMaL'  # Default voice
            headers = {
                'xi-api-key': self.elevenlabs_api_key,
                'Content-Type': 'application/json'
            }
            payload = {
                'text': text,
                'voice_settings': {'stability': 0.5, 'similarity_boost': 0.5}
            }
            response = requests.post(url + voice, headers=headers, json=payload)
            if response.status_code == 200:
                audio = response.content
                if save_path:
                    with open(save_path, 'wb') as f:
                        f.write(audio)
                return audio
            else:
                print(f'[VoiceAgent] ElevenLabs error: {response.text}')
                return None
        elif self.engine:
            self.engine.say(text)
            self.engine.runAndWait()
            return None
        else:
            print('[VoiceAgent] No TTS engine available.')
            return None

# Usage example:
# agent = VoiceAgent()
# agent.speak('Hello, this is your trading assistant.') 