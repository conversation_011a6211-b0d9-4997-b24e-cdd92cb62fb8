import numpy as np
import pandas as pd
from sklearn.ensemble import IsolationForest
from datetime import datetime, timedelta
from app.database import db

class AnomalyDetector:
    """
    Detects abnormal trading patterns using Isolation Forest and provides structured output.
    """
    def __init__(self, lookback_days=7, contamination=0.1):
        self.lookback_days = lookback_days
        self.model = IsolationForest(contamination=contamination, random_state=42)
        self.last_analysis = None
        self.cache_expiry = None

    def _get_streaks(self, series):
        streaks = (series != series.shift()).cumsum()
        return series.groupby(streaks).cumsum()

    def get_analysis(self):
        """
        Fetches trade data, computes features, and returns a structured analysis
        of market anomalies. Caches the result for 15 minutes.
        """
        now = datetime.now()
        if self.last_analysis and self.cache_expiry and now < self.cache_expiry:
            return self.last_analysis

        since_date = (now - timedelta(days=self.lookback_days)).strftime('%Y-%m-%d')
        
        try:
            trades_query = "SELECT timestamp, size, profit_loss, symbol FROM trades WHERE timestamp >= ?"
            trades = db.conn.execute(trades_query, (since_date,)).fetchall()

            if len(trades) < 20: # Need sufficient data
                return self._default_no_anomaly()

            df = pd.DataFrame(trades)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
            
            # --- Feature Engineering ---
            # 1. Trading Frequency (trades per hour)
            freq = df.resample('h').size().rename('frequency')
            # 2. Average Trade Size
            avg_size = df['size'].resample('h').mean().rename('avg_size')
            # 3. Profitability Volatility (std dev of P/L)
            pnl_vol = df['profit_loss'].resample('h').std().rename('pnl_volatility')
            # 4. Symbol Diversity (number of unique symbols traded)
            symbol_diversity = df['symbol'].resample('h').nunique().rename('symbol_diversity')

            features = pd.concat([freq, avg_size, pnl_vol, symbol_diversity], axis=1).fillna(0)
            
            if features.empty:
                return self._default_no_anomaly()

            # --- Model Training & Scoring ---
            self.model.fit(features)
            # Scores are inverted; higher means more anomalous
            scores = -self.model.score_samples(features) 
            # Normalize scores to be between 0 and 1
            normalized_scores = (scores - scores.min()) / (scores.max() - scores.min())
            
            latest_score = normalized_scores[-1]
            
            # --- Interpretation ---
            level = 'low'
            if latest_score > self.model.contamination * 2: # High if > 2x contamination threshold
                level = 'high'
            elif latest_score > self.model.contamination: # Medium if > contamination threshold
                level = 'medium'

            indicators = self._interpret_features(features.iloc[-1], level)

            analysis = {
                "score": round(latest_score, 2),
                "level": level,
                "timestamp": now.isoformat(),
                "indicators": indicators,
                "confidence": round((1 - latest_score) * 100, 0),
                "history": normalized_scores.tolist()
            }
            
            self.last_analysis = analysis
            self.cache_expiry = now + timedelta(minutes=15) # Cache for 15 mins
            
            return analysis

        except Exception as e:
            print(f"Error in AnomalyDetector: {e}")
            return self._default_no_anomaly(error=str(e))

    def _interpret_features(self, latest_features, level):
        """Generates human-readable indicators based on feature values."""
        indicators = []
        if level == 'high':
            indicators.append("Significant deviation in market behavior detected.")
        elif level == 'medium':
            indicators.append("Moderate unusual trading patterns observed.")
        else:
            indicators.append("Market conditions appear stable.")

        if latest_features['frequency'] > latest_features.mean() * 1.5:
             indicators.append(f"High trading frequency: {latest_features['frequency']:.0f} trades/hr.")
        if latest_features['pnl_volatility'] > latest_features.mean() * 2:
             indicators.append("Increased profit/loss volatility.")
        if latest_features['symbol_diversity'] < 2 and latest_features['symbol_diversity'] > 0:
             indicators.append("Low instrument diversification.")
             
        return indicators

    def _default_no_anomaly(self, error=None):
        """Returns a default, safe analysis object."""
        res = {
            "score": 0.0, "level": "low", "timestamp": datetime.now().isoformat(),
            "indicators": ["Awaiting sufficient trade data."],
            "confidence": 99, "history": [0.0] * 10
        }
        if error: res['indicators'].append(f"Error: {error}")
        return res

    def should_reduce_position(self):
        """Legacy method for the trading loop."""
        analysis = self.get_analysis()
        return analysis and analysis['level'] == 'high'

    def get_last_score(self):
        analysis = self.get_analysis()
        return analysis.get('score') if analysis else None 