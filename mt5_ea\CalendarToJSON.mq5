//+------------------------------------------------------------------+
//| CalendarToJSON.mq5                                               |
//| Extended version: epoch, unit, forecast, previous, actual        |
//+------------------------------------------------------------------+
#property copyright "NexGen"
#property link      ""
#property version   "1.08"
#property strict
#property description "Extended CalendarToJSON with epoch, unit, values, status"

#include <Files\FileTxt.mqh>
CFileTxt file;

input int  BufferBeforeMinutes = 0;
input int  BufferAfterMinutes  = 24 * 60;
input int  RefreshSeconds      = 60;

// Map importance level to string
string GetImportanceString(int importance)
{
   switch (importance)
   {
      case CALENDAR_IMPORTANCE_HIGH:     return "high";
      case CALENDAR_IMPORTANCE_MODERATE: return "medium";
      case CALENDAR_IMPORTANCE_LOW:      return "low";
      default:                           return "none";
   }
}

void OnInit()
{
   EventSetTimer(RefreshSeconds);
   PrintFormat("✅ CalendarToJSON initialised (refresh: %d s)", RefreshSeconds);
}

void OnDeinit(const int reason)
{
   EventKillTimer();
   Print("🛑 CalendarToJSON stopped.");
}

void OnTimer()
{
   datetime from_time = TimeCurrent() - BufferBeforeMinutes * 60;
   datetime to_time   = TimeCurrent() + BufferAfterMinutes  * 60;

   MqlCalendarValue values[];
   int total = CalendarValueHistory(values, from_time, to_time);

   if (total <= 0)
   {
      PrintFormat("⚠️ CalendarValueHistory returned %d (error %d)", total, GetLastError());
      return;
   }

   string json = "[";
   bool first = true;

   for (int i = 0; i < total; i++)
   {
      MqlCalendarValue v = values[i];
      if (v.time > to_time)
         break;

      MqlCalendarEvent meta;
      if (!CalendarEventById(v.event_id, meta))
         continue;

      MqlCalendarCountry ctry;
      string country_code = "";
      string currency_code = "";
      if (CalendarCountryById(meta.country_id, ctry))
      {
         country_code = ctry.code;
         currency_code = ctry.currency;
      }

      if (!first) json += ",";
      first = false;

      // Force safe conversions (MQL5 can be picky)
      string epoch_str = IntegerToString((int)v.time);
      string unit_str  = EnumToString((ENUM_CALENDAR_EVENT_UNIT)meta.unit);

      double forecast = (v.forecast_value == LONG_MIN) ? 0.0 : v.forecast_value / 1e6;
      double previous = (v.prev_value     == LONG_MIN) ? 0.0 : v.prev_value     / 1e6;
      double actual   = (v.actual_value   == LONG_MIN) ? 0.0 : v.actual_value   / 1e6;
      string release_status = (v.actual_value == LONG_MIN) ? "pending" : "released";

      json += StringFormat(
         "{\"id\":\"%s\",\"time\":\"%s\",\"epoch\":\"%s\",\"country\":\"%s\","
         "\"currency\":\"%s\",\"importance\":\"%s\",\"unit\":\"%s\",\"event\":\"%s\","
         "\"forecast\":%.2f,\"previous\":%.2f,\"actual\":%.2f,\"release_status\":\"%s\"}",
         IntegerToString(v.event_id),
         StringReplace(TimeToString(v.time, TIME_DATE | TIME_MINUTES), ".", "-"),
         epoch_str,
         country_code,
         currency_code,
         GetImportanceString(meta.importance),
         unit_str,
         (string)meta.name,
         forecast,
         previous,
         actual,
         release_status
      );
   }

   json += "]";

   if (file.Open("calendar.json", FILE_WRITE | FILE_COMMON | FILE_ANSI))
   {
      file.WriteString(json);
      file.Close();
      PrintFormat("✅ Calendar exported: %d events, %d bytes", total, StringLen(json));
   }
   else
   {
      PrintFormat("❌ Failed to open calendar.json (error %d)", GetLastError());
   }
}
//+------------------------------------------------------------------+
