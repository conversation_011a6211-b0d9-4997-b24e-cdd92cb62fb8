from app.fiass_core import FIASS
from app.memory_summarizer import MemorySummarizer
from app.postgresql_memory import PostgresMemory
from app.config import config

class ChatAgent:
    def __init__(self):
        self.fiass = FIASS()
        self.memory = PostgresMemory()
        self.summarizer = MemorySummarizer()

    def process_message(self, message: str) -> str:
        # Simple routing logic for demo; can be extended with LLM intent detection
        msg = message.lower()
        if 'summarize' in msg and 'week' in msg:
            summary = self.summarizer.summarize_week()
            return f"Weekly summary:\n{summary}"
        elif 'last trade' in msg or 'recent trade' in msg:
            logs = self.memory.retrieve_memory(type_='TRADE', limit=1)
            if logs:
                return f"Last trade: {logs[0]['content']}"
            else:
                return "No recent trades found."
        elif 'top' in msg and 'trade' in msg:
            logs = self.memory.retrieve_memory(type_='TRADE', limit=3)
            return '\n'.join([f"Trade {i+1}: {log['content']}" for i, log in enumerate(logs)])
        elif 'why' in msg and 'trade' in msg:
            logs = self.memory.retrieve_memory(type_='LLM', limit=1)
            if logs:
                return f"Reasoning: {logs[0]['content']}"
            else:
                return "No reasoning found."
        else:
            # Default: run FIASS reasoning cycle for all symbols and return a status
            self.fiass.run_all()
            return "Ran reasoning cycle. Check memory for new decisions."

# Usage example:
# agent = ChatAgent()
# response = agent.process_message('Summarize the week') 