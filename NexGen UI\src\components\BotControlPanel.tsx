import React, { useState } from 'react';
import { Play, Pause, Square, TrendingUp, TrendingDown, AlertTriangle, Power, Settings, Zap } from 'lucide-react';

interface BotStatus {
  isRunning: boolean;
  isPaused: boolean;
  lastAction: string;
  uptime: string;
  tradesCount: number;
  emergencyStop: boolean;
}

const BotControlPanel: React.FC = () => {
  const [botStatus, setBotStatus] = useState<BotStatus>({
    isRunning: true,
    isPaused: false,
    lastAction: 'BUY EURUSD @ 1.0845',
    uptime: '2h 34m',
    tradesCount: 15,
    emergencyStop: false
  });

  const [showConfirmation, setShowConfirmation] = useState<string | null>(null);

  const handleBotAction = (action: string) => {
    switch (action) {
      case 'pause':
        setBotStatus(prev => ({ ...prev, isPaused: true, isRunning: false }));
        break;
      case 'resume':
        setBotStatus(prev => ({ ...prev, isPaused: false, isRunning: true }));
        break;
      case 'emergency':
        setBotStatus(prev => ({ ...prev, emergencyStop: true, isRunning: false, isPaused: false }));
        break;
      case 'reset':
        setBotStatus(prev => ({ ...prev, emergencyStop: false, isRunning: true, isPaused: false }));
        break;
    }
    setShowConfirmation(null);
  };

  const handleForceAction = (action: 'buy' | 'sell') => {
    // Simulate force trade execution
    const pairs = ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD'];
    const pair = pairs[Math.floor(Math.random() * pairs.length)];
    const price = (1 + Math.random()).toFixed(4);
    
    setBotStatus(prev => ({
      ...prev,
      lastAction: `FORCE ${action.toUpperCase()} ${pair} @ ${price}`,
      tradesCount: prev.tradesCount + 1
    }));
    
    setShowConfirmation(null);
  };

  const getStatusColor = () => {
    if (botStatus.emergencyStop) return 'text-neon-red';
    if (botStatus.isPaused) return 'text-neon-yellow';
    if (botStatus.isRunning) return 'text-neon-green';
    return 'text-terminal-muted';
  };

  const getStatusText = () => {
    if (botStatus.emergencyStop) return 'Emergency Stop';
    if (botStatus.isPaused) return 'Paused';
    if (botStatus.isRunning) return 'Active';
    return 'Stopped';
  };

  const controlButtons = [
    {
      id: 'pause',
      label: botStatus.isRunning ? 'Pause Bot' : 'Resume Bot',
      icon: botStatus.isRunning ? Pause : Play,
      action: botStatus.isRunning ? 'pause' : 'resume',
      color: botStatus.isRunning ? 'from-neon-yellow to-neon-orange' : 'from-neon-green to-neon-blue',
      disabled: botStatus.emergencyStop
    },
    {
      id: 'emergency',
      label: botStatus.emergencyStop ? 'Reset System' : 'Emergency Stop',
      icon: botStatus.emergencyStop ? Power : Square,
      action: botStatus.emergencyStop ? 'reset' : 'emergency',
      color: botStatus.emergencyStop ? 'from-neon-blue to-neon-green' : 'from-neon-red to-neon-orange',
      disabled: false,
      requiresConfirmation: true
    }
  ];

  const forceTradeButtons = [
    {
      id: 'forceBuy',
      label: 'Force Buy',
      icon: TrendingUp,
      action: 'buy',
      color: 'from-neon-green to-neon-blue',
      disabled: botStatus.emergencyStop || !botStatus.isRunning
    },
    {
      id: 'forceSell',
      label: 'Force Sell',
      icon: TrendingDown,
      action: 'sell',
      color: 'from-neon-red to-neon-orange',
      disabled: botStatus.emergencyStop || !botStatus.isRunning
    }
  ];

  return (
    <div className="terminal-card p-6">
      <div className="flex items-center space-x-3 mb-6">
        <div className="p-2 bg-neon-green/20 rounded-lg">
          <Settings className="text-neon-green" size={24} />
        </div>
        <div>
          <h2 className="text-xl font-bold text-terminal-text">Bot Control Panel</h2>
          <p className="text-sm text-terminal-muted font-mono">Trading Bot Management</p>
        </div>
      </div>

      {/* Bot Status Display */}
      <div className="terminal-surface p-5 rounded-lg mb-6 border-l-4" style={{ borderLeftColor: getStatusColor().replace('text-', 'var(--color-') }}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${
              botStatus.emergencyStop ? 'bg-neon-red/20' :
              botStatus.isPaused ? 'bg-neon-yellow/20' :
              botStatus.isRunning ? 'bg-neon-green/20' : 'bg-terminal-muted/20'
            }`}>
              {botStatus.emergencyStop ? <AlertTriangle className="text-neon-red" size={20} /> :
               botStatus.isPaused ? <Pause className="text-neon-yellow" size={20} /> :
               botStatus.isRunning ? <Zap className="text-neon-green" size={20} /> :
               <Square className="text-terminal-muted" size={20} />}
            </div>
            <div>
              <div className={`text-lg font-bold font-mono ${getStatusColor()}`}>
                Bot Status: {getStatusText()}
              </div>
              <div className="text-sm text-terminal-muted font-mono">
                Uptime: {botStatus.uptime} • Trades: {botStatus.tradesCount}
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-terminal-muted font-mono mb-1">Last Action</div>
            <div className="text-terminal-text font-mono font-medium">{botStatus.lastAction}</div>
          </div>
        </div>

        {botStatus.emergencyStop && (
          <div className="bg-neon-red/10 border border-neon-red/30 rounded-lg p-3 mt-4">
            <div className="flex items-center space-x-2 text-neon-red">
              <AlertTriangle size={16} />
              <span className="font-mono font-medium">Emergency stop activated. All trading halted.</span>
            </div>
          </div>
        )}
      </div>

      {/* Main Control Buttons */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {controlButtons.map((button) => (
          <button
            key={button.id}
            onClick={() => {
              if (button.requiresConfirmation) {
                setShowConfirmation(button.action);
              } else {
                handleBotAction(button.action);
              }
            }}
            disabled={button.disabled}
            className={`flex items-center justify-center space-x-3 bg-gradient-to-r ${button.color} hover:opacity-90 text-white py-4 px-6 rounded-lg transition-all duration-300 font-mono font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            <button.icon size={20} />
            <span>{button.label}</span>
          </button>
        ))}
      </div>

      {/* Force Trade Buttons */}
      <div className="terminal-surface p-4 rounded-lg mb-6">
        <h3 className="text-lg font-semibold text-terminal-text font-mono mb-4">Manual Trading</h3>
        <div className="grid grid-cols-2 gap-3">
          {forceTradeButtons.map((button) => (
            <button
              key={button.id}
              onClick={() => setShowConfirmation(button.action)}
              disabled={button.disabled}
              className={`flex items-center justify-center space-x-2 bg-gradient-to-r ${button.color} hover:opacity-90 text-white py-3 px-4 rounded-lg transition-all duration-300 font-mono font-medium disabled:opacity-50 disabled:cursor-not-allowed`}
            >
              <button.icon size={16} />
              <span>{button.label}</span>
            </button>
          ))}
        </div>
        <div className="text-xs text-terminal-muted font-mono mt-3 text-center">
          Manual trades will use current risk management settings
        </div>
      </div>

      {/* Connection Status */}
      <div className="grid grid-cols-2 gap-4">
        <div className="terminal-surface p-4 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm font-mono text-terminal-muted">MT5 Connection</span>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-neon-green rounded-full animate-pulse"></div>
              <span className="text-sm font-mono text-neon-green">Online</span>
            </div>
          </div>
        </div>
        <div className="terminal-surface p-4 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm font-mono text-terminal-muted">API Status</span>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-neon-green rounded-full animate-pulse"></div>
              <span className="text-sm font-mono text-neon-green">Connected</span>
            </div>
          </div>
        </div>
      </div>

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="terminal-card p-6 max-w-md w-full mx-4">
            <div className="flex items-center space-x-3 mb-4">
              <AlertTriangle className="text-neon-yellow" size={24} />
              <h3 className="text-xl font-bold text-terminal-text font-mono">Confirm Action</h3>
            </div>
            <p className="text-terminal-muted font-mono mb-6">
              {showConfirmation === 'emergency' && 'This will immediately stop all trading and close open positions.'}
              {showConfirmation === 'buy' && 'Execute a manual BUY order with current risk settings?'}
              {showConfirmation === 'sell' && 'Execute a manual SELL order with current risk settings?'}
              {showConfirmation === 'reset' && 'Reset the emergency stop and resume normal operations?'}
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => {
                  if (showConfirmation === 'buy' || showConfirmation === 'sell') {
                    handleForceAction(showConfirmation as 'buy' | 'sell');
                  } else {
                    handleBotAction(showConfirmation);
                  }
                }}
                className="flex-1 bg-gradient-to-r from-neon-red to-neon-orange text-white py-3 px-4 rounded-lg font-mono font-medium hover:opacity-90 transition-all duration-300"
              >
                Confirm
              </button>
              <button
                onClick={() => setShowConfirmation(null)}
                className="flex-1 terminal-surface text-terminal-text py-3 px-4 rounded-lg font-mono font-medium hover:bg-terminal-card transition-all duration-300 border border-terminal-border"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BotControlPanel;