import React, { useState, useEffect } from 'react';
import { Calendar, AlertTriangle, Clock, Globe, Filter, TrendingUp, Loader2, ServerCrash } from 'lucide-react';
import { api, NewsEvent } from '../services/api';

interface BlockedTrade {
  id: string;
  timestamp: string;
  pair: string;
  reason: string;
  eventTime: string;
}

const NewsFilter: React.FC = () => {
  const [upcomingEvents, setUpcomingEvents] = useState<NewsEvent[]>([]);
  const [blockedTrades, setBlockedTrades] = useState<BlockedTrade[]>([
    {
      id: '1',
      timestamp: '2025-01-15 12:25:00',
      pair: 'EURUSD',
      reason: 'NFP News Event (USD)',
      eventTime: '12:30'
    },
    {
      id: '2',
      timestamp: '2025-01-15 13:55:00',
      pair: 'GBPUSD',
      reason: 'ECB Interest Decision (EUR)',
      eventTime: '14:00'
    },
    {
      id: '3',
      timestamp: '2025-01-15 11:45:00',
      pair: 'USDJPY',
      reason: 'High Impact News Window',
      eventTime: '12:30'
    }
  ]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [sentiment, setSentiment] = useState<{decision:string;score:number} | null>(null);

  const [filterSettings, setFilterSettings] = useState({
    blockHighImpact: true,
    blockMediumImpact: false,
    blockLowImpact: false,
    bufferMinutes: 30
  });

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-neon-red bg-neon-red/20 border-neon-red/30';
      case 'medium': return 'text-neon-yellow bg-neon-yellow/20 border-neon-yellow/30';
      case 'low': return 'text-neon-green bg-neon-green/20 border-neon-green/30';
      default: return 'text-terminal-muted bg-terminal-muted/20 border-terminal-muted/30';
    }
  };

  const getImpactIcon = (impact: string) => {
    switch (impact) {
      case 'high': return <AlertTriangle size={14} />;
      case 'medium': return <TrendingUp size={14} />;
      case 'low': return <Globe size={14} />;
      default: return <Globe size={14} />;
    }
  };

  const getCurrencyFlag = (currency: string) => {
    const flags: { [key: string]: string } = {
      'USD': '🇺🇸',
      'EUR': '🇪🇺',
      'GBP': '🇬🇧',
      'JPY': '🇯🇵',
      'AUD': '🇦🇺',
      'CAD': '🇨🇦',
      'CHF': '🇨🇭',
      'NZD': '🇳🇿'
    };
    return flags[currency] || '🌍';
  };

  const handleFilterChange = (key: keyof typeof filterSettings, value: boolean | number) => {
    setFilterSettings(prev => ({ ...prev, [key]: value }));
  };

  const activeFilters = Object.entries(filterSettings).filter(([key, value]) => 
    key !== 'bufferMinutes' && value === true
  ).length;

  useEffect(() => {
    const fetchNews = async () => {
      try {
        setIsLoading(true);
        const response = await api.getNews();
        setUpcomingEvents(response.events);
        setError(null);
        // fetch sentiment
        try {
          const s = await api.getNewsSentiment('EURUSD');
          setSentiment({decision:s.decision, score:s.score});
        } catch(e){ console.error(e); }
      } catch (err) {
        setError("Failed to fetch news events.");
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };
    fetchNews();
    // Refresh every 15 minutes
    const interval = setInterval(fetchNews, 15 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="terminal-card p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-neon-orange/20 rounded-lg">
            <Calendar className="text-neon-orange" size={24} />
          </div>
          <div>
            <h2 className="text-xl font-bold text-terminal-text">News Filter</h2>
            <p className="text-sm text-terminal-muted font-mono">Economic Events & Trade Blocking</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Filter className="text-neon-blue" size={16} />
          <span className="text-sm font-mono text-terminal-text">
            {activeFilters} filter{activeFilters !== 1 ? 's' : ''} active
          </span>
          {sentiment && (
            <span className={`ml-4 px-3 py-1 rounded-full text-xs font-mono shadow ${sentiment.score>0.2?'bg-neon-green/20 text-neon-green':sentiment.score<-0.2?'bg-neon-red/20 text-neon-red':'bg-terminal-card text-terminal-text'}`}>Sentiment {sentiment.score.toFixed(2)} {sentiment.decision}</span>
          )}
        </div>
      </div>

      {/* Filter Settings */}
      <div className="terminal-surface p-4 rounded-lg mb-6">
        <h3 className="text-lg font-semibold text-terminal-text font-mono mb-4">Filter Settings</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div className="space-y-3">
            {[
              { key: 'blockHighImpact', label: 'Block High Impact', color: 'neon-red' },
              { key: 'blockMediumImpact', label: 'Block Medium Impact', color: 'neon-yellow' },
              { key: 'blockLowImpact', label: 'Block Low Impact', color: 'neon-green' }
            ].map((filter) => (
              <div key={filter.key} className="flex items-center justify-between p-3 terminal-bg rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full bg-${filter.color}`}></div>
                  <span className="text-terminal-text font-mono">{filter.label}</span>
                </div>
                <button
                  onClick={() => handleFilterChange(filter.key as keyof typeof filterSettings, !filterSettings[filter.key as keyof typeof filterSettings])}
                  className={`relative inline-flex items-center h-6 rounded-full w-11 transition-all duration-300 ${
                    filterSettings[filter.key as keyof typeof filterSettings] ? `bg-${filter.color} shadow-glow-${filter.color.split('-')[1]}` : 'bg-terminal-border'
                  }`}
                >
                  <span
                    className={`inline-block w-4 h-4 transform bg-white rounded-full transition-transform duration-300 ${
                      filterSettings[filter.key as keyof typeof filterSettings] ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            ))}
          </div>
          
          <div className="terminal-bg p-3 rounded-lg">
            <label className="block text-sm font-medium text-terminal-text mb-2 font-mono">
              Buffer Time (minutes)
            </label>
            <input
              type="number"
              value={filterSettings.bufferMinutes}
              onChange={(e) => handleFilterChange('bufferMinutes', Number(e.target.value))}
              className="w-full px-3 py-2 bg-terminal-surface border border-terminal-border rounded-lg text-terminal-text font-mono focus:outline-none focus:ring-2 focus:ring-neon-blue focus:border-transparent transition-all duration-200"
              min="0"
              max="120"
            />
            <div className="text-xs text-terminal-muted font-mono mt-1">
              Block trades {filterSettings.bufferMinutes} minutes before/after events
            </div>
          </div>
        </div>
      </div>

      {/* Upcoming Events */}
      <div className="terminal-surface p-4 rounded-lg mb-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-terminal-text font-mono">Today's Events</h3>
          <div className="text-sm text-terminal-muted font-mono">
            {new Date().toLocaleDateString()}
          </div>
        </div>
        
        <div className="space-y-3">
          {upcomingEvents.map((event) => (
            <div key={event.id} className={`p-4 rounded-lg border transition-all duration-300 hover:bg-terminal-card/30 ${
              event.isBlocked ? 'bg-neon-red/5 border-neon-red/20' : 'bg-terminal-bg border-terminal-border'
            }`}>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    <Clock className="text-terminal-muted" size={14} />
                    <span className="font-mono font-bold text-terminal-text">{event.time}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{getCurrencyFlag(event.currency)}</span>
                    <span className="font-mono font-medium text-terminal-text">{event.currency}</span>
                  </div>
                  <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-mono font-medium border ${getImpactColor(event.impact)}`}>
                    {getImpactIcon(event.impact)}
                    <span className="capitalize">{event.impact}</span>
                  </div>
                </div>
                {event.isBlocked && (
                  <div className="flex items-center space-x-2 text-neon-red">
                    <AlertTriangle size={16} />
                    <span className="text-sm font-mono font-medium">BLOCKED</span>
                  </div>
                )}
              </div>
              
              <div className="text-terminal-text font-mono font-medium mb-2">{event.event}</div>
              
              {(event.forecast || event.previous) && (
                <div className="flex space-x-6 text-sm">
                  {event.forecast && (
                    <div className="flex space-x-2">
                      <span className="text-terminal-muted font-mono">Forecast:</span>
                      <span className="text-neon-blue font-mono font-medium">{event.forecast}</span>
                    </div>
                  )}
                  {event.previous && (
                    <div className="flex space-x-2">
                      <span className="text-terminal-muted font-mono">Previous:</span>
                      <span className="text-terminal-text font-mono">{event.previous}</span>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Blocked Trades Log */}
      <div className="terminal-surface p-4 rounded-lg">
        <h3 className="text-lg font-semibold text-terminal-text font-mono mb-4">Blocked Trades Log</h3>
        
        <div className="space-y-2 max-h-48 overflow-y-auto scrollbar-thin">
          {blockedTrades.map((trade) => (
            <div key={trade.id} className="flex items-center justify-between p-3 terminal-bg rounded-lg hover:bg-terminal-card/30 transition-colors">
              <div className="flex items-center space-x-3">
                <div className="p-1.5 bg-neon-red/20 rounded-lg">
                  <AlertTriangle className="text-neon-red" size={14} />
                </div>
                <div>
                  <div className="text-sm font-mono text-terminal-text font-medium">
                    {trade.pair} - Trade Blocked
                  </div>
                  <div className="text-xs text-terminal-muted font-mono">
                    {trade.reason}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-xs text-terminal-muted font-mono">
                  {new Date(trade.timestamp).toLocaleTimeString()}
                </div>
                <div className="text-xs text-neon-yellow font-mono">
                  Event: {trade.eventTime}
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {blockedTrades.length === 0 && (
          <div className="text-center py-8 text-terminal-muted">
            <Globe className="mx-auto mb-2" size={24} />
            <p className="font-mono">No trades blocked today</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default NewsFilter;