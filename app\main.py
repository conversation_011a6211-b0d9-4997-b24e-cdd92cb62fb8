import pandas as pd
from datetime import datetime
import MetaTrader5 as mt5

from app.config import config
from app.data_feeder import DataFeeder
from app.ai_brain import MLBrain, HybridBrain
from app.trade_executor import TradeExecutor
from app.news_filter import NewsFilter
from app.database import db
from app.anomaly_detector import AnomalyDetector
from app.auto_tuner import AutoTuner
from app.explainability import ExplainabilityEngine
from app.chat_interface import ChatAgent
from app.voice_agent import VoiceAgent
from app.memory_summarizer import MemorySummarizer
from app.fiass_core import FIASS

# --- Safety Globals ---
# In a more advanced system, this state would be managed in a database or a dedicated state manager class.
daily_drawdown_limit = config.RISK_PERCENT 
daily_equity_start = None
trading_halted = False

def initialize_components():
    """Initializes all the major components of the bot."""
    ml_brain = MLBrain()
    return {
        "feeder": DataFeeder(),
        "ml_brain": ml_brain,
        "hybrid_brain": HybridBrain(ml_brain),
        "executor": TradeExecutor(),
        "news_filter": NewsFilter(),
        "anomaly_detector": AnomalyDetector(),
        "explain_engine": ExplainabilityEngine(ml_brain),
        "auto_tuner": AutoTuner(),
        "db": db,
        "chat_agent": ChatAgent(),
        "voice_agent": VoiceAgent(),
        "memory_summarizer": MemorySummarizer(),
        "fiass": FIASS()
    }

def check_safety_protocols(symbol: str, df: pd.DataFrame, news_filter: NewsFilter) -> bool:
    """
    Runs all safety checks. Returns True if safe to trade, False otherwise.
    """
    global trading_halted, daily_equity_start

    account_info = mt5.account_info()
    if not account_info:
        print("Could not get account info for safety check.")
        return False

    if daily_equity_start is None:
        daily_equity_start = account_info.equity

    current_equity = account_info.equity
    drawdown_pct = (daily_equity_start - current_equity) / daily_equity_start * 100

    if drawdown_pct >= daily_drawdown_limit:
        if not trading_halted:
            print(f"CRITICAL: Daily drawdown limit of {daily_drawdown_limit}% reached. Halting all trades for the day.")
            trading_halted = True
        return False
    
    if news_filter.is_news_imminent(symbol):
        return False

    atr_threshold = 0.5
    if not df.empty:
        latest_atr = df['atr'].iloc[-1]
        latest_close = df['close'].iloc[-1]
        volatility_metric = (latest_atr / latest_close) * 100
        
        if volatility_metric > atr_threshold:
            print(f"VOLATILITY HALT: ATR ({volatility_metric:.2f}%) exceeds threshold of {atr_threshold}%. Skipping trade.")
            return False

    return True

def reset_daily_protocols():
    """Resets daily limits. To be scheduled to run once a day."""
    global daily_equity_start, trading_halted
    daily_equity_start = None
    trading_halted = False
    print("Daily safety protocols have been reset.")

def trade_cycle(feeder, hybrid_brain, executor, news_filter, anomaly_detector, explain_engine, ml_brain):
    """
    Executes a single trading cycle: fetch data, get decision, execute.
    """
    print(f"--- Running trade cycle at {datetime.now()} ---")
    if trading_halted:
        print("Trading is currently halted due to safety protocol breach.")
        return

    try:
        for symbol in config.SYMBOLS:
            print(f"Analyzing {symbol}...")
            
            latest_data = feeder.get_features(symbol)
            if latest_data.empty:
                continue

            if not check_safety_protocols(symbol, latest_data, news_filter):
                continue

            if ml_brain.model is None:
                print("ML model not trained. Cannot generate prediction. Skipping cycle.")
                return 

            decision = hybrid_brain.get_decision(symbol, latest_data)
            
            print(f"AI Decision for {symbol}: {decision.action} (Confidence: {decision.confidence:.2%})")

            if decision.action != "HOLD":
                result = executor.execute_trade(symbol, decision, latest_data.iloc[-1])
                trade_id = db.log_trade(decision, symbol, result)
                
                db.log_indicators(trade_id, latest_data.iloc[-1].to_dict())
                
                if explain_engine.should_explain():
                    features = ml_brain.features_from_df(latest_data).iloc[[-1]]
                    explain_engine.explain_trade(
                        trade_id,
                        features.iloc[-1].to_dict(),
                        features.values,
                        decision.confidence,
                        decision.action,
                        symbol
                    )
    except Exception as e:
        print(f"An error occurred during the trade cycle: {e}")

def anomaly_detection_cycle(anomaly_detector):
    if anomaly_detector.should_reduce_position():
        print("Anomaly detected! Reducing position size by 50% for next cycle.")
        config.RISK_PERCENT = max(1.0, config.RISK_PERCENT * 0.5)
    else:
        score = anomaly_detector.get_last_score()
        if score is not None:
            print(f"Anomaly score OK: {score:.2f}")

def auto_tuning_cycle(auto_tuner):
    auto_tuner.apply_tuning()

# The main function, scheduling logic, and __main__ block have been moved to the root run.py.
# This file now only contains the core trading logic functions and component initialization. 