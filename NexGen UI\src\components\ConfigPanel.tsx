import React, { useState, useEffect } from 'react';
import { Settings, Target, Shield, TrendingUp, Save, Zap, AlertTriangle, Loader2, ServerCrash } from 'lucide-react';
import { api, Config } from '../services/api';

type EditableConfig = Omit<Config, 'MT5_PASSWORD' | 'OPENAI_API_KEY' | 'POSTGRES_PASSWORD' | 'SYMBOLS' | 'MT5_LOGIN' | 'MT5_SERVER' | 'MT5_PATH' | 'POSTGRES_HOST' | 'POSTGRES_PORT' | 'POSTGRES_DB' | 'POSTGRES_USER' | 'VECTOR_STORE_TYPE' | 'VECTOR_STORE_PATH' >;

const ConfigPanel: React.FC = () => {
  const [config, setConfig] = useState<Partial<EditableConfig>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [isModified, setIsModified] = useState(false);

  useEffect(() => {
    const fetchConfig = async () => {
      try {
        setIsLoading(true);
        const fetchedConfig = await api.getConfig();
        setConfig(fetchedConfig);
        setError(null);
      } catch (err) {
        setError('Failed to fetch configuration. Is the backend running?');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };
    fetchConfig();
  }, []);

  const handleChange = (key: keyof EditableConfig, value: number | string) => {
    setConfig(prev => ({ ...prev, [key]: value }));
    if (!isModified) setIsModified(true);
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      await api.updateConfig(config);
    setIsModified(false);
    } catch (err) {
      setError('Failed to save configuration.');
      console.error(err);
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="terminal-card p-6 flex items-center justify-center h-full">
        <Loader2 className="animate-spin text-neon-blue" size={48} />
        <p className="ml-4 text-lg font-mono text-terminal-text">Loading Configuration...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="terminal-card p-6 flex flex-col items-center justify-center h-full bg-neon-red/10 border border-neon-red">
        <ServerCrash className="text-neon-red" size={48} />
        <p className="mt-4 text-lg font-mono text-neon-red">Error</p>
        <p className="text-sm font-mono text-terminal-muted">{error}</p>
      </div>
    );
  }
  
  const riskLevel = config.RISK_PERCENT && config.RISK_PERCENT > 5 ? 'high' : config.RISK_PERCENT && config.RISK_PERCENT > 2 ? 'medium' : 'low';
  const riskColor = riskLevel === 'high' ? 'text-neon-red' : riskLevel === 'medium' ? 'text-neon-yellow' : 'text-neon-green';

  return (
    <div className="terminal-card p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-neon-blue/20 rounded-lg">
            <Settings className="text-neon-blue" size={24} />
          </div>
          <div>
            <h2 className="text-xl font-bold text-terminal-text">Trading Configuration</h2>
            <p className="text-sm text-terminal-muted font-mono">Core Bot & Strategy Parameters</p>
          </div>
        </div>
        <button
          onClick={handleSave}
          disabled={!isModified || isSaving}
          className={`flex items-center space-x-2 px-6 py-3 rounded-lg transition-all duration-300 font-mono font-medium ${
            isModified && !isSaving
              ? 'bg-gradient-to-r from-neon-green to-neon-blue hover:from-neon-blue hover:to-neon-green text-white shadow-glow-green'
              : 'terminal-surface text-terminal-muted cursor-not-allowed border border-terminal-border'
          }`}
        >
          {isSaving ? <Loader2 className="animate-spin" size={16} /> : <Save size={16} />}
          <span>{isSaving ? 'Saving...' : 'Save Changes'}</span>
        </button>
      </div>

      {/* Risk Level Indicator */}
      <div className={`terminal-surface p-4 rounded-lg mb-6 border-l-4 ${
        riskLevel === 'high' ? 'border-l-neon-red bg-neon-red/5' :
        riskLevel === 'medium' ? 'border-l-neon-yellow bg-neon-yellow/5' :
        'border-l-neon-green bg-neon-green/5'
      }`}>
        <div className="flex items-center space-x-3">
          <AlertTriangle className={riskColor} size={20} />
          <div>
            <div className={`font-bold font-mono ${riskColor} capitalize`}>
              {riskLevel} Risk Profile
            </div>
            <div className="text-xs text-terminal-muted font-mono">
              Current risk per trade: {config.RISK_PERCENT}%
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-6">
        {/* Risk & Indicators */}
        <div className="terminal-surface rounded-lg p-5">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-neon-red/20 rounded-lg">
              <Shield className="text-neon-red" size={20} />
            </div>
            <h3 className="text-lg font-semibold text-terminal-text font-mono">Risk & Indicators</h3>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            
            <ConfigInput label="Risk per Trade (%)" value={config.RISK_PERCENT} name="RISK_PERCENT" onChange={handleChange} step={0.1} />
            <ConfigInput label="Take Profit Ratio" value={config.TP_RATIO} name="TP_RATIO" onChange={handleChange} step={0.1} />
            <ConfigInput label="RSI Period" value={config.RSI_PERIOD} name="RSI_PERIOD" onChange={handleChange} />
            <ConfigInput label="Stochastic Period" value={config.STOCHASTIC_PERIOD} name="STOCHASTIC_PERIOD" onChange={handleChange} />
            <ConfigInput label="Bollinger Period" value={config.BOLLINGER_PERIOD} name="BOLLINGER_PERIOD" onChange={handleChange} />
            <ConfigInput label="Bollinger StdDev" value={config.BOLLINGER_STD} name="BOLLINGER_STD" onChange={handleChange} />
            <ConfigInput label="MACD Fast" value={config.MACD_FAST} name="MACD_FAST" onChange={handleChange} />
            <ConfigInput label="MACD Slow" value={config.MACD_SLOW} name="MACD_SLOW" onChange={handleChange} />
            <ConfigInput label="MACD Signal" value={config.MACD_SIGNAL} name="MACD_SIGNAL" onChange={handleChange} />

          </div>
        </div>

      </div>
    </div>
  );
};

interface ConfigInputProps {
    label: string;
    value: number | string | undefined;
    name: keyof EditableConfig;
    onChange: (name: keyof EditableConfig, value: number) => void;
    step?: number;
    type?: string;
}

const ConfigInput: React.FC<ConfigInputProps> = ({ label, value, name, onChange, step = 1, type = "number" }) => (
    <div>
        <label className="block text-sm font-medium text-terminal-text mb-2 font-mono">
            {label}
        </label>
        <input
            type={type}
            step={step}
            value={value || ''}
            onChange={(e) => onChange(name, Number(e.target.value))}
            className="w-full px-4 py-3 bg-terminal-bg border border-terminal-border rounded-lg text-terminal-text font-mono focus:outline-none focus:ring-2 focus:ring-neon-blue focus:border-transparent transition-all duration-200"
        />
    </div>
);

export default ConfigPanel;