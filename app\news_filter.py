import requests
import os
from datetime import datetime, timedelta, date
from pathlib import Path
import json
from app.database import db
from app.config import config

class NewsFilter:
    """
    Fetches economic-calendar events and blocks trading around medium/high-impact news.
    Reads from MT5-exported `calendar.json`, updated every minute.
    """

    def __init__(self, buffer_minutes: int = 30, refresh_seconds: int = 60):
        self.buffer = timedelta(minutes=buffer_minutes)
        self.refresh = timedelta(seconds=refresh_seconds)
        self.cache = None
        self.expiry = None

    # ------------------------------------------------------------------
    # MetaQuotes calendar reader (local JSON)
    # ------------------------------------------------------------------
    def _fetch_mt5_calendar(self):
        # Try multiple possible calendar file locations
        possible_paths = []

        if config.CALENDAR_FILE_PATH:
            possible_paths.append(Path(config.CALENDAR_FILE_PATH))

        # Add common MT5 calendar file locations
        possible_paths.extend([
            Path("calendar.json"),  # Current directory
            Path("mt5_ea/calendar.json"),  # MT5 EA directory
            Path("data/calendar.json"),  # Data directory
            Path(r"C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\Common\Files\calendar.json"),
            Path(r"C:\ProgramData\MetaQuotes\Terminal\Common\Files\calendar.json"),
        ])

        for p in possible_paths:
            if p.exists():
                try:
                    # Check if we have read permissions
                    if not os.access(p, os.R_OK):
                        print(f"[NewsFilter] No read permission for: {p}")
                        continue

                    raw = json.loads(p.read_text(encoding="utf-8"))
                    parsed = []
                    for i, ev in enumerate(raw):
                        # Convert time "2025.06.21 14:30" → "2025-06-21 14:30"
                        ev_time = ev.get("time", "").replace(".", "-")
                        epoch_val = int(datetime.strptime(ev_time, "%Y-%m-%d %H:%M").timestamp()) if ev_time else None
                        parsed.append({
                            "event_id": ev.get("id"),
                            "time": ev_time,
                            "epoch": epoch_val,
                            "country": ev.get("country", ""),
                            "currency": ev.get("currency", ""),
                            "event": ev.get("event", ""),
                            "impact": ev.get("importance", "low").lower(),
                            "unit": ev.get("unit"),
                            "forecast": ev.get("forecast"),
                            "previous": ev.get("previous"),
                            "actual": ev.get("actual"),
                            "release_status": ev.get("release_status", "scheduled")
                        })
                    # Log to DB
                    try:
                        db.log_calendar_events(parsed)
                    except Exception as log_err:
                        print(f"[NewsFilter] DB log error: {log_err}")

                    print(f"[NewsFilter] Successfully loaded {len(parsed)} events from: {p}")
                    return parsed

                except PermissionError as e:
                    print(f"[NewsFilter] Permission denied accessing: {p}")
                    continue
                except json.JSONDecodeError as e:
                    print(f"[NewsFilter] JSON decode error in {p}: {e}")
                    continue
                except Exception as e:
                    print(f"[NewsFilter] Error reading {p}: {e}")
                    continue

        # If no calendar file found, return mock data for testing
        print("[NewsFilter] No calendar file found, using mock data")
        return self._get_mock_events()

    def _get_mock_events(self):
        """Return mock economic events for testing when calendar file is not available"""
        now = datetime.now()
        today = now.date()

        mock_events = [
            {
                "event_id": "mock_1",
                "time": f"{today.isoformat()} 08:30",
                "epoch": int((now.replace(hour=8, minute=30, second=0, microsecond=0)).timestamp()),
                "country": "US",
                "currency": "USD",
                "event": "Non-Farm Payrolls",
                "impact": "high",
                "unit": "K",
                "forecast": 200.0,
                "previous": 180.0,
                "actual": None,
                "release_status": "scheduled"
            },
            {
                "event_id": "mock_2",
                "time": f"{today.isoformat()} 14:00",
                "epoch": int((now.replace(hour=14, minute=0, second=0, microsecond=0)).timestamp()),
                "country": "EU",
                "currency": "EUR",
                "event": "ECB Interest Rate Decision",
                "impact": "high",
                "unit": "%",
                "forecast": 4.25,
                "previous": 4.25,
                "actual": None,
                "release_status": "scheduled"
            },
            {
                "event_id": "mock_3",
                "time": f"{today.isoformat()} 10:00",
                "epoch": int((now.replace(hour=10, minute=0, second=0, microsecond=0)).timestamp()),
                "country": "GB",
                "currency": "GBP",
                "event": "GDP Growth Rate",
                "impact": "medium",
                "unit": "%",
                "forecast": 0.2,
                "previous": 0.1,
                "actual": None,
                "release_status": "scheduled"
            }
        ]

        return mock_events

    # ------------------------------------------------------------------
    # Public interface
    # ------------------------------------------------------------------
    def get_todays_events(self):
        """Loads new events every `refresh_seconds`. No stale cache."""
        now = datetime.utcnow()
        if self.cache and self.expiry and now < self.expiry:
            return self.cache

        self.cache = self._fetch_mt5_calendar()
        self.expiry = now + self.refresh
        return self.cache

    def is_news_imminent(self, symbol: str) -> bool:
        """True if a medium/high impact event for this symbol is within ±buffer window."""
        events = self.get_todays_events()
        if not events:
            return False

        now_utc = datetime.utcnow()

        for ev in events:
            if ev["impact"] not in ["high", "medium"]:
                continue
            if ev["currency"] not in symbol.upper():
                continue

            # Handle format: "2025-06-21 14:30" or fallback "HH:MM"
            time_str = ev.get("time", "")
            if len(time_str) == 5:
                ev_datetime = datetime.strptime(
                    f"{date.today().isoformat()} {time_str}", "%Y-%m-%d %H:%M"
                )
            else:
                ev_datetime = datetime.strptime(time_str, "%Y-%m-%d %H:%M")

            if ev_datetime - self.buffer <= now_utc <= ev_datetime + self.buffer:
                print(f"[NewsFilter] ❌ NEWS BLOCK: '{ev['event']}' ({ev['country']}, {ev['impact']}) is imminent.")
                return True

        return False
