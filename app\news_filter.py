import requests
from datetime import datetime, timedelta, date
from pathlib import Path
import json
from app.database import db
from app.config import config

class NewsFilter:
    """
    Fetches economic-calendar events and blocks trading around medium/high-impact news.
    Reads from MT5-exported `calendar.json`, updated every minute.
    """

    def __init__(self, buffer_minutes: int = 30, refresh_seconds: int = 60):
        self.buffer = timedelta(minutes=buffer_minutes)
        self.refresh = timedelta(seconds=refresh_seconds)
        self.cache = None
        self.expiry = None

    # ------------------------------------------------------------------
    # MetaQuotes calendar reader (local JSON)
    # ------------------------------------------------------------------
    def _fetch_mt5_calendar(self):
        if config.CALENDAR_FILE_PATH:
            p = Path(config.CALENDAR_FILE_PATH)
            if p.exists():
                try:
                    raw = json.loads(p.read_text(encoding="utf-8"))
                    parsed = []
                    for i, ev in enumerate(raw):
                        # Convert time "2025.06.21 14:30" → "2025-06-21 14:30"
                        ev_time = ev.get("time", "").replace(".", "-")
                        epoch_val = int(datetime.strptime(ev_time, "%Y-%m-%d %H:%M").timestamp()) if ev_time else None
                        parsed.append({
                            "event_id": ev.get("id"),
                            "time": ev_time,
                            "epoch": epoch_val,
                            "country": ev.get("country", ""),
                            "currency": ev.get("currency", ""),
                            "event": ev.get("event", ""),
                            "impact": ev.get("importance", "low").lower(),
                            "unit": ev.get("unit"),
                            "forecast": ev.get("forecast"),
                            "previous": ev.get("previous"),
                            "actual": ev.get("actual"),
                            "release_status": ev.get("release_status", "scheduled")
                        })
                    # Log to DB
                    try:
                        db.log_calendar_events(parsed)
                    except Exception as log_err:
                        print(f"[NewsFilter] DB log error: {log_err}")
                    return parsed
                except Exception as e:
                    print(f"[NewsFilter] JSON parse error: {e}")
        return []

    # ------------------------------------------------------------------
    # Public interface
    # ------------------------------------------------------------------
    def get_todays_events(self):
        """Loads new events every `refresh_seconds`. No stale cache."""
        now = datetime.utcnow()
        if self.cache and self.expiry and now < self.expiry:
            return self.cache

        self.cache = self._fetch_mt5_calendar()
        self.expiry = now + self.refresh
        return self.cache

    def is_news_imminent(self, symbol: str) -> bool:
        """True if a medium/high impact event for this symbol is within ±buffer window."""
        events = self.get_todays_events()
        if not events:
            return False

        now_utc = datetime.utcnow()

        for ev in events:
            if ev["impact"] not in ["high", "medium"]:
                continue
            if ev["currency"] not in symbol.upper():
                continue

            # Handle format: "2025-06-21 14:30" or fallback "HH:MM"
            time_str = ev.get("time", "")
            if len(time_str) == 5:
                ev_datetime = datetime.strptime(
                    f"{date.today().isoformat()} {time_str}", "%Y-%m-%d %H:%M"
                )
            else:
                ev_datetime = datetime.strptime(time_str, "%Y-%m-%d %H:%M")

            if ev_datetime - self.buffer <= now_utc <= ev_datetime + self.buffer:
                print(f"[NewsFilter] ❌ NEWS BLOCK: '{ev['event']}' ({ev['country']}, {ev['impact']}) is imminent.")
                return True

        return False
