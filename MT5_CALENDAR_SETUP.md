# MT5 Calendar Setup Guide

## Issue
The NewsFilter is getting a permission error when trying to access the MT5 calendar file:
```
[NewsFilter] JSON parse error: [<PERSON>rr<PERSON> 13] Permission denied: 'C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\Common\Files'
```

## Solutions

### Option 1: Fix MT5 File Permissions (Recommended)

1. **Run MetaTrader 5 as Administrator**
   - Right-click on MT5 icon
   - Select "Run as administrator"
   - This ensures the EA can write to the Common Files directory

2. **Set Folder Permissions**
   ```
   C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\Common\Files
   ```
   - Right-click on the `Files` folder
   - Properties → Security → Edit
   - Add "Full Control" for your user account

3. **Install and Run the Calendar EA**
   - Copy `mt5_ea/CalendarToJSON.mq5` to your MT5 Experts folder
   - Compile and attach to any chart
   - The EA will create `calendar.json` every 60 seconds

### Option 2: Use Alternative Calendar File Location

1. **Set Custom Path in Environment**
   ```bash
   # In your .env file
   CALENDAR_FILE_PATH=C:\path\to\your\calendar.json
   ```

2. **Copy Calendar File to Project Directory**
   - The system will automatically look for `calendar.json` in the project root
   - A sample file has been created for you

### Option 3: Use Mock Data (Current Fallback)

The system now automatically falls back to mock economic events if no calendar file is found. This allows the bot to continue running without the MT5 calendar.

## Current Implementation

The NewsFilter now tries multiple locations in order:

1. **Environment Variable Path**: `CALENDAR_FILE_PATH`
2. **Project Directory**: `./calendar.json`
3. **MT5 EA Directory**: `./mt5_ea/calendar.json`
4. **Data Directory**: `./data/calendar.json`
5. **Common MT5 Locations**: Various AppData paths
6. **Mock Data**: Fallback if none found

## Testing the Fix

Run the integration test to verify the news filter is working:

```bash
python test_integrations.py
```

Look for the "News Events" test to pass.

## MT5 EA Setup (For Real Calendar Data)

1. **Open MetaTrader 5**
2. **Navigate to**: File → Open Data Folder → MQL5 → Experts
3. **Copy**: `mt5_ea/CalendarToJSON.mq5` to this folder
4. **Compile**: Press F7 or use MetaEditor
5. **Attach to Chart**: Drag the EA to any chart
6. **Enable Auto Trading**: Click the "AutoTrading" button
7. **Check Logs**: The EA will log calendar exports every minute

## Verification

The calendar file should contain JSON like:
```json
[
  {
    "id": "123",
    "time": "2025-01-15 14:30",
    "country": "US",
    "currency": "USD",
    "importance": "high",
    "event": "Non-Farm Payrolls",
    "forecast": 200.0,
    "previous": 180.0
  }
]
```

## Troubleshooting

1. **Permission Denied**: Run MT5 as Administrator
2. **File Not Found**: Check EA is running and creating the file
3. **JSON Parse Error**: Verify the calendar.json file is valid JSON
4. **No Events**: The mock data will be used automatically

The bot will continue to function with mock data even if the MT5 calendar is not available.
