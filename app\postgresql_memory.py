import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime
from app.config import config
from typing import Optional, List, Dict, Any


class PostgresMemory:
    """Lightweight wrapper around PostgreSQL for memory logging.
    If the database is unavailable, it will transparently fall back to an
    in-memory list so the rest of the application continues to function.
    """

    # Fallback shared store (class-level so all instances share the same list)
    _fallback_store: List[Dict[str, Any]] = []

    def __init__(self):
        # Default to disabled until a successful connection is made
        self.enabled = False

        try:
            self.conn = psycopg2.connect(
                host=config.POSTGRES_HOST,
                port=config.POSTGRES_PORT,
                dbname=config.POSTGRES_DB,
                user=config.POSTGRES_USER,
                password=config.POSTGRES_PASSWORD,
            )
            self.conn.autocommit = True
            self.ensure_tables()
            self.enabled = True
        except psycopg2.OperationalError as e:
            # Detect missing database and create it automatically
            if "does not exist" in str(e) and config.POSTGRES_DB:
                try:
                    bootstrap_conn = psycopg2.connect(
                        host=config.POSTGRES_HOST,
                        port=config.POSTGRES_PORT,
                        dbname="postgres",
                        user=config.POSTGRES_USER,
                        password=config.POSTGRES_PASSWORD,
                    )
                    bootstrap_conn.autocommit = True
                    with bootstrap_conn.cursor() as cur:
                        cur.execute(f"CREATE DATABASE {config.POSTGRES_DB};")
                    bootstrap_conn.close()
                    print(f"[PostgresMemory] Created missing database '{config.POSTGRES_DB}'. Reconnecting…")
                    # Retry connection
                    self.conn = psycopg2.connect(
                        host=config.POSTGRES_HOST,
                        port=config.POSTGRES_PORT,
                        dbname=config.POSTGRES_DB,
                        user=config.POSTGRES_USER,
                        password=config.POSTGRES_PASSWORD,
                    )
                    self.conn.autocommit = True
                    self.ensure_tables()
                    self.enabled = True
                except Exception as inner:
                    print(f"[PostgresMemory] Failed to create database automatically: {inner}")
                    self.conn = None
                    self.enabled = False
            else:
                print(
                    f"[PostgresMemory] WARNING: Could not connect to PostgreSQL ({e}). "
                    "Falling back to in-memory storage. Data will not persist across restarts."
                )
                self.conn = None
                self.enabled = False

    # ---------------------------------------------------------------------
    # Internal helpers
    # ---------------------------------------------------------------------
    def ensure_tables(self):
        if not self.enabled:
            return
        with self.conn, self.conn.cursor() as cur:
            cur.execute(
                """
                CREATE TABLE IF NOT EXISTS memory_logs (
                    id SERIAL PRIMARY KEY,
                    timestamp TIMESTAMP NOT NULL,
                    type VARCHAR(32) NOT NULL,
                    content TEXT NOT NULL,
                    meta JSONB
                );
                """
            )

    # ---------------------------------------------------------------------
    # Public API
    # ---------------------------------------------------------------------
    def store_memory(self, type_: str, content: str, meta: Optional[dict] = None):
        if self.enabled and self.conn:
            with self.conn, self.conn.cursor() as cur:
                cur.execute(
                    """
                    INSERT INTO memory_logs (timestamp, type, content, meta)
                    VALUES (%s, %s, %s, %s)
                    """,
                    (
                        datetime.now(),
                        type_,
                        content,
                        psycopg2.extras.Json(meta) if meta else None,
                    ),
                )
        else:
            # Fallback: store in class-level list
            self._fallback_store.append(
                {
                    "timestamp": datetime.now(),
                    "type": type_,
                    "content": content,
                    "meta": meta,
                }
            )

    def retrieve_memory(self, type_: Optional[str] = None, limit: int = 50):
        if self.enabled and self.conn:
            with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
                if type_:
                    cur.execute(
                        "SELECT * FROM memory_logs WHERE type = %s ORDER BY timestamp DESC LIMIT %s",
                        (type_, limit),
                    )
                else:
                    cur.execute(
                        "SELECT * FROM memory_logs ORDER BY timestamp DESC LIMIT %s", (limit,)
                    )
                return cur.fetchall()
        else:
            # Fallback: filter the in-memory list
            records = [r for r in self._fallback_store if (not type_ or r["type"] == type_)]
            records.sort(key=lambda x: x["timestamp"], reverse=True)
            return records[:limit]

    def search_memory(self, query: str, limit: int = 20):
        if self.enabled and self.conn:
            with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute(
                    """
                    SELECT * FROM memory_logs
                    WHERE content ILIKE %s
                    ORDER BY timestamp DESC
                    LIMIT %s
                    """,
                    (f"%{query}%", limit),
                )
                return cur.fetchall()
        else:
            results = [r for r in self._fallback_store if query.lower() in r["content"].lower()]
            results.sort(key=lambda x: x["timestamp"], reverse=True)
            return results[:limit]

# Usage example:
# memory = PostgresMemory()
# memory.store_memory('LLM', 'Thought about EURUSD', {'symbol': 'EURUSD'})
# logs = memory.retrieve_memory('LLM') 