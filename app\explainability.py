import shap
import numpy as np
import json
from datetime import datetime
from app.ai_brain import MLBrain
from app.database import db
from app.config import config
import random

class ExplainabilityEngine:
    """
    Generates structured, human-readable trade rationales using SHAP and GPT-4.
    """
    def __init__(self, ml_brain: MLBrain, sample_rate=0.1):
        self.ml_brain = ml_brain
        self.sample_rate = sample_rate
        self.openai_client = None
        if config.OPENAI_API_KEY:
            from openai import OpenAI
            self.openai_client = OpenAI(api_key=config.OPENAI_API_KEY)

    def should_explain(self):
        """Determines if an explanation should be generated based on the sample rate."""
        return random.random() < self.sample_rate

    def explain_trade(self, trade_id: int, features: dict, X_row: np.ndarray, model_confidence: float, action: str, symbol: str):
        """Generates a detailed explanation for a trade and stores it in the database."""
        # 1. Calculate SHAP values
        explainer = shap.TreeExplainer(self.ml_brain.model)
        shap_values = explainer.shap_values(X_row)
        
        class_map = {"HOLD": 0, "BUY": 1, "SELL": 2}
        class_idx = class_map.get(action, 0)
        
        feature_names = list(features.keys())
        shap_scores = shap_values[class_idx][0] if isinstance(shap_values, list) else shap_values[0]
        
        # Get top 4 features for more detail
        top_idx = np.argsort(np.abs(shap_scores))[-4:][::-1]
        top_features = [(feature_names[i], features[feature_names[i]], shap_scores[i]) for i in top_idx]

        # 2. Build detailed prompt for JSON output
        feature_details = []
        for name, value, score in top_features:
            impact_direction = "supports" if score > 0 else "opposes"
            feature_details.append(
                f"- Feature '{name}' with value {value:.3f} {impact_direction} the '{action}' decision (SHAP value: {score:.3f})."
            )
        
        prompt = f"""
        Analyze the following Forex trade decision for {symbol} and generate a JSON explanation.

        Decision Details:
        - Action: {action}
        - Model Confidence: {model_confidence:.2%}
        - Key Influencing Factors:
        {chr(10).join(feature_details)}

        JSON Output Format:
        {{
          "tradeId": "{trade_id}",
          "explanation": "A concise, expert-level summary of the trade rationale. Synthesize the factors into a coherent narrative.",
          "confidence": {int(model_confidence * 100)},
          "recommendation": "A clear, actionable recommendation, e.g., 'Strong Buy', 'Hold', 'Consider Exit'.",
          "factors": [
            {{
              "name": "Factor Name (e.g., RSI Divergence)",
              "impact": "A percentage (0-100) representing this factor's contribution. The sum of impacts for all factors should be 100.",
              "description": "A detailed sentence explaining how this specific factor's value contributed to the decision.",
              "type": "technical"
            }}
          ],
          "timestamp": "{datetime.now().isoformat()}"
        }}

        Instructions:
        1. Fill in the 'explanation' and 'recommendation' fields with insightful analysis.
        2. The 'factors' array must contain the top {len(top_features)} features provided.
        3. For each factor, invent a descriptive 'name' based on the feature (e.g., 'rsi' -> 'RSI Momentum').
        4. Distribute the 'impact' percentages among the factors, ensuring they sum to 100.
        5. The 'description' for each factor should be detailed and clear.
        6. All factors are 'technical' for this model.
        7. Ensure the output is a single, valid JSON object.
        """

        # 3. Get structured explanation
        explanation_json = None
        if self.openai_client:
            try:
                response = self.openai_client.chat.completions.create(
                    model="gpt-4-turbo",
                    messages=[{"role": "system", "content": "You are a world-class Forex trading analyst that provides explanations in JSON format."},
                              {"role": "user", "content": prompt}],
                    response_format={"type": "json_object"},
                    temperature=0.6,
                )
                explanation_json = response.choices[0].message.content
            except Exception as e:
                explanation_json = json.dumps({"error": f"GPT-4 error: {str(e)}"})
        else:
            # Create a basic JSON explanation if OpenAI is not available
            factors = [{"name": n, "impact": round(100/len(top_features)), "description": f"Feature {n} with value {v:.2f} contributed.", "type": "technical"} for n,v,_ in top_features]
            explanation_json = json.dumps({
                "tradeId": str(trade_id),
                "explanation": f"Decision based on top features: {', '.join([f[0] for f in top_features])}",
                "confidence": int(model_confidence * 100),
                "recommendation": f"Model recommends {action}",
                "factors": factors,
                "timestamp": datetime.now().isoformat()
            })

        # 4. Store in database
        if explanation_json:
            with db.conn:
                db.conn.execute(
                    "INSERT INTO trade_explanations (trade_id, explanation, created_at) VALUES (?, ?, ?)",
                        (trade_id, explanation_json, datetime.now().isoformat())
                )
                print(f"Stored structured explanation for trade {trade_id}.")
        else:
            print(f"Failed to generate explanation for trade {trade_id}.")