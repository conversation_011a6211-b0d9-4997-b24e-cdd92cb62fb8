import uvicorn
from threading import Thread
import time
import schedule
import Meta<PERSON><PERSON>r5 as mt5

from app.main import initialize_components, trade_cycle, reset_daily_protocols, anomaly_detection_cycle, auto_tuning_cycle
from app.api import app, current_backtest
from app.config import config
from app.backtester import Backtester
from datetime import datetime, timedelta

# --- Global State ---
# This dictionary holds the shared state and components for the application.
app_state = {
    "trading_enabled": True,
    "scheduler_thread": None,
    "components": {}
}

def run_scheduler(scheduler):
    """The function that runs in the background thread."""
    
    # Run an initial trade cycle immediately
    scheduler.run_all(delay_seconds=0) 
    
    while True:
        if app_state["trading_enabled"]:
            scheduler.run_pending()
        time.sleep(1)

def main():
    """
    The main entry point for the entire application.
    Initializes components, starts the FastAPI server, and runs the trading loop.
    """
    print("Initializing application...")

    # --- Initialize MT5 ---
    if not mt5.initialize(
        login=config.MT5_LOGIN, password=config.MT5_PASSWORD, 
        server=config.MT5_SERVER, path=config.MT5_PATH
    ):
        print("MT5 initialization failed.")
        mt5.shutdown()
        return
    print("MT5 Connected Successfully!")

    # --- Initialize Components ---
    app_state["components"] = initialize_components()
    print("Bot components initialized.")
    
    # --- Share State with FastAPI ---
    # This makes the app_state accessible from within API endpoints
    app.state.shared_state = app_state

    # --- Schedule Trading Tasks ---
    trading_scheduler = schedule.Scheduler()
    components = app_state["components"]
    
    trading_scheduler.every(15).minutes.do(
        trade_cycle, components['feeder'], components['hybrid_brain'], 
        components['executor'], components['news_filter'], 
        components['anomaly_detector'], components['explain_engine'], 
        components['ml_brain']
    )
    trading_scheduler.every().day.at("00:01").do(reset_daily_protocols)
    trading_scheduler.every(4).hours.do(anomaly_detection_cycle, components['anomaly_detector'])
    trading_scheduler.every().sunday.at("04:00").do(auto_tuning_cycle, components['auto_tuner'])
    # Schedule automatic backtests every 6 hours
    trading_scheduler.every(6).hours.do(auto_backtest_cycle)
    
    # --- Start Background Scheduler ---
    app_state["scheduler_thread"] = Thread(target=run_scheduler, args=(trading_scheduler,), daemon=True)
    app_state["scheduler_thread"].start()
    print("Trading scheduler started in a background thread.")
    
    # --- Start FastAPI Server ---
    try:
        uvicorn.run(app, host="127.0.0.1", port=8000)
    finally:
        print("Shutting down...")
        mt5.shutdown()
        print("MT5 connection closed.")

def auto_backtest_cycle():
    """Runs an automated backtest for each configured symbol over the last 90 days."""
    global current_backtest
    try:
        # Choose a symbol to backtest (could iterate all)
        for symbol in config.SYMBOLS:
            start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
            end_date = datetime.now().strftime('%Y-%m-%d')
            print(f"[AutoBacktest] Starting backtest for {symbol} ({start_date} -> {end_date})")
            current_backtest = {
                "symbol": symbol,
                "strategy": "Hybrid",
                "progress": 0,
                "start_time": datetime.now().isoformat(),
                "estimated_completion": (datetime.now() + timedelta(minutes=1)).isoformat(),
            }
            backtester = Backtester(symbol, start_date, end_date)
            result = backtester.run()
            current_backtest = None  # Clear when done
            print(f"[AutoBacktest] Completed backtest for {symbol}. Trades: {len(result.get('trades', []))}")
    except Exception as e:
        print(f"[AutoBacktest] Error during backtest: {e}")

if __name__ == "__main__":
    main() 