#!/usr/bin/env python3
"""
Integration test script for NexGen Forex Trading Bot
Tests the backend API endpoints and component integrations
"""

import requests
import json
import sys
from typing import Dict, Any

# API Base URL
API_BASE_URL = "http://localhost:8000"

def test_endpoint(endpoint: str, method: str = "GET", data: Dict[Any, Any] = None) -> Dict[str, Any]:
    """Test a single API endpoint"""
    url = f"{API_BASE_URL}{endpoint}"
    
    try:
        if method == "GET":
            response = requests.get(url, timeout=10)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=10)
        else:
            return {"status": "error", "message": f"Unsupported method: {method}"}
        
        return {
            "status": "success" if response.status_code == 200 else "error",
            "status_code": response.status_code,
            "response": response.json() if response.headers.get('content-type', '').startswith('application/json') else str(response.content)
        }
    except requests.exceptions.ConnectionError:
        return {"status": "error", "message": "Connection refused - is the server running?"}
    except requests.exceptions.Timeout:
        return {"status": "error", "message": "Request timeout"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

def run_integration_tests():
    """Run all integration tests"""
    print("🚀 Starting NexGen Forex Trading Bot Integration Tests")
    print("=" * 60)
    
    tests = [
        # Basic endpoints
        {"name": "Root Endpoint", "endpoint": "/", "method": "GET"},
        {"name": "System Health", "endpoint": "/system_health", "method": "GET"},
        {"name": "Bot Status", "endpoint": "/bot/status", "method": "GET"},
        
        # Configuration
        {"name": "Get Config", "endpoint": "/config", "method": "GET"},
        
        # Trading data
        {"name": "Account Stats", "endpoint": "/account_stats", "method": "GET"},
        {"name": "Open Trades", "endpoint": "/open_trades", "method": "GET"},
        {"name": "Trade Logs", "endpoint": "/trade_logs", "method": "GET"},
        
        # News and sentiment
        {"name": "News Events", "endpoint": "/news", "method": "GET"},
        {"name": "News Sentiment", "endpoint": "/news_sentiment?symbol=EURUSD", "method": "GET"},
        
        # Anomaly detection
        {"name": "Anomaly Status", "endpoint": "/anomaly_status", "method": "GET"},
        
        # Backtest
        {"name": "Backtest Results", "endpoint": "/backtest_results", "method": "GET"},
        
        # Mode management
        {"name": "Get Mode", "endpoint": "/get_mode", "method": "GET"},
        {"name": "Set Mode", "endpoint": "/set_mode", "method": "POST", "data": {"mode": "forward"}},
        
        # Voice endpoints
        {"name": "Voice Options", "endpoint": "/voices", "method": "GET"},
        
        # Chat endpoint
        {"name": "Chat", "endpoint": "/chat", "method": "POST", "data": {"message": "Hello, test message"}},
        
        # Memory endpoints
        {"name": "Query Memory", "endpoint": "/memory", "method": "POST", "data": {"limit": 5}},
        {"name": "Summarize Memory", "endpoint": "/summarize", "method": "POST"},
        
        # Reasoning
        {"name": "Run Reasoning", "endpoint": "/run-reasoning", "method": "POST"},
        
        # Price data
        {"name": "Price Data", "endpoint": "/price_data", "method": "POST", 
         "data": {"symbol": "EURUSD", "timeframe": "M15", "limit": 100}},
    ]
    
    results = []
    passed = 0
    failed = 0
    
    for test in tests:
        print(f"\n📊 Testing: {test['name']}")
        result = test_endpoint(
            test["endpoint"], 
            test.get("method", "GET"), 
            test.get("data")
        )
        
        if result["status"] == "success":
            print(f"   ✅ PASSED (Status: {result['status_code']})")
            passed += 1
        else:
            print(f"   ❌ FAILED: {result.get('message', 'Unknown error')}")
            if 'status_code' in result:
                print(f"      Status Code: {result['status_code']}")
            failed += 1
        
        results.append({
            "test": test["name"],
            "endpoint": test["endpoint"],
            "result": result
        })
    
    print("\n" + "=" * 60)
    print(f"📈 Test Results Summary:")
    print(f"   ✅ Passed: {passed}")
    print(f"   ❌ Failed: {failed}")
    print(f"   📊 Total: {passed + failed}")
    print(f"   🎯 Success Rate: {(passed / (passed + failed) * 100):.1f}%")
    
    if failed > 0:
        print(f"\n❌ Failed Tests:")
        for result in results:
            if result["result"]["status"] != "success":
                print(f"   - {result['test']}: {result['result'].get('message', 'Unknown error')}")
    
    return passed, failed

if __name__ == "__main__":
    print("Make sure the backend server is running on http://localhost:8000")
    print("You can start it with: python run.py")
    print()
    
    try:
        passed, failed = run_integration_tests()
        sys.exit(0 if failed == 0 else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Tests interrupted by user")
        sys.exit(1)
