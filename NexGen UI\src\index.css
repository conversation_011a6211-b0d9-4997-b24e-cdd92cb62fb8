@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-terminal-border;
  }
  
  body {
    @apply font-sans antialiased;
    line-height: 1.25;
    background: linear-gradient(135deg, #0a0a0f 0%, #111118 50%, #0f0f1a 100%);
    background-attachment: fixed;
  }
  
  h1, h2, h3, h4, h5, h6 {
    line-height: 1.2;
  }
}

@layer components {
  .terminal-card {
    @apply bg-terminal-card/80 backdrop-blur-xl border border-terminal-border/60 rounded-2xl shadow-terminal;
    background-image: 
      radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(168, 85, 247, 0.03) 0%, transparent 50%);
  }
  
  .terminal-surface {
    @apply bg-terminal-surface/60 backdrop-blur-sm border border-terminal-border/40 rounded-xl;
  }
  
  .neon-text {
    text-shadow: 
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 15px currentColor;
  }
  
  .profit-glow {
    @apply shadow-glow-green;
  }
  
  .loss-glow {
    @apply shadow-glow-red;
  }
  
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 130, 246, 0.5) rgba(26, 26, 36, 0.5);
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-terminal-card/30 rounded-full;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-terminal-accent/60 rounded-full;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    @apply bg-terminal-accent;
  }
  
  /* Enhanced glass morphism effect */
  .glass-effect {
    background: rgba(26, 26, 36, 0.4);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  /* Improved glow effects */
  .shadow-glow-blue {
    box-shadow: 
      0 0 20px rgba(0, 212, 255, 0.2),
      0 0 40px rgba(0, 212, 255, 0.1),
      0 4px 20px rgba(0, 0, 0, 0.3);
  }
  
  .shadow-glow-purple {
    box-shadow: 
      0 0 20px rgba(168, 85, 247, 0.2),
      0 0 40px rgba(168, 85, 247, 0.1),
      0 4px 20px rgba(0, 0, 0, 0.3);
  }
  
  .shadow-glow-green {
    box-shadow: 
      0 0 20px rgba(0, 255, 136, 0.2),
      0 0 40px rgba(0, 255, 136, 0.1),
      0 4px 20px rgba(0, 0, 0, 0.3);
  }
  
  .shadow-glow-red {
    box-shadow: 
      0 0 20px rgba(255, 71, 87, 0.2),
      0 0 40px rgba(255, 71, 87, 0.1),
      0 4px 20px rgba(0, 0, 0, 0.3);
  }
  
  .shadow-glow-yellow {
    box-shadow: 
      0 0 20px rgba(255, 215, 0, 0.2),
      0 0 40px rgba(255, 215, 0, 0.1),
      0 4px 20px rgba(0, 0, 0, 0.3);
  }
  
  .shadow-glow-orange {
    box-shadow: 
      0 0 20px rgba(255, 107, 53, 0.2),
      0 0 40px rgba(255, 107, 53, 0.1),
      0 4px 20px rgba(0, 0, 0, 0.3);
  }
}

@layer utilities {
  .text-shadow-glow {
    text-shadow: 0 0 10px currentColor;
  }
  
  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }
  
  @keyframes glow {
    from {
      text-shadow: 0 0 5px currentColor;
    }
    to {
      text-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
    }
  }
}