import React, { useState, useEffect } from 'react';
import { AlertTriangle, Shield, TrendingUp, Activity, Zap, Eye, Loader2, ServerCrash } from 'lucide-react';
import { api, AnomalyStatus } from '../services/api';

const AnomalyDetector: React.FC = () => {
  const [anomalyData, setAnomalyData] = useState<AnomalyStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStatus = async () => {
      try {
        // Don't set loading to true on interval fetches
        if (isLoading) setIsLoading(true);
        const data = await api.getAnomalyStatus();
        setAnomalyData(data);
        setError(null);
      } catch (err) {
        setError("Failed to fetch anomaly status.");
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStatus(); // Initial fetch
    const interval = setInterval(fetchStatus, 30 * 1000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, []);


  if (isLoading) {
    return (
        <div className="terminal-card p-6 flex items-center justify-center h-full">
            <Loader2 className="animate-spin text-neon-orange" size={48} />
            <p className="ml-4 text-lg font-mono text-terminal-text">Loading Anomaly Data...</p>
        </div>
    );
  }

  if (error || !anomalyData) {
    return (
      <div className="terminal-card p-6 flex flex-col items-center justify-center h-full bg-neon-red/10 border border-neon-red">
        <ServerCrash className="text-neon-red" size={48} />
        <p className="mt-4 text-lg font-mono text-neon-red">Error</p>
        <p className="text-sm font-mono text-terminal-muted">{error || 'Could not load anomaly data.'}</p>
      </div>
    );
  }

  const getScoreColor = (score: number) => {
    if (score < 0.3) return 'text-neon-green';
    if (score < 0.6) return 'text-neon-yellow';
    return 'text-neon-red';
  };

  const getBackgroundColor = (score: number) => {
    if (score < 0.3) return 'bg-neon-green/10 border-neon-green/30';
    if (score < 0.6) return 'bg-neon-yellow/10 border-neon-yellow/30';
    return 'bg-neon-red/10 border-neon-red/30';
  };

  const getIcon = (level: string) => {
    switch (level) {
      case 'low': return <Shield className="text-neon-green" size={20} />;
      case 'medium': return <Activity className="text-neon-yellow" size={20} />;
      case 'high': return <AlertTriangle className="text-neon-red" size={20} />;
      default: return <Shield className="text-neon-green" size={20} />;
    }
  };

  return (
    <div className="terminal-card p-6">
      <div className="flex items-center space-x-3 mb-6">
        <div className="p-2 bg-neon-orange/20 rounded-lg">
          <Eye className="text-neon-orange" size={24} />
        </div>
        <div>
          <h2 className="text-xl font-bold text-terminal-text">Anomaly Detection</h2>
          <p className="text-sm text-terminal-muted font-mono">AI-Powered Market Analysis</p>
        </div>
      </div>

      {/* Main Anomaly Display */}
      <div className={`rounded-xl p-6 border-2 mb-6 ${getBackgroundColor(anomalyData.score)} relative overflow-hidden`}>
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse"></div>
        
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              {getIcon(anomalyData.level)}
              <div>
                <span className="text-terminal-text font-bold text-lg capitalize">{anomalyData.level} Risk</span>
                <div className="text-xs text-terminal-muted font-mono">Confidence: {anomalyData.confidence.toFixed(0)}%</div>
              </div>
            </div>
            <div className="text-right">
              <div className={`text-4xl font-bold font-mono ${getScoreColor(anomalyData.score)} neon-text`}>
                {(anomalyData.score * 100).toFixed(1)}%
              </div>
              <div className="text-xs text-terminal-muted font-mono">Anomaly Score</div>
            </div>
          </div>

          <div className="space-y-2">
            {anomalyData.indicators.map((indicator, index) => (
              <div key={index} className="flex items-center space-x-3 text-sm text-terminal-text">
                <div className={`w-2 h-2 rounded-full ${
                  anomalyData.level === 'low' ? 'bg-neon-green' :
                  anomalyData.level === 'medium' ? 'bg-neon-yellow' : 'bg-neon-red'
                } animate-pulse`}></div>
                <span className="font-mono">{indicator}</span>
              </div>
            ))}
          </div>

          <div className="mt-4 text-xs text-terminal-muted font-mono">
            Last updated: {new Date(anomalyData.timestamp).toLocaleString()}
          </div>
        </div>
      </div>

      {/* Score History Chart */}
      <div className="terminal-surface p-4 rounded-lg">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-terminal-text font-mono">Score History</h3>
          <Zap className="text-neon-blue" size={16} />
        </div>
        <div className="flex items-end space-x-1 h-20">
          {anomalyData.history.map((score, index) => (
            <div
              key={index}
              className="flex-1 rounded-t transition-all duration-500 hover:opacity-100 relative group"
              style={{ 
                height: `${score * 100}%`,
                background: score < 0.3 
                  ? 'linear-gradient(to top, #00ff88, #00d4ff)' 
                  : score < 0.6 
                    ? 'linear-gradient(to top, #ffd700, #ff6b35)'
                    : 'linear-gradient(to top, #ff4757, #ff6b35)',
                opacity: 0.7
              }}
            >
              <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-terminal-card px-2 py-1 rounded text-xs font-mono opacity-0 group-hover:opacity-100 transition-opacity">
                {(score * 100).toFixed(1)}%
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-2 gap-3">
        <button className="flex items-center justify-center space-x-2 bg-gradient-to-r from-neon-blue to-neon-purple hover:from-neon-purple hover:to-neon-blue text-white py-3 px-4 rounded-lg transition-all duration-300 font-mono font-medium">
          <Activity size={16} />
          <span>Refresh</span>
        </button>
        <button className="flex items-center justify-center space-x-2 terminal-surface hover:bg-terminal-card text-terminal-text py-3 px-4 rounded-lg transition-all duration-300 border border-terminal-border font-mono font-medium">
          <Eye size={16} />
          <span>Details</span>
        </button>
      </div>
    </div>
  );
};

export default AnomalyDetector;