// Advanced microphone handling utility inspired by Elements AI
// Provides robust microphone access, recording and speech recognition capabilities

type MicrophoneState = 'inactive' | 'recording' | 'paused' | 'error';

interface MicrophoneOptions {
  continuous?: boolean;
  interimResults?: boolean;
  lang?: string;
  maxAlternatives?: number;
  visualFeedback?: boolean;
}

export interface MicrophoneManager {
  state: MicrophoneState;
  isRecording: boolean;
  transcript: string;
  interimTranscript: string;
  error: Error | null;
  startRecording: () => Promise<void>;
  stopRecording: () => void;
  pauseRecording: () => void;
  resumeRecording: () => void;
  getAudioLevel: () => number;
  clearTranscript: () => void;
}

// Add TypeScript interface for SpeechRecognition
// This ensures we have proper type checking
interface SpeechRecognitionEvent extends Event {
  results: SpeechRecognitionResultList;
  resultIndex: number;
  error: string;
}

interface SpeechRecognitionResultList {
  length: number;
  item(index: number): SpeechRecognitionResult;
  [index: number]: SpeechRecognitionResult;
}

interface SpeechRecognitionResult {
  length: number;
  item(index: number): SpeechRecognitionAlternative;
  [index: number]: SpeechRecognitionAlternative;
  isFinal: boolean;
}

interface SpeechRecognitionAlternative {
  transcript: string;
  confidence: number;
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  maxAlternatives: number;
  onaudioend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onaudiostart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onerror: ((this: SpeechRecognition, ev: Event & { error: string }) => any) | null;
  onnomatch: ((this: SpeechRecognition, ev: Event) => any) | null;
  onresult: ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => any) | null;
  onsoundend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onsoundstart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onspeechend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onspeechstart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onstart: ((this: SpeechRecognition, ev: Event) => any) | null;
  start(): void;
  stop(): void;
  abort(): void;
}

// Declare global types
declare global {
  interface Window {
    SpeechRecognition: new () => SpeechRecognition;
    webkitSpeechRecognition: new () => SpeechRecognition;
    AudioContext: typeof AudioContext;
    webkitAudioContext: typeof AudioContext;
  }
}

export function useMicrophone(options: MicrophoneOptions = {}): MicrophoneManager {
  let recognition: SpeechRecognition | null = null;
  let audioContext: AudioContext | null = null;
  let audioAnalyser: AnalyserNode | null = null;
  let microphone: MediaStreamAudioSourceNode | null = null;
  let audioData: Uint8Array | null = null;
  let mediaStream: MediaStream | null = null;
  
  // State variables
  let state: MicrophoneState = 'inactive';
  let transcript = '';
  let interimTranscript = '';
  let error: Error | null = null;
  let isRecording = false;
  
  // Check if browser supports SpeechRecognition
  const SpeechRecognitionAPI = window.SpeechRecognition || window.webkitSpeechRecognition;
  const hasSpeechRecognition = !!SpeechRecognitionAPI;

  // Setup speech recognition
  const setupRecognition = () => {
    if (!hasSpeechRecognition) {
      const err = new Error('Speech recognition not supported in this browser');
      state = 'error';
      error = err;
      console.error(err);
      return false;
    }
    
    try {
      recognition = new SpeechRecognitionAPI();
      
      if (recognition) {
        recognition.continuous = options.continuous ?? true;
        recognition.interimResults = options.interimResults ?? true;
        recognition.lang = options.lang ?? 'en-US';
        recognition.maxAlternatives = options.maxAlternatives ?? 1;

        recognition.onstart = () => {
          state = 'recording';
          isRecording = true;
          error = null;
        };

        recognition.onresult = (event) => {
          interimTranscript = '';
          let finalTranscript = '';

          for (let i = event.resultIndex; i < event.results.length; i++) {
            const result = event.results[i];
            if (result.isFinal) {
              finalTranscript += result[0].transcript;
            } else {
              interimTranscript += result[0].transcript;
            }
          }

          if (finalTranscript) {
            transcript += finalTranscript + ' ';
          }
        };

        recognition.onerror = (event) => {
          console.error('Speech recognition error', event.error);
          error = new Error(`Speech recognition error: ${event.error}`);
          if (event.error === 'not-allowed') {
            state = 'error';
            isRecording = false;
          }
        };

        recognition.onend = () => {
          if (state !== 'error') {
            state = 'inactive';
            isRecording = false;
          }
        };
      }

      return true;
    } catch (err) {
      console.error('Error setting up speech recognition', err);
      error = err instanceof Error ? err : new Error('Unknown error setting up speech recognition');
      state = 'error';
      return false;
    }
  };

  // Setup audio context for visualizations and level detection
  const setupAudioContext = async (): Promise<boolean> => {
    try {
      if (!options.visualFeedback) return true;
      
      // Request microphone access
      mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      // Create audio context
      audioContext = new (window.AudioContext || window.webkitAudioContext)();
      audioAnalyser = audioContext.createAnalyser();
      audioAnalyser.fftSize = 256;
      
      // Connect microphone to analyzer
      microphone = audioContext.createMediaStreamSource(mediaStream);
      microphone.connect(audioAnalyser);
      
      // Create data array for analyzer
      audioData = new Uint8Array(audioAnalyser.frequencyBinCount);
      
      return true;
    } catch (err) {
      console.error('Error setting up audio context', err);
      error = err instanceof Error ? err : new Error('Error accessing microphone');
      state = 'error';
      return false;
    }
  };

  // Start recording
  const startRecording = async (): Promise<void> => {
    if (state === 'recording') return;
    
    // Reset error state if trying again
    if (state === 'error') {
      error = null;
    }
    
    try {
      // Set up audio context first to ensure microphone permission
      const audioContextReady = await setupAudioContext();
      if (!audioContextReady) return;
      
      // Then set up speech recognition
      const recognitionReady = setupRecognition();
      if (!recognitionReady) return;
      
      // Start recognition
      if (recognition) {
        recognition.start();
      }
    } catch (err) {
      console.error('Error starting recording', err);
      error = err instanceof Error ? err : new Error('Unknown error starting recording');
      state = 'error';
    }
  };

  // Stop recording
  const stopRecording = () => {
    if (state !== 'recording' && state !== 'paused') return;
    
    try {
      if (recognition) {
        recognition.stop();
      }
      state = 'inactive';
      isRecording = false;
      
      // Clean up audio context
      if (microphone) {
        microphone.disconnect();
        microphone = null;
      }
      
      if (mediaStream) {
        mediaStream.getTracks().forEach(track => track.stop());
        mediaStream = null;
      }
      
      if (audioContext && audioContext.state !== 'closed') {
        audioContext.close();
        audioContext = null;
      }
      
      audioAnalyser = null;
      audioData = null;
    } catch (err) {
      console.error('Error stopping recording', err);
      error = err instanceof Error ? err : new Error('Unknown error stopping recording');
    }
  };

  // Pause recording
  const pauseRecording = () => {
    if (state !== 'recording') return;
    
    try {
      if (recognition) {
        recognition.stop();
      }
      state = 'paused';
      isRecording = false;
    } catch (err) {
      console.error('Error pausing recording', err);
      error = err instanceof Error ? err : new Error('Unknown error pausing recording');
    }
  };

  // Resume recording
  const resumeRecording = () => {
    if (state !== 'paused') return;
    
    try {
      if (recognition) {
        recognition.start();
      }
      state = 'recording';
      isRecording = true;
    } catch (err) {
      console.error('Error resuming recording', err);
      error = err instanceof Error ? err : new Error('Unknown error resuming recording');
      state = 'error';
    }
  };

  // Get current audio level (for visualizations)
  const getAudioLevel = (): number => {
    if (!audioAnalyser || !audioData) return 0;
    
    audioAnalyser.getByteFrequencyData(audioData);
    
    // Calculate average level
    let sum = 0;
    for (let i = 0; i < audioData.length; i++) {
      sum += audioData[i];
    }
    
    return sum / audioData.length / 255; // Normalize to 0-1
  };

  // Clear transcript
  const clearTranscript = () => {
    transcript = '';
    interimTranscript = '';
  };

  return {
    state,
    isRecording,
    transcript,
    interimTranscript,
    error,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    getAudioLevel,
    clearTranscript
  };
} 