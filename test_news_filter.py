#!/usr/bin/env python3
"""
Test script for NewsFilter to verify the permission fix
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.news_filter import NewsFilter
from app.config import config

def test_news_filter():
    print("🧪 Testing NewsFilter with permission fix...")
    print("=" * 50)
    
    try:
        # Create NewsFilter instance
        news_filter = NewsFilter()
        print("✅ NewsFilter instance created successfully")
        
        # Test getting today's events
        print("\n📅 Testing get_todays_events()...")
        events = news_filter.get_todays_events()
        
        if events:
            print(f"✅ Successfully loaded {len(events)} events")
            
            # Show first few events
            for i, event in enumerate(events[:3]):
                print(f"   Event {i+1}: {event.get('event', 'Unknown')} ({event.get('currency', 'N/A')}) - {event.get('impact', 'low')} impact")
        else:
            print("⚠️  No events loaded (this might be normal if no calendar file exists)")
        
        # Test news imminent check
        print("\n🚨 Testing is_news_imminent()...")
        for symbol in ['EURUSD', 'GBPUSD', 'USDJPY']:
            is_imminent = news_filter.is_news_imminent(symbol)
            status = "🔴 BLOCKED" if is_imminent else "🟢 CLEAR"
            print(f"   {symbol}: {status}")
        
        print("\n✅ NewsFilter test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ NewsFilter test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_calendar_file_access():
    print("\n🔍 Testing calendar file access...")
    print("=" * 50)
    
    from pathlib import Path
    
    # Test possible calendar file locations
    possible_paths = [
        Path("calendar.json"),
        Path("mt5_ea/calendar.json"),
        Path("data/calendar.json"),
    ]
    
    if config.CALENDAR_FILE_PATH:
        possible_paths.insert(0, Path(config.CALENDAR_FILE_PATH))
    
    for path in possible_paths:
        if path.exists():
            try:
                if os.access(path, os.R_OK):
                    print(f"✅ Can read: {path}")
                    # Try to read the file
                    content = path.read_text(encoding="utf-8")
                    print(f"   File size: {len(content)} characters")
                else:
                    print(f"❌ No read permission: {path}")
            except Exception as e:
                print(f"❌ Error accessing {path}: {e}")
        else:
            print(f"⚠️  File not found: {path}")
    
    print(f"\n📝 CALENDAR_FILE_PATH config: {config.CALENDAR_FILE_PATH or 'Not set'}")

if __name__ == "__main__":
    print("🚀 NewsFilter Permission Fix Test")
    print("This test verifies that the NewsFilter can handle permission issues gracefully")
    print()
    
    # Test calendar file access
    test_calendar_file_access()
    
    # Test NewsFilter functionality
    success = test_news_filter()
    
    if success:
        print("\n🎉 All tests passed! The NewsFilter permission issue has been resolved.")
        print("\n💡 Tips:")
        print("   - The system will use mock data if no calendar file is available")
        print("   - For real MT5 calendar data, see MT5_CALENDAR_SETUP.md")
        print("   - The bot will continue to function normally")
    else:
        print("\n❌ Some tests failed. Check the error messages above.")
        sys.exit(1)
