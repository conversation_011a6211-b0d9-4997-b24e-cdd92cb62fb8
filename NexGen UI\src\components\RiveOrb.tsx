import React, { useEffect } from 'react';
import { useRive, Layout, Fit, Alignment } from '@rive-app/react-webgl';

type OrbState = 'listening' | 'thinking' | 'speaking' | 'asleep';

interface RiveOrbProps {
  state: OrbState;
  width?: number;
  height?: number;
}

const STATE_MACHINE = 'State Machine 1'; // Default in Elements AI Demo orb

const stateMap: Record<OrbState, number> = {
  asleep: 0,
  listening: 1,
  thinking: 2,
  speaking: 3,
};

export const RiveOrb: React.FC<RiveOrbProps> = ({ state, width = 200, height = 200 }) => {
  const { rive, RiveComponent } = useRive({
    src: '/command-2.0.riv',
    stateMachines: STATE_MACHINE,
    autoplay: true,
    layout: new Layout({ fit: Fit.Contain, alignment: Alignment.Center }),
  });

  useEffect(() => {
    if (rive) {
      const inputs = rive.stateMachineInputs(STATE_MACHINE);
      const stateInput = inputs.find(input => input.name === 'state');
      if (stateInput) {
        stateInput.value = stateMap[state];
      }
    }
  }, [state, rive]);

  return <RiveComponent style={{ width, height }} />;
}; 