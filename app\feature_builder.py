from datetime import datetime
from app.database import db
import numpy as np

def get_next_event(symbol, now=None, window=60):
    now = now or datetime.utcnow()
    currency = symbol[-3:]
    sql = """
        SELECT * FROM calendar_events
        WHERE currency = %s AND time >= %s
        ORDER BY time ASC LIMIT 1
    """
    with db.conn.cursor() as cur:
        cur.execute(sql, (currency, now))
        row = cur.fetchone()
        if row:
            minutes_to_event = (row[2] - now).total_seconds() / 60
            return {
                "importance": {"high": 3, "medium": 2, "low": 1}.get(row[6], 0),
                "minutes_to_event": minutes_to_event,
                "actual": row[11],
                "forecast": row[9],
                "event_time": row[2],
                "event": row[8]
            }
    return {"importance": 0, "minutes_to_event": 9999, "actual": None, "forecast": None, "event_time": None, "event": None}

# Sentiment/news modules disabled (MarketAux & Yahoo removed)
def get_marketaux_sentiment(symbol, window=30):
    return 0.0

def get_yahoo_headlines(symbol, window=30):
    return 0, 0.0

def build_features(symbol, ta_features=None, now=None):
    now = now or datetime.utcnow()
    event = get_next_event(symbol, now)
    marketaux_sentiment = get_marketaux_sentiment(symbol)
    headline_count, headline_avg_sentiment = get_yahoo_headlines(symbol)
    ta_features = ta_features or {}

    features = {
        "event_importance": event["importance"],
        "minutes_to_event": event["minutes_to_event"],
        "surprise_pct": ((event["actual"] - event["forecast"]) / abs(event["forecast"]))
            if event["actual"] is not None and event["forecast"] else 0.0,
        "marketaux_sentiment": marketaux_sentiment,
        "headline_count": headline_count,
        "headline_avg_sentiment": headline_avg_sentiment,
        **ta_features
    }
    return features 