# Prompt Templates

This folder contains prompt templates for the LLM agent, tailored for different trading strategies and agent tasks.

## Usage
- Store each prompt as a `.txt`, `.json`, or `.yaml` file.
- Name files by strategy or task, e.g.:
  - `scalping.txt`
  - `trend_trading.txt`
  - `zone_sniping.txt`
  - `self_review.txt`
  - `summarization.txt`

## Example (scalping.txt)
```
You are an expert scalping trader. Given the following indicators and market context, decide whether to BUY, SELL, or HOLD. Explain your reasoning in detail.

Indicators:
{indicators}
Market context:
{context}
```

## How to Add New Templates
1. Create a new file in this folder for your strategy or task.
2. Use curly braces `{}` for variables to be filled in by the agent code.
3. Reference these templates in your agent modules as needed. 