import { useState, useEffect, useRef, useCallback } from 'react';
import { useMicrophone, MicrophoneManager } from './microphone';
import { WaveformVisualizer } from './waveformVisualizer';

interface SpeechRecognitionOptions {
  continuous?: boolean;
  interimResults?: boolean;
  lang?: string;
  visualize?: boolean;
  visualizerOptions?: {
    lineColor?: string;
    lineWidth?: number;
    backgroundColor?: string;
  };
  autoStart?: boolean;
}

interface SpeechRecognitionHook {
  isListening: boolean;
  transcript: string;
  interimTranscript: string;
  error: Error | null;
  startListening: () => Promise<void>;
  stopListening: () => void;
  resetTranscript: () => void;
  visualizerRef: React.RefObject<HTMLCanvasElement>;
  audioLevel: number;
  isSupported: boolean;
}

export function useSpeechRecognition(options: SpeechRecognitionOptions = {}): SpeechRecognitionHook {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [interimTranscript, setInterimTranscript] = useState('');
  const [error, setError] = useState<Error | null>(null);
  const [audioLevel, setAudioLevel] = useState(0);
  
  const microphoneRef = useRef<MicrophoneManager | null>(null);
  const visualizerRef = useRef<HTMLCanvasElement>(null);
  const waveformVisualizerRef = useRef<WaveformVisualizer | null>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);
  const animationFrameRef = useRef<number | null>(null);

  // Check if speech recognition is supported
  const isSupported = typeof window !== 'undefined' && 
    !!(window.SpeechRecognition || window.webkitSpeechRecognition);

  // Initialize microphone
  useEffect(() => {
    if (!isSupported) return;
    
    microphoneRef.current = useMicrophone({
      continuous: options.continuous ?? true,
      interimResults: options.interimResults ?? true,
      lang: options.lang ?? 'en-US',
      visualFeedback: true,
    });

    return () => {
      stopListening();
    };
  }, [isSupported, options.continuous, options.interimResults, options.lang]);

  // Initialize waveform visualizer if visualization is enabled
  useEffect(() => {
    if (!options.visualize || !visualizerRef.current) return;
    
    waveformVisualizerRef.current = new WaveformVisualizer(visualizerRef.current, {
      lineColor: options.visualizerOptions?.lineColor || '#00CCFF',
      lineWidth: options.visualizerOptions?.lineWidth || 2,
      backgroundColor: options.visualizerOptions?.backgroundColor || 'rgba(0, 0, 0, 0.1)',
    });

    return () => {
      if (waveformVisualizerRef.current) {
        waveformVisualizerRef.current.disconnect();
        waveformVisualizerRef.current = null;
      }
    };
  }, [options.visualize, options.visualizerOptions]);

  // Auto-start if option is enabled
  useEffect(() => {
    if (options.autoStart && isSupported) {
      startListening();
    }
    
    return () => {
      stopListening();
    };
  }, [options.autoStart, isSupported]);

  // Update transcript and interim transcript when microphone values change
  useEffect(() => {
    if (!microphoneRef.current) return;
    
    const intervalId = setInterval(() => {
      if (microphoneRef.current) {
        setTranscript(microphoneRef.current.transcript);
        setInterimTranscript(microphoneRef.current.interimTranscript);
        setError(microphoneRef.current.error);
        setIsListening(microphoneRef.current.isRecording);
      }
    }, 100);

    return () => {
      clearInterval(intervalId);
    };
  }, []);

  // Update audio level when microphone is active
  useEffect(() => {
    if (!isListening) {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      return;
    }

    const updateAudioLevel = () => {
      if (waveformVisualizerRef.current) {
        setAudioLevel(waveformVisualizerRef.current.getAudioLevel());
      } else if (microphoneRef.current) {
        setAudioLevel(microphoneRef.current.getAudioLevel());
      }
      
      animationFrameRef.current = requestAnimationFrame(updateAudioLevel);
    };

    updateAudioLevel();

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    };
  }, [isListening]);

  // Start listening function
  const startListening = useCallback(async () => {
    if (!microphoneRef.current || !isSupported) {
      setError(new Error('Speech recognition not supported'));
      return;
    }
    
    try {
      // First get audio stream for visualizer
      mediaStreamRef.current = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      // Then connect visualizer if enabled
      if (options.visualize && waveformVisualizerRef.current && mediaStreamRef.current) {
        waveformVisualizerRef.current.connectToAudioStream(mediaStreamRef.current);
      }
      
      // Start microphone recording
      await microphoneRef.current.startRecording();
      setIsListening(true);
    } catch (err) {
      console.error('Error starting speech recognition', err);
      setError(err instanceof Error ? err : new Error('Unknown error starting recognition'));
    }
  }, [isSupported, options.visualize]);

  // Stop listening function
  const stopListening = useCallback(() => {
    if (microphoneRef.current) {
      microphoneRef.current.stopRecording();
    }
    
    if (waveformVisualizerRef.current) {
      waveformVisualizerRef.current.disconnect();
    }
    
    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => track.stop());
      mediaStreamRef.current = null;
    }
    
    setIsListening(false);
  }, []);

  // Reset transcript function
  const resetTranscript = useCallback(() => {
    if (microphoneRef.current) {
      microphoneRef.current.clearTranscript();
    }
    
    setTranscript('');
    setInterimTranscript('');
  }, []);

  return {
    isListening,
    transcript,
    interimTranscript,
    error,
    startListening,
    stopListening,
    resetTranscript,
    visualizerRef,
    audioLevel,
    isSupported
  };
} 