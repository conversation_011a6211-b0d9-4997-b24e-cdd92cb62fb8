import React, { useState, useEffect } from 'react';
import { Activity, Wifi, Database, Zap, AlertCircle, CheckCircle, Clock, Server, Cpu, Info } from 'lucide-react';
import api from '../services/api';

interface SystemStatus {
  apiConnection: 'online' | 'offline' | 'degraded';
  database: 'online' | 'offline' | 'degraded';
  trading: 'active' | 'paused' | 'error';
  lastUpdate: string;
  uptime: string;
  latency: number;
  errors: number;
  cpuUsage: number;
  memoryUsage: number;
  platform?: string;
  platformVersion?: string;
}

const SystemHealth: React.FC = () => {
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    apiConnection: 'online',
    database: 'online',
    trading: 'active',
    lastUpdate: new Date().toLocaleTimeString(),
    uptime: '0h 0m',
    latency: 45,
    errors: 0,
    cpuUsage: 0,
    memoryUsage: 0,
    platform: '',
    platformVersion: ''
  });

  const [logs, setLogs] = useState([
    { time: '15:45:23', level: 'info', message: 'Trade executed successfully - BTC/USD BUY 0.1' },
    { time: '15:44:15', level: 'info', message: 'Anomaly detection score updated: 0.23' },
    { time: '15:43:08', level: 'warn', message: 'High volatility detected in ETH/USD' },
    { time: '15:42:01', level: 'info', message: 'Configuration updated: Stop Loss = 50 pips' },
    { time: '15:41:33', level: 'info', message: 'WebSocket connection established' }
  ]);

  // Fetch real system health metrics
  const fetchSystemHealth = async () => {
    try {
      const health = await api.getSystemHealth();
      setSystemStatus(prev => ({
        ...prev,
        cpuUsage: health.cpu,
        memoryUsage: health.memory,
        uptime: health.uptime,
        platform: health.platform,
        platformVersion: health.platform_version,
        lastUpdate: new Date().toLocaleTimeString(),
      }));
    } catch (e) {
      // Optionally set degraded status or show error
      setSystemStatus(prev => ({
        ...prev,
        apiConnection: 'degraded',
        lastUpdate: new Date().toLocaleTimeString(),
      }));
    }
  };

  useEffect(() => {
    fetchSystemHealth();
    const interval = setInterval(() => {
      fetchSystemHealth();
      // Simulate random log entries
      if (Math.random() > 0.8) {
        const messages = [
          'Market data feed updated successfully',
          'Position monitoring active',
          'Risk management check completed',
          'Database backup completed',
          'AI model inference completed'
        ];
        const newLog = {
          time: new Date().toLocaleTimeString(),
          level: Math.random() > 0.9 ? 'warn' : 'info',
          message: messages[Math.floor(Math.random() * messages.length)]
        };
        setLogs(prev => [newLog, ...prev.slice(0, 9)]);
      }
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
      case 'active':
        return 'text-neon-green';
      case 'offline':
      case 'error':
        return 'text-neon-red';
      case 'degraded':
      case 'paused':
        return 'text-neon-yellow';
      default:
        return 'text-terminal-muted';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
      case 'active':
        return <CheckCircle className="text-neon-green" size={16} />;
      case 'offline':
      case 'error':
        return <AlertCircle className="text-neon-red" size={16} />;
      case 'degraded':
      case 'paused':
        return <Clock className="text-neon-yellow" size={16} />;
      default:
        return <AlertCircle className="text-terminal-muted" size={16} />;
    }
  };

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'info': return 'text-neon-blue';
      case 'warn': return 'text-neon-yellow';
      case 'error': return 'text-neon-red';
      default: return 'text-terminal-muted';
    }
  };

  const services = [
    {
      name: 'API Connection',
      status: systemStatus.apiConnection,
      icon: Wifi,
      detail: `Latency: ${systemStatus.latency.toFixed(0)}ms`,
      color: 'text-neon-blue'
    },
    {
      name: 'Database',
      status: systemStatus.database,
      icon: Database,
      detail: 'Last backup: 2h ago',
      color: 'text-neon-purple'
    },
    {
      name: 'Trading Engine',
      status: systemStatus.trading,
      icon: Zap,
      detail: `Uptime: ${systemStatus.uptime}`,
      color: 'text-neon-green'
    }
  ];

  return (
    <div className="terminal-card p-6">
      <div className="flex items-center space-x-3 mb-6">
        <div className="p-2 bg-neon-green/20 rounded-lg">
          <Activity className="text-neon-green" size={24} />
        </div>
        <div>
          <h2 className="text-xl font-bold text-terminal-text">System Health</h2>
          <p className="text-sm text-terminal-muted font-mono">Real-time Monitoring</p>
        </div>
      </div>

      {/* Service Status */}
      <div className="space-y-3 mb-6">
        {services.map((service, index) => (
          <div key={index} className="terminal-surface p-4 rounded-lg hover:bg-terminal-card/50 transition-all duration-300">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-lg bg-${service.color.split('-')[1]}/20`}>
                  <service.icon className={service.color} size={20} />
                </div>
                <span className="text-terminal-text font-medium font-mono">{service.name}</span>
              </div>
              <div className="flex items-center space-x-2">
                {getStatusIcon(service.status)}
                <span className={`capitalize font-mono font-medium ${getStatusColor(service.status)}`}>
                  {service.status}
                </span>
              </div>
            </div>
            <div className="text-sm text-terminal-muted font-mono ml-11">
              {service.detail}
            </div>
          </div>
        ))}
      </div>

      {/* System Metrics */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="terminal-surface p-4 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <Cpu className="text-neon-blue" size={16} />
              <span className="text-sm font-mono text-terminal-text">CPU Usage</span>
            </div>
            <span className="text-lg font-bold font-mono text-neon-blue">{systemStatus.cpuUsage.toFixed(0)}%</span>
          </div>
          <div className="w-full bg-terminal-bg rounded-full h-2">
            <div
              className="bg-gradient-to-r from-neon-blue to-neon-purple h-2 rounded-full transition-all duration-1000"
              style={{ width: `${systemStatus.cpuUsage}%` }}
            />
          </div>
        </div>

        <div className="terminal-surface p-4 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <Server className="text-neon-purple" size={16} />
              <span className="text-sm font-mono text-terminal-text">Memory</span>
            </div>
            <span className="text-lg font-bold font-mono text-neon-purple">{systemStatus.memoryUsage.toFixed(0)}%</span>
          </div>
          <div className="w-full bg-terminal-bg rounded-full h-2">
            <div
              className="bg-gradient-to-r from-neon-purple to-neon-blue h-2 rounded-full transition-all duration-1000"
              style={{ width: `${systemStatus.memoryUsage}%` }}
            />
          </div>
        </div>
      </div>

      {/* Platform Info */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="terminal-surface p-4 rounded-lg flex items-center space-x-3">
          <Info className="text-neon-yellow" size={18} />
          <div>
            <div className="text-xs text-terminal-muted font-mono">Platform</div>
            <div className="text-lg font-bold text-terminal-text font-mono">{systemStatus.platform || 'Unknown'}</div>
          </div>
        </div>
        <div className="terminal-surface p-4 rounded-lg flex items-center space-x-3">
          <Info className="text-neon-yellow" size={18} />
          <div>
            <div className="text-xs text-terminal-muted font-mono">Version</div>
            <div className="text-lg font-bold text-terminal-text font-mono">{systemStatus.platformVersion || 'Unknown'}</div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-3 gap-3 mb-6">
        <div className="terminal-surface p-3 rounded-lg text-center">
          <div className="text-2xl font-bold text-neon-green font-mono">{systemStatus.errors}</div>
          <div className="text-xs text-terminal-muted font-mono">Errors</div>
        </div>
        <div className="terminal-surface p-3 rounded-lg text-center">
          <div className="text-2xl font-bold text-neon-blue font-mono">99.9%</div>
          <div className="text-xs text-terminal-muted font-mono">Uptime</div>
        </div>
        <div className="terminal-surface p-3 rounded-lg text-center">
          <div className="text-2xl font-bold text-neon-yellow font-mono">{systemStatus.latency.toFixed(0)}ms</div>
          <div className="text-xs text-terminal-muted font-mono">Latency</div>
        </div>
      </div>

      {/* System Logs */}
      <div className="terminal-surface p-4 rounded-lg">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-terminal-text font-mono">Recent Logs</h3>
          <span className="text-xs text-terminal-muted font-mono">
            Updated: {systemStatus.lastUpdate}
          </span>
        </div>
        
        <div className="space-y-2 max-h-32 overflow-y-auto scrollbar-thin">
          {logs.map((log, index) => (
            <div key={index} className="flex items-start space-x-3 text-sm hover:bg-terminal-card/30 p-2 rounded transition-colors">
              <span className="text-terminal-muted font-mono text-xs w-16 flex-shrink-0 mt-0.5">
                {log.time}
              </span>
              <span className={`uppercase text-xs font-mono font-bold w-12 flex-shrink-0 mt-0.5 ${getLogLevelColor(log.level)}`}>
                {log.level}
              </span>
              <span className="text-terminal-text flex-1 font-mono">{log.message}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Actions */}
      <div className="grid grid-cols-2 gap-3 mt-4">
        <button className="flex items-center justify-center space-x-2 bg-gradient-to-r from-neon-green to-neon-blue hover:from-neon-blue hover:to-neon-green text-white py-2 px-4 rounded-lg transition-all duration-300 text-sm font-mono font-medium">
          <Activity size={16} />
          <span>Refresh</span>
        </button>
        <button className="flex items-center justify-center space-x-2 terminal-surface hover:bg-terminal-card text-terminal-text py-2 px-4 rounded-lg transition-all duration-300 border border-terminal-border text-sm font-mono font-medium">
          <Server size={16} />
          <span>View Logs</span>
        </button>
      </div>
    </div>
  );
};

export default SystemHealth;