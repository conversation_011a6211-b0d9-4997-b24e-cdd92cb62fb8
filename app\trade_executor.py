import MetaTrader5 as mt5
import time
from app.config import config
from app.ai_brain import TradeDecision

class TradeExecutor:
    """
    Handles trade execution, position sizing, and risk management.
    """
    def __init__(self):
        # Assumes MT5 is already initialized
        if not mt5.terminal_info():
            raise ConnectionError("MT5 not connected. Initialize connection before using Executor.")
        self.slippage = 5 # Max allowed deviation in points

    def calculate_position_size(self, symbol: str, sl_pips: float) -> float:
        """
        Calculates position size based on risk percentage and stop loss.
        """
        account_info = mt5.account_info()
        if account_info is None:
            print("Failed to get account info.")
            return 0.01

        balance = account_info.balance
        risk_amount = balance * (config.RISK_PERCENT / 100)
        
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            print(f"Failed to get symbol info for {symbol}")
            return 0.01
            
        point = symbol_info.point
        sl_points = sl_pips * 10 * point if "JPY" not in symbol else sl_pips * point
        
        tick_value = mt5.symbol_info_tick(symbol).ask
        if tick_value == 0:
            print(f"Could not get tick value for {symbol}")
            return 0.01
            
        sl_money = sl_points * tick_value
        
        if sl_money <= 0:
            print("Invalid stop loss value for position sizing.")
            return 0.01
        
        volume = round(risk_amount / sl_money, 2)
        
        min_volume = symbol_info.volume_min
        max_volume = symbol_info.volume_max
        volume_step = symbol_info.volume_step

        # Clamp and step-align the volume
        volume = max(min_volume, volume)
        volume = min(max_volume, volume)
        volume = round(volume / volume_step) * volume_step
        
        return round(volume, 2)

    def execute_trade(self, symbol: str, decision: TradeDecision, latest_price: dict):
        """
        Executes a trade based on the AI's decision.
        """
        if decision.action == "HOLD":
            print("Decision is HOLD. No trade executed.")
            return None

        trade_type = mt5.ORDER_TYPE_BUY if decision.action == "BUY" else mt5.ORDER_TYPE_SELL
        price = mt5.symbol_info_tick(symbol).ask if trade_type == mt5.ORDER_TYPE_BUY else mt5.symbol_info_tick(symbol).bid
        point = mt5.symbol_info(symbol).point

        sl = price - decision.sl_pips * point if trade_type == mt5.ORDER_TYPE_BUY else price + decision.sl_pips * point
        tp = price + (decision.sl_pips * decision.tp_ratio) * point if trade_type == mt5.ORDER_TYPE_BUY else price - (decision.sl_pips * decision.tp_ratio) * point

        volume = self.calculate_position_size(symbol, decision.sl_pips)
        
        if volume <= 0:
            print(f"Invalid volume {volume}, cannot place trade.")
            return None

        comment = f"AI Trade | Confidence: {decision.confidence:.2%}"

        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": symbol,
            "volume": volume,
            "type": trade_type,
            "price": price,
            "sl": sl,
            "tp": tp,
            "deviation": self.slippage,
            "magic": 23400,
            "comment": comment,
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }

        for attempt in range(3): # Retry up to 3 times
            result = mt5.order_send(request)
            if result.retcode == mt5.TRADE_RETCODE_DONE:
                print(f"Trade executed successfully: {result.order}")
                return result
            
            print(f"Order failed (attempt {attempt+1}): {result.comment}")
            time.sleep(1)
            
        print("Failed to execute trade after 3 attempts.")
        return None 