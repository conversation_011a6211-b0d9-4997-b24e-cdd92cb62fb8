import pandas as pd
import numpy as np
from app.data_feeder import DataFeeder
from app.ai_brain import MLBrain
from app.config import config

def create_labels(df: pd.DataFrame, future_candles=10, threshold=0.001) -> pd.Series:
    """
    Creates target labels for training.
    - 1 (BUY): Future price increases by `threshold`.
    - 2 (SELL): Future price decreases by `threshold`.
    - 0 (HOLD): Neither condition is met.
    """
    df['future_price'] = df['close'].shift(-future_candles)
    df['price_change_pct'] = (df['future_price'] - df['close']) / df['close']
    
    df['label'] = 0 # Default to HOLD
    df.loc[df['price_change_pct'] > threshold, 'label'] = 1 # BUY
    df.loc[df['price_change_pct'] < -threshold, 'label'] = 2 # SELL
    
    return df['label'].dropna()

def main():
    """
    Main function to fetch data, create labels, train, and save the model.
    """
    print("Starting model training process...")
    feeder = DataFeeder()
    ml_brain = MLBrain()

    all_features = []
    all_labels = []

    for symbol in config.SYMBOLS:
        print(f"Fetching and processing data for {symbol}...")
        
        # Fetch a larger dataset for training
        historical_data = feeder.fetch_data(symbol, num_candles=5000) 
        
        # Calculate indicators
        data_with_indicators = feeder.calculate_indicators(historical_data)
        
        # Create features and labels
        features = ml_brain.features_from_df(data_with_indicators)
        labels = create_labels(data_with_indicators)
        
        # Align features and labels by index
        aligned_features, aligned_labels = features.align(labels, join='inner', axis=0)
        
        all_features.append(aligned_features)
        all_labels.append(aligned_labels)

    # Combine data from all symbols
    X_train = pd.concat(all_features)
    y_train = pd.concat(all_labels)

    if X_train.empty or y_train.empty:
        print("Could not generate sufficient training data. Exiting.")
        return

    print(f"Training model with {len(X_train)} samples...")
    print("Label distribution:\n", y_train.value_counts())

    # Train the model
    ml_brain.train(X_train, y_train)

    print(f"Training complete. Model saved to {ml_brain.model_path}")

if __name__ == "__main__":
    main() 