import { useState, useEffect, useRef, useCallback } from 'react';
import { AudioRecorder, AudioRecorderOptions, RecordingState } from './audioRecorder';
import api from '../services/api';

interface UseAudioRecorderOptions extends AudioRecorderOptions {
  autoTranscribe?: boolean;
  streamingTranscription?: boolean;
}

interface UseAudioRecorderReturn {
  isRecording: boolean;
  isPaused: boolean;
  audioLevel: number;
  duration: number;
  hasRecording: boolean;
  recordingBlob: Blob | null;
  transcript: string;
  isTranscribing: boolean;
  transcriptionError: Error | null;
  startRecording: () => Promise<boolean>;
  stopRecording: () => Promise<Blob | null>;
  pauseRecording: () => boolean;
  resumeRecording: () => boolean;
  getRecordingUrl: () => string | null;
  transcribeRecording: () => Promise<string>;
  clearRecording: () => void;
}

export function useAudioRecorder(options: UseAudioRecorderOptions = {}): UseAudioRecorderReturn {
  // State variables
  const [recorderState, setRecorderState] = useState<RecordingState>({
    isRecording: false,
    isPaused: false,
    duration: 0,
    audioLevel: 0,
    hasRecording: false
  });
  const [recordingBlob, setRecordingBlob] = useState<Blob | null>(null);
  const [recordingUrl, setRecordingUrl] = useState<string | null>(null);
  const [transcript, setTranscript] = useState('');
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [transcriptionError, setTranscriptionError] = useState<Error | null>(null);
  
  // Refs
  const recorderRef = useRef<AudioRecorder | null>(null);
  const streamingConnectionRef = useRef<{ sendAudio: (data: Blob) => void; close: () => void } | null>(null);
  
  // Initialize recorder on mount
  useEffect(() => {
    const setupRecorder = async () => {
      // Create recorder
      const recorder = new AudioRecorder({
        mimeType: options.mimeType,
        audioBitsPerSecond: options.audioBitsPerSecond,
        pauseAfterInactivity: options.pauseAfterInactivity,
        silenceThreshold: options.silenceThreshold
      });
      
      // Set up recorder
      const success = await recorder.setup();
      
      if (success) {
        recorderRef.current = recorder;
        
        // Listen for state changes
        recorder.onStateChange((state) => {
          setRecorderState(state);
        });
        
        // Set up streaming transcription if enabled
        if (options.streamingTranscription) {
          recorder.onDataAvailable((data) => {
            if (streamingConnectionRef.current) {
              streamingConnectionRef.current.sendAudio(data);
            }
          });
        }
      }
    };
    
    setupRecorder();
    
    // Clean up on unmount
    return () => {
      if (recorderRef.current) {
        recorderRef.current.release();
        recorderRef.current = null;
      }
      
      if (streamingConnectionRef.current) {
        streamingConnectionRef.current.close();
        streamingConnectionRef.current = null;
      }
      
      if (recordingUrl) {
        URL.revokeObjectURL(recordingUrl);
      }
    };
  }, []); // Only run on mount
  
  /**
   * Start recording
   */
  const startRecording = useCallback(async (): Promise<boolean> => {
    try {
      if (!recorderRef.current) {
        const recorder = new AudioRecorder(options);
        const success = await recorder.setup();
        
        if (!success) {
          throw new Error('Failed to set up recorder');
        }
        
        recorderRef.current = recorder;
        
        // Listen for state changes
        recorder.onStateChange((state) => {
          setRecorderState(state);
        });
      }
      
      // Start streaming transcription if enabled
      if (options.streamingTranscription) {
        // Close existing connection if any
        if (streamingConnectionRef.current) {
          streamingConnectionRef.current.close();
        }
        
        streamingConnectionRef.current = api.startStreamingTranscription(
          (text, isFinal) => {
            if (isFinal) {
              setTranscript(prev => prev + ' ' + text);
            }
          },
          (error) => {
            console.error('Streaming transcription error:', error);
            setTranscriptionError(error);
          }
        );
      }
      
      // Clear previous recording if any
      if (recordingUrl) {
        URL.revokeObjectURL(recordingUrl);
        setRecordingUrl(null);
      }
      
      setRecordingBlob(null);
      setTranscript('');
      setTranscriptionError(null);
      
      return recorderRef.current.start();
    } catch (error) {
      console.error('Error starting recording:', error);
      setTranscriptionError(error instanceof Error ? error : new Error('Failed to start recording'));
      return false;
    }
  }, [options]);
  
  /**
   * Stop recording
   */
  const stopRecording = useCallback(async (): Promise<Blob | null> => {
    if (!recorderRef.current) {
      return null;
    }
    
    try {
      // Stop recording
      const blob = recorderRef.current.stop();
      setRecordingBlob(blob);
      
      // Create URL for the blob
      if (blob) {
        const url = URL.createObjectURL(blob);
        setRecordingUrl(url);
      }
      
      // Close streaming transcription if enabled
      if (streamingConnectionRef.current) {
        streamingConnectionRef.current.close();
        streamingConnectionRef.current = null;
      }
      
      // Auto-transcribe if enabled
      if (options.autoTranscribe && blob) {
        await transcribeBlob(blob);
      }
      
      return blob;
    } catch (error) {
      console.error('Error stopping recording:', error);
      return null;
    }
  }, [options.autoTranscribe]);
  
  /**
   * Pause recording
   */
  const pauseRecording = useCallback((): boolean => {
    if (!recorderRef.current) {
      return false;
    }
    
    return recorderRef.current.pause();
  }, []);
  
  /**
   * Resume recording
   */
  const resumeRecording = useCallback((): boolean => {
    if (!recorderRef.current) {
      return false;
    }
    
    return recorderRef.current.resume();
  }, []);
  
  /**
   * Get recording URL
   */
  const getRecordingUrl = useCallback((): string | null => {
    return recordingUrl;
  }, [recordingUrl]);
  
  /**
   * Transcribe recording using our API
   */
  const transcribeRecording = useCallback(async (): Promise<string> => {
    if (!recordingBlob) {
      throw new Error('No recording to transcribe');
    }
    
    return transcribeBlob(recordingBlob);
  }, [recordingBlob]);
  
  /**
   * Helper function to transcribe a blob
   */
  const transcribeBlob = async (blob: Blob): Promise<string> => {
    try {
      setIsTranscribing(true);
      setTranscriptionError(null);
      
      const result = await api.transcribeAudio(blob);
      setTranscript(result.text);
      
      return result.text;
    } catch (error) {
      console.error('Error transcribing audio:', error);
      setTranscriptionError(error instanceof Error ? error : new Error('Failed to transcribe audio'));
      throw error;
    } finally {
      setIsTranscribing(false);
    }
  };
  
  /**
   * Clear current recording
   */
  const clearRecording = useCallback(() => {
    if (recorderRef.current) {
      recorderRef.current.clearRecording();
    }
    
    if (recordingUrl) {
      URL.revokeObjectURL(recordingUrl);
    }
    
    setRecordingBlob(null);
    setRecordingUrl(null);
    setTranscript('');
  }, [recordingUrl]);
  
  return {
    // State
    isRecording: recorderState.isRecording,
    isPaused: recorderState.isPaused,
    audioLevel: recorderState.audioLevel,
    duration: recorderState.duration,
    hasRecording: recorderState.hasRecording,
    recordingBlob,
    transcript,
    isTranscribing,
    transcriptionError,
    
    // Methods
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    getRecordingUrl,
    transcribeRecording,
    clearRecording
  };
} 