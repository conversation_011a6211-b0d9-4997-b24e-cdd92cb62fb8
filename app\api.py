from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Response, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
import psutil
import platform
import time
import MetaTrader5 as mt5
from app.backtester import Backtester
from app.config import config, Config
from app.main import initialize_components
from datetime import datetime, date
from app.postgresql_memory import PostgresMemory
from app.explainability import ExplainabilityEngine
from app.news_strategy import run_sentiment_strategy, fetch_news, score_headlines, make_decision

app = FastAPI(title="NexGen Forex Trading Bot API")

# Add CORS middleware to allow frontend requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

start_time = time.time()

# --- Backtest state storage ---
backtest_memory = PostgresMemory()
current_backtest: Optional[dict] = None  # Placeholder for future async execution

# Request/Response models
class ChatRequest(BaseModel):
    message: str
    use_multi_agent: bool = False

class ChatResponse(BaseModel):
    response: str
    voice_enabled: bool

class MemoryQueryRequest(BaseModel):
    query: Optional[str] = None
    type_: Optional[str] = None
    limit: int = 20

class MemoryQueryResponse(BaseModel):
    results: List[Dict[str, Any]]

class BacktestRequest(BaseModel):
    symbol: str
    start_date: str
    end_date: str
    initial_balance: Optional[float] = 10000.0

class NewsSentimentResponse(BaseModel):
    symbol: str
    decision: str
    score: float
    top_article: dict | None

@app.get("/")
async def root():
    return {"status": "NexGen Forex Trading Bot API is running"}

# Note: The 'chat', 'voice', 'memory', and 'fiass' related endpoints
# are left as placeholders assuming they might be powered by components
# not part of the core trading loop. If they need access to the main
# components, they should be updated to use `request.app.state.components`.

@app.post("/chat", response_model=ChatResponse)
async def process_chat(request: Request, chat_request: ChatRequest):
    # This is a placeholder implementation.
    # A real implementation would use a chat agent from request.app.state.components
    return ChatResponse(response="Chat feature not fully integrated yet.", voice_enabled=False)

@app.post("/run-reasoning")
async def run_reasoning(request: Request):
    # fiass = request.app.state.components['fiass']
    # fiass.run_all()
    return {"status": "Reasoning cycle not fully integrated yet."}

@app.post("/backtest")
async def run_backtest(request: BacktestRequest):
    try:
        backtester = Backtester(
            symbol=request.symbol,
            start_date=request.start_date,
            end_date=request.end_date,
            initial_balance=request.initial_balance or 10000.0
        )
        result = backtester.run()
        if result is None:
            raise HTTPException(status_code=400, detail="No data for backtest.")
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error running backtest: {str(e)}")

@app.get("/backtest_results")
async def get_backtest_results(limit: int = 20):
    """Returns recent completed backtests and the status of any backtest currently running.
    The response schema matches what the frontend BacktestPanel expects.
    """
    try:
        # Fetch recent backtest logs from PostgresMemory
        logs = backtest_memory.retrieve_memory(type_="BACKTEST", limit=limit)
        completed_backtests = []
        for log in logs:
            meta = log.get("meta") or {}
            # Flatten the structure to what the UI expects
            completed_backtests.append({
                "symbol": meta.get("symbol") or "N/A",
                "strategy": meta.get("strategy") or "Hybrid",
                "timestamp": meta.get("timestamp") or log["timestamp"].isoformat() if isinstance(log.get("timestamp"), str) else str(log.get("timestamp")),
                "performance": meta.get("performance") or {},
                "equity_curve": meta.get("equity_curve") or [],
                "trades": meta.get("trades") or [],
                "ai_assessment": meta.get("ai_assessment") or "Pending Analysis",
            })

        status = "backtesting" if current_backtest else "idle"
        return {
            "status": status,
            "current_backtest": current_backtest,
            "completed_backtests": completed_backtests,
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching backtest results: {str(e)}")

@app.get("/account_stats")
async def get_account_stats():
    # MT5 connection is now managed globally by run.py
    if not mt5.terminal_info():
        raise HTTPException(status_code=500, detail="MT5 not connected.")
    try:
        info = mt5.account_info()
        return info._asdict() if info else {}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching account stats: {str(e)}")

@app.get("/open_trades")
async def get_open_trades():
    # MT5 connection is now managed globally by run.py
    if not mt5.terminal_info():
        raise HTTPException(status_code=500, detail="MT5 not connected.")
    try:
        trades = mt5.positions_get()
        return [t._asdict() for t in trades] if trades else []
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching open trades: {str(e)}")

@app.get("/system_health")
async def get_system_health():
    try:
        cpu = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory().percent
        uptime_seconds = int(time.time() - start_time)
        uptime = f"{uptime_seconds // 3600}h {(uptime_seconds % 3600) // 60}m"
        return {
            "cpu": cpu,
            "memory": memory,
            "uptime": uptime,
            "platform": platform.system(),
            "platform_version": platform.version()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching system health: {str(e)}")

# --- Configuration Endpoints ---

@app.get("/config", response_model=Config)
async def get_config():
    """Returns the current bot configuration."""
    return config

@app.post("/config", response_model=Config)
async def update_config(new_config: Config, request: Request):
    """Updates the bot's configuration."""
    global config
    try:
        # Update the global config object
        for key, value in new_config.dict().items():
            if hasattr(config, key):
                setattr(config, key, value)
        
        # Note: This updates the config in memory. 
        # For persistence across restarts, the config would need to be saved to a file.
        # This simple in-memory update is sufficient for live adjustments.
        
        # Re-initialize components that depend on the config
        # This is a simplified example. A more robust implementation might
        # re-initialize specific components based on what changed.
        request.app.state.shared_state["components"] = initialize_components()

        return config
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating configuration: {str(e)}")

# --- Bot Control Endpoints ---

@app.get("/bot/status")
async def get_bot_status(request: Request):
    is_enabled = request.app.state.shared_state.get("trading_enabled", False)
    return {"status": "running" if is_enabled else "stopped"}

@app.post("/bot/start")
async def start_bot(request: Request):
    request.app.state.shared_state["trading_enabled"] = True
    return {"message": "Bot started."}

@app.post("/bot/stop")
async def stop_bot(request: Request):
    request.app.state.shared_state["trading_enabled"] = False
    return {"message": "Bot stopped. It will finish the current cycle if one is running."}

# --- Data Endpoints ---

class TradeLogResponse(BaseModel):
    logs: List[Dict[str, Any]]

@app.get("/trade_logs", response_model=TradeLogResponse)
async def get_trade_logs(request: Request, limit: int = 100):
    try:
        db = request.app.state.shared_state["components"]["db"]
        # Assuming the db object has a method 'get_trade_logs'
        logs = db.get_trade_logs(limit=limit) 
        return {"logs": logs}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching trade logs: {str(e)}")

class PriceDataRequest(BaseModel):
    symbol: str
    timeframe: str
    limit: int

@app.post("/price_data")
async def get_price_data(request: Request, data_request: PriceDataRequest):
    try:
        feeder = request.app.state.shared_state["components"]["feeder"]
        # Assuming the feeder has a method to get historical data
        df = feeder.fetch_data(
            data_request.symbol, 
            timeframe=getattr(mt5, f"TIMEFRAME_{data_request.timeframe.upper()}"),
            num_candles=data_request.limit
        )
        return df.reset_index().to_dict(orient='records')
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching price data: {str(e)}")

# --- Explainability Endpoints ---
@app.get("/explanations/{trade_id}")
async def get_trade_explanation(trade_id: int, request: Request):
    """Fetches (or lazily generates) the explanation for a specific trade."""
    try:
        components = request.app.state.shared_state["components"]
        db = components["db"]
        explanation = db.get_explanation_for_trade(trade_id)
        if explanation:
            return explanation
        # Not found – generate now
        ml_brain = components["ml_brain"]
        explain_engine: ExplainabilityEngine = components["explain_engine"]
        # Fetch trade row and indicator data
        trade_row = db.conn.execute("SELECT * FROM trades WHERE id = ?", (trade_id,)).fetchone()
        if not trade_row:
            raise HTTPException(status_code=404, detail="Trade not found.")
        symbol = trade_row["symbol"]
        latest_indicators_row = db.conn.execute(
            "SELECT * FROM indicator_values WHERE trade_id = ? ORDER BY id DESC LIMIT 1", (trade_id,)
        ).fetchone()
        if not latest_indicators_row:
            raise HTTPException(status_code=404, detail="Indicators not found for this trade.")
        features_dict = dict(latest_indicators_row)
        features_df = ml_brain.features_from_df(
            # create DataFrame with single row
            ml_brain._features_from_dict(features_dict)
        )
        explain_engine.explain_trade(
            trade_id=trade_id,
            features=features_df.iloc[-1].to_dict(),
            X_row=features_df.values,
            model_confidence=trade_row["confidence"] or 0.0,
            action=trade_row["action"],
            symbol=symbol,
        )
        # Retrieve new explanation
        explanation = db.get_explanation_for_trade(trade_id)
        if explanation:
            return explanation
        else:
            raise HTTPException(status_code=500, detail="Failed to generate explanation.")
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching explanation: {str(e)}")

# --- News Endpoint ---
@app.get("/news")
async def get_news_events(request: Request):
    """Fetches upcoming economic news events."""
    try:
        news_filter = request.app.state.shared_state["components"]["news_filter"]
        events = news_filter.get_todays_events()
        
        # Also check which events are currently blocking trades
        now_utc = datetime.utcnow()
        for event in events:
            event['isBlocked'] = False
            if event['impact'] in ['high', 'medium']:
                event_time_utc = datetime.strptime(f"{date.today().isoformat()} {event['time']}", '%Y-%m-%d %H:%M')
                time_before = event_time_utc - news_filter.buffer
                time_after = event_time_utc + news_filter.buffer
                if time_before <= now_utc <= time_after:
                    event['isBlocked'] = True
        
        return {"events": events}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching news events: {str(e)}")

# --- Anomaly Detection Endpoint ---
@app.get("/anomaly_status")
async def get_anomaly_status(request: Request):
    """Fetches the latest market anomaly analysis."""
    try:
        anomaly_detector = request.app.state.shared_state["components"]["anomaly_detector"]
        analysis = anomaly_detector.get_analysis()
        return analysis
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching anomaly status: {str(e)}")

@app.get("/news_sentiment", response_model=NewsSentimentResponse)
async def get_news_sentiment(symbol: str = "EURUSD"):
    """Returns aggregated headline sentiment for the given forex pair."""
    try:
        decision = run_sentiment_strategy(symbol)
        # run_sentiment_strategy logs and returns decision; we need avg score & top article
        arts = score_headlines(fetch_news(symbol))
        avg = sum(a.sentiment for a in arts) / len(arts) if arts else 0.0
        return {
            "symbol": symbol,
            "decision": decision,
            "score": round(avg, 3),
            "top_article": arts[0].__dict__ if arts else None,
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching sentiment: {str(e)}") 