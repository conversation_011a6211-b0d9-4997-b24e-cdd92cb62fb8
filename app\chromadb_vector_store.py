import numpy as np
from app.config import config
from typing import Optional

try:
    import chromadb
    from chromadb.config import Settings
    from chromadb.utils import embedding_functions
except ImportError:
    chromadb = None

class VectorStore:
    def __init__(self, collection_name='memory_vectors'):
        self.type = config.VECTOR_STORE_TYPE
        self.collection_name = collection_name
        if self.type == 'chromadb':
            if not chromadb:
                raise ImportError('chromadb is not installed. Please install it to use this vector store.')
            self.client = chromadb.Client(Settings(persist_directory=config.VECTOR_STORE_PATH))
            self.collection = self.client.get_or_create_collection(collection_name)
        elif self.type == 'faiss':
            # Placeholder for FAISS integration
            raise NotImplementedError('FAISS vector store not implemented yet.')
        else:
            raise ValueError(f'Unknown vector store type: {self.type}')

    def add_embedding(self, id_: str, embedding: np.ndarray, metadata: Optional[dict] = None):
        if self.type == 'chromadb':
            self.collection.add(
                ids=[id_],
                embeddings=[embedding.tolist()],
                metadatas=[metadata or {}]
            )
        else:
            raise NotImplementedError('Only ChromaDB is implemented.')

    def query(self, embedding: np.ndarray, n_results: int = 5):
        if self.type == 'chromadb':
            results = self.collection.query(
                query_embeddings=[embedding.tolist()],
                n_results=n_results
            )
            return results
        else:
            raise NotImplementedError('Only ChromaDB is implemented.')

# Usage example:
# store = VectorStore()
# store.add_embedding('thought1', np.random.rand(1536), {'type': 'LLM', 'symbol': 'EURUSD'})
# results = store.query(np.random.rand(1536)) 