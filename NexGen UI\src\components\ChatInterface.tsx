import React, { useState, useEffect, useRef } from 'react';
import { MessageCircle, Send, Bot, User, Terminal, Zap, Mic, MicOff, Volume2, VolumeX, Maximize2, Minimize2, X } from 'lucide-react';
import api from '../services/api'; // Import our API client
import { useSpeechRecognition } from '../utils/useSpeechRecognition';

interface ChatMessage {
  id: string;
  type: 'user' | 'bot';
  message: string;
  timestamp: string;
}

const ChatInterface: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'bot',
      message: 'NexGen TraderPro AI Assistant ready. Ask me about trades, market analysis, or bot status.',
      timestamp: new Date().toLocaleTimeString()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isSpeechEnabled, setIsSpeechEnabled] = useState(false);
  const [isVoiceMode, setIsVoiceMode] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [useMultiAgent, setUseMultiAgent] = useState(false);

  // Speech recognition hook from our custom utility
  const {
    isListening,
    transcript,
    interimTranscript,
    error: speechError,
    startListening,
    stopListening,
    resetTranscript,
    visualizerRef,
    audioLevel,
    isSupported: isSpeechSupported
  } = useSpeechRecognition({
    continuous: true,
    interimResults: true,
    visualize: true,
    visualizerOptions: {
      lineColor: '#00CCFF', 
      backgroundColor: 'rgba(0, 0, 0, 0.2)',
      lineWidth: 3
    }
  });

  // Update input message when transcript changes
  useEffect(() => {
    if (isListening && !isVoiceMode) {
      setInputMessage(transcript);
    }
  }, [transcript, isListening, isVoiceMode]);

  // In voice mode, automatically send message when we get a final transcript
  useEffect(() => {
    if (isVoiceMode && transcript && !isTyping && !isSpeaking) {
      // Add a small delay to ensure we get the complete sentence
      const timer = setTimeout(() => {
        if (transcript.trim()) {
          handleSendMessage(transcript);
          resetTranscript();
        }
      }, 1500);
      
      return () => clearTimeout(timer);
    }
  }, [transcript, isVoiceMode, isTyping, isSpeaking]);

  // Auto restart listening in voice mode after speaking finishes
  useEffect(() => {
    if (isVoiceMode && !isListening && !isSpeaking) {
      const timer = setTimeout(() => {
        startListening();
      }, 1000);
      
      return () => clearTimeout(timer);
    }
  }, [isVoiceMode, isListening, isSpeaking]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async (message?: string) => {
    const messageToSend = message || inputMessage;
    if (!messageToSend.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      message: messageToSend,
      timestamp: new Date().toLocaleTimeString()
    };

    setMessages(prev => [...prev, userMessage]);
    if (!message) setInputMessage('');
    setIsTyping(true);
    setIsLoading(true);

    try {
      // Call our API instead of using simulated responses
      const response = await api.chat({
        message: messageToSend,
        use_multi_agent: useMultiAgent
      });

      const botMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        message: response.response,
        timestamp: new Date().toLocaleTimeString()
      };

      setMessages(prev => [...prev, botMessage]);

      // Text-to-speech for bot responses
      if ((isSpeechEnabled || isVoiceMode) && 'speechSynthesis' in window) {
        // Stop listening while the bot is speaking
        if (isListening) {
          stopListening();
        }
        
        setIsSpeaking(true);
        
        // Option 1: Use browser's built-in TTS
        const utterance = new SpeechSynthesisUtterance(botMessage.message);
        utterance.rate = 0.9;
        utterance.pitch = 1;
        utterance.onend = () => {
          setIsSpeaking(false);
          // Auto restart listening in voice mode
          if (isVoiceMode) {
            setTimeout(() => startListening(), 500);
          }
        };
        speechSynthesis.speak(utterance);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        message: 'Sorry, I encountered an error processing your request. Please try again later.',
        timestamp: new Date().toLocaleTimeString()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const toggleVoiceMode = () => {
    if (isVoiceMode) {
      // Exit voice mode
      setIsVoiceMode(false);
      stopListening();
      if ('speechSynthesis' in window) {
        speechSynthesis.cancel();
      }
      setIsSpeaking(false);
    } else {
      // Enter voice mode
      setIsVoiceMode(true);
      setIsExpanded(false); // Exit expanded mode if active
      startListening();
    }
  };

  // Add function to fetch memory summaries
  const fetchMemorySummary = async () => {
    try {
      const result = await api.summarizeMemory();
      handleSendMessage(`Here's the weekly summary:\n\n${result.summary}`);
    } catch (error) {
      console.error('Error fetching memory summary:', error);
    }
  };

  // Add function to trigger reasoning cycle
  const triggerReasoning = async () => {
    try {
      setIsLoading(true);
      await api.runReasoning();
      handleSendMessage('Reasoning cycle completed. What would you like to know about the results?');
    } catch (error) {
      console.error('Error triggering reasoning cycle:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Update quick commands to use our new functions
  const quickCommands = [
    "What's the next signal?",
    "Summarize past 5 trades",
    "Current market analysis",
    "Bot status check",
    "Risk assessment",
    "Run reasoning cycle"
  ];

  // Update the quick command handler
  const handleQuickCommand = (command: string) => {
    if (command === "Run reasoning cycle") {
      triggerReasoning();
    } else if (command === "Summarize past 5 trades") {
      fetchMemorySummary();
    } else {
      setInputMessage(command);
    }
  };

  // Voice Mode Interface (Full Screen)
  if (isVoiceMode) {
    return (
      <div className="fixed inset-0 bg-gradient-to-br from-terminal-bg via-terminal-surface to-terminal-card z-50 flex flex-col items-center justify-center">
        {/* Header */}
        <div className="absolute top-8 left-8 right-8 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-neon-purple/20 rounded-lg">
              <MessageCircle className="text-neon-purple" size={24} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-terminal-text">Voice Mode</h2>
              <p className="text-sm text-terminal-muted font-mono">Trading AI Assistant</p>
            </div>
          </div>
          
          <button
            onClick={toggleVoiceMode}
            className="p-3 rounded-full bg-terminal-surface/80 backdrop-blur-sm text-terminal-muted hover:text-terminal-text border border-terminal-border transition-all duration-200"
          >
            <X size={24} />
          </button>
        </div>

        {/* Central Orb */}
        <div className="flex flex-col items-center space-y-8">
          {/* Waveform Visualizer Canvas */}
          <div className="relative">
            {/* Background Orb */}
            <div className={`w-48 h-48 rounded-full flex items-center justify-center transition-all duration-500 ${
              isListening 
                ? 'bg-gradient-to-br from-neon-blue/30 via-neon-purple/30 to-neon-green/30 animate-pulse shadow-glow-blue' 
                : isSpeaking
                  ? 'bg-gradient-to-br from-neon-green/30 via-neon-blue/30 to-neon-purple/30 animate-pulse shadow-glow-green'
                  : 'bg-gradient-to-br from-terminal-surface to-terminal-card border-2 border-terminal-border'
            }`}>
              {/* Canvas for audio visualization */}
              <canvas 
                ref={visualizerRef} 
                className="absolute inset-0 w-full h-full rounded-full"
                style={{ opacity: isListening || isSpeaking ? 1 : 0.3 }}
              />
              
              {/* Center Icon */}
              <div className="absolute inset-0 flex items-center justify-center z-10">
                {isListening ? (
                  <Mic className="text-white" size={48} />
                ) : isSpeaking ? (
                  <Volume2 className="text-white" size={48} />
                ) : (
                  <Bot className="text-terminal-muted" size={48} />
                )}
              </div>
            </div>
          </div>

          {/* Status Text and Transcript */}
          <div className="text-center space-y-2 max-w-md">
            <div className="text-2xl font-bold text-terminal-text font-mono">
              {isListening ? 'Listening...' : isSpeaking ? 'Speaking...' : isTyping ? 'Thinking...' : 'Ready to chat'}
            </div>
            <div className="text-terminal-muted font-mono">
              {isListening ? 'Speak now, I\'m listening' : isSpeaking ? 'AI is responding' : 'Tap the orb or say something'}
            </div>
            
            {/* Show interim transcript while listening */}
            {isListening && interimTranscript && (
              <div className="mt-4 p-3 bg-terminal-bg/60 rounded-lg border border-terminal-border text-terminal-text font-mono">
                {interimTranscript}
              </div>
            )}
          </div>
        </div>

        {/* Controls */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex items-center space-x-6">
          <button
            onClick={isListening ? stopListening : startListening}
            disabled={isSpeaking}
            className={`p-4 rounded-full transition-all duration-200 ${
              isListening 
                ? 'bg-neon-red/20 text-neon-red border-2 border-neon-red/50' 
                : 'bg-terminal-surface/80 backdrop-blur-sm text-terminal-muted hover:text-terminal-text border-2 border-terminal-border'
            } disabled:opacity-50`}
          >
            {isListening ? <MicOff size={24} /> : <Mic size={24} />}
          </button>
          
          <button
            onClick={() => {
              if ('speechSynthesis' in window) {
                speechSynthesis.cancel();
                setIsSpeaking(false);
              }
            }}
            disabled={!isSpeaking}
            className="p-4 rounded-full bg-terminal-surface/80 backdrop-blur-sm text-terminal-muted hover:text-terminal-text border-2 border-terminal-border transition-all duration-200 disabled:opacity-50"
          >
            <VolumeX size={24} />
          </button>
        </div>

        {/* Recent Message */}
        {messages.length > 1 && (
          <div className="absolute bottom-24 left-8 right-8 max-w-2xl mx-auto">
            <div className="bg-terminal-surface/80 backdrop-blur-sm rounded-xl p-4 border border-terminal-border">
              <div className="text-sm text-terminal-muted font-mono mb-1">
                {messages[messages.length - 1].type === 'user' ? 'You said:' : 'AI responded:'}
              </div>
              <div className="text-terminal-text font-mono">
                {messages[messages.length - 1].message}
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Regular Chat Interface
  return (
    <div className={`terminal-card p-6 flex flex-col max-h-[80vh] w-full transition-all duration-300 ${isExpanded ? 'fixed inset-4 z-50 max-w-none max-h-none' : ''}`}>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-neon-purple/20 rounded-lg">
            <MessageCircle className="text-neon-purple" size={24} />
          </div>
          <div>
            <h2 className="text-xl font-bold text-terminal-text">AI Assistant</h2>
            <p className="text-sm text-terminal-muted font-mono">Trading Co-Pilot Chat</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={toggleVoiceMode}
            className="p-2 rounded-lg bg-gradient-to-r from-neon-purple to-neon-blue hover:from-neon-blue hover:to-neon-purple text-white transition-all duration-200 shadow-glow-purple"
            title="Voice Mode"
          >
            <Mic size={16} />
          </button>
          
          <button
            onClick={() => setIsSpeechEnabled(!isSpeechEnabled)}
            className={`p-2 rounded-lg transition-all duration-200 ${
              isSpeechEnabled 
                ? 'bg-neon-green/20 text-neon-green border border-neon-green/30' 
                : 'bg-terminal-surface text-terminal-muted border border-terminal-border hover:text-terminal-text'
            }`}
            title="Toggle Text-to-Speech"
          >
            {isSpeechEnabled ? <Volume2 size={16} /> : <VolumeX size={16} />}
          </button>
          
          {/* Multi-Agent Toggle */}
          <label className="flex items-center space-x-1 cursor-pointer select-none text-xs font-mono">
            <input
              type="checkbox"
              checked={useMultiAgent}
              onChange={e => setUseMultiAgent(e.target.checked)}
              className="accent-neon-yellow"
            />
            <Zap className={useMultiAgent ? "text-neon-yellow" : "text-terminal-muted"} size={16} />
            <span>Multi-Agent</span>
          </label>
          
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-2 rounded-lg bg-terminal-surface text-terminal-muted border border-terminal-border hover:text-terminal-text transition-colors"
            title={isExpanded ? "Minimize" : "Expand"}
          >
            {isExpanded ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
          </button>
        </div>
      </div>

      {/* Chat Messages */}
      <div className="flex-1 min-h-0 overflow-y-auto mb-4">
        <div className="space-y-4">
          {messages.map((msg) => (
            <div key={msg.id} className={`flex ${msg.type === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className={`flex items-start space-x-3 max-w-[85%] ${msg.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                <div className={`p-2 rounded-lg flex-shrink-0 ${
                  msg.type === 'user' 
                    ? 'bg-neon-blue/20 border border-neon-blue/30' 
                    : 'bg-neon-green/20 border border-neon-green/30'
                }`}>
                  {msg.type === 'user' ? (
                    <User className="text-neon-blue" size={16} />
                  ) : (
                    <Bot className="text-neon-green" size={16} />
                  )}
                </div>
                <div className={`rounded-xl p-4 ${
                  msg.type === 'user'
                    ? 'bg-neon-blue/10 border border-neon-blue/20'
                    : 'bg-terminal-bg border border-terminal-border'
                }`}>
                  <p className="text-terminal-text font-mono text-sm leading-relaxed break-words">{msg.message}</p>
                  <span className="text-xs text-terminal-muted font-mono mt-2 block">{msg.timestamp}</span>
                </div>
              </div>
            </div>
          ))}
          
          {isTyping && (
            <div className="flex justify-start">
              <div className="flex items-start space-x-3">
                <div className="p-2 rounded-lg bg-neon-green/20 border border-neon-green/30">
                  <Bot className="text-neon-green" size={16} />
                </div>
                <div className="bg-terminal-bg border border-terminal-border rounded-xl p-4">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-neon-green rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-neon-green rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-neon-green rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
        <div ref={messagesEndRef} />
      </div>

      {/* Quick Commands */}
      <div className="mb-4">
        <div className="flex flex-wrap gap-2">
          {quickCommands.map((command, index) => (
            <button
              key={index}
              onClick={() => handleQuickCommand(command)}
              className="text-xs bg-terminal-surface hover:bg-terminal-card text-terminal-muted hover:text-terminal-text px-3 py-2 rounded-lg border border-terminal-border transition-all duration-200 font-mono hover:border-neon-purple/30"
            >
              {command}
            </button>
          ))}
        </div>
      </div>

      {/* Input Area with Visualizer */}
      <div className="flex items-center space-x-3">
        <div className="flex-1 relative">
          <Terminal className="absolute left-3 top-1/2 transform -translate-y-1/2 text-terminal-muted" size={16} />
          <input
            ref={inputRef}
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask about trades, analysis, or bot status..."
            className="w-full pl-10 pr-16 py-3 bg-terminal-bg border border-terminal-border rounded-lg text-terminal-text font-mono focus:outline-none focus:ring-2 focus:ring-neon-purple focus:border-transparent transition-all duration-200 resize-none"
            disabled={isListening}
          />
          
          {/* Mini audio visualizer when listening */}
          {isListening && (
            <div className="absolute left-10 right-10 bottom-0 h-1 overflow-hidden">
              <canvas 
                ref={visualizerRef}
                className="w-full h-8 transform translate-y-1"
              />
            </div>
          )}
          
          {/* Voice Input Button */}
          <button
            onClick={isListening ? stopListening : startListening}
            disabled={isTyping || !isSpeechSupported}
            className={`absolute right-12 top-1/2 transform -translate-y-1/2 p-1.5 rounded-lg transition-all duration-200 ${
              isListening 
                ? 'bg-neon-red/20 text-neon-red animate-pulse' 
                : 'bg-terminal-surface text-terminal-muted hover:text-terminal-text hover:bg-terminal-card'
            } disabled:opacity-50 disabled:cursor-not-allowed`}
            title={isSpeechSupported ? "Voice Input" : "Speech Recognition Not Supported"}
          >
            {isListening ? <MicOff size={14} /> : <Mic size={14} />}
          </button>
        
        <button
          onClick={() => handleSendMessage()}
            disabled={!inputMessage.trim() || isTyping}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1.5 rounded-lg bg-terminal-surface text-terminal-muted hover:text-neon-blue hover:bg-terminal-card transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
            <Send size={14} />
        </button>
        </div>
      </div>
      
      {/* Speech Recognition Error Display */}
      {speechError && (
        <div className="mt-2 text-xs text-neon-red font-mono">
          {speechError.message}
        </div>
      )}
    </div>
  );
};

export default ChatInterface;