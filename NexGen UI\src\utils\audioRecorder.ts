// Audio recorder utility
// Handles audio recording, saving to blob, and provides pause/resume functionality

export interface AudioRecorderOptions {
  mimeType?: string;
  audioBitsPerSecond?: number;
  pauseAfterInactivity?: number;  // auto-pause after ms of silence 
  silenceThreshold?: number;      // 0-1 value for silence detection
}

export interface RecordingState {
  isRecording: boolean;
  isPaused: boolean;
  duration: number;
  audioLevel: number;
  hasRecording: boolean;
}

export class AudioRecorder {
  private mediaRecorder: MediaRecorder | null = null;
  private mediaStream: MediaStream | null = null;
  private audioContext: AudioContext | null = null;
  private analyser: AnalyserNode | null = null;
  private dataArray: Uint8Array | null = null;
  private recordedChunks: Blob[] = [];
  private recordingStartTime: number = 0;
  private recordingDuration: number = 0;
  private pauseStartTime: number = 0;
  private inactivityTimer: number | null = null;
  private options: Required<AudioRecorderOptions>;
  private onDataAvailableCallback: ((data: Blob) => void) | null = null;
  private onStateChangeCallback: ((state: RecordingState) => void) | null = null;
  
  constructor(options: AudioRecorderOptions = {}) {
    this.options = {
      mimeType: options.mimeType || 'audio/webm',
      audioBitsPerSecond: options.audioBitsPerSecond || 128000,
      pauseAfterInactivity: options.pauseAfterInactivity || 0, // 0 means disabled
      silenceThreshold: options.silenceThreshold || 0.05, // default silence threshold
    };
  }
  
  /**
   * Request microphone permission and set up recording
   */
  public async setup(): Promise<boolean> {
    try {
      // Request microphone access
      this.mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      // Set up audio context for level analysis
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = 256;
      
      const source = this.audioContext.createMediaStreamSource(this.mediaStream);
      source.connect(this.analyser);
      
      // Set up data array for analyzer
      this.dataArray = new Uint8Array(this.analyser.frequencyBinCount);
      
      // Find supported mime type
      const mimeTypes = [
        this.options.mimeType,
        'audio/webm',
        'audio/mp4',
        'audio/ogg',
        'audio/wav'
      ];
      
      let selectedMimeType = mimeTypes.find(type => MediaRecorder.isTypeSupported(type));
      
      if (!selectedMimeType) {
        throw new Error('No supported mime type found for audio recording');
      }
      
      // Create media recorder
      this.mediaRecorder = new MediaRecorder(this.mediaStream, {
        mimeType: selectedMimeType,
        audioBitsPerSecond: this.options.audioBitsPerSecond
      });
      
      // Set up event handlers
      this.mediaRecorder.ondataavailable = this.handleDataAvailable.bind(this);
      
      return true;
    } catch (error) {
      console.error('Error setting up audio recorder:', error);
      return false;
    }
  }
  
  /**
   * Start recording
   */
  public start(): boolean {
    if (!this.mediaRecorder || this.mediaRecorder.state === 'recording') {
      return false;
    }
    
    try {
      this.recordedChunks = [];
      this.recordingStartTime = Date.now();
      this.mediaRecorder.start(100); // Collect data every 100ms
      
      // Notify about state change
      this.notifyStateChange();
      
      // Set up inactivity detection if enabled
      this.setupInactivityDetection();
      
      return true;
    } catch (error) {
      console.error('Error starting recording:', error);
      return false;
    }
  }
  
  /**
   * Stop recording
   */
  public stop(): Blob | null {
    if (!this.mediaRecorder || this.mediaRecorder.state === 'inactive') {
      return this.getRecording();
    }
    
    try {
      this.mediaRecorder.stop();
      this.recordingDuration += Date.now() - this.recordingStartTime;
      this.clearInactivityDetection();
      
      // Notify about state change
      this.notifyStateChange();
      
      return this.getRecording();
    } catch (error) {
      console.error('Error stopping recording:', error);
      return null;
    }
  }
  
  /**
   * Pause recording
   */
  public pause(): boolean {
    if (!this.mediaRecorder || this.mediaRecorder.state !== 'recording') {
      return false;
    }
    
    try {
      this.mediaRecorder.pause();
      this.pauseStartTime = Date.now();
      this.recordingDuration += this.pauseStartTime - this.recordingStartTime;
      this.clearInactivityDetection();
      
      // Notify about state change
      this.notifyStateChange();
      
      return true;
    } catch (error) {
      console.error('Error pausing recording:', error);
      return false;
    }
  }
  
  /**
   * Resume recording
   */
  public resume(): boolean {
    if (!this.mediaRecorder || this.mediaRecorder.state !== 'paused') {
      return false;
    }
    
    try {
      this.mediaRecorder.resume();
      this.recordingStartTime = Date.now();
      this.setupInactivityDetection();
      
      // Notify about state change
      this.notifyStateChange();
      
      return true;
    } catch (error) {
      console.error('Error resuming recording:', error);
      return false;
    }
  }
  
  /**
   * Get current recording as blob
   */
  public getRecording(): Blob | null {
    if (this.recordedChunks.length === 0) {
      return null;
    }
    
    try {
      return new Blob(this.recordedChunks, { type: this.options.mimeType });
    } catch (error) {
      console.error('Error creating recording blob:', error);
      return null;
    }
  }
  
  /**
   * Clear current recording
   */
  public clearRecording(): void {
    this.recordedChunks = [];
    this.recordingDuration = 0;
    this.notifyStateChange();
  }
  
  /**
   * Get current audio level (0-1)
   */
  public getAudioLevel(): number {
    if (!this.analyser || !this.dataArray) {
      return 0;
    }
    
    this.analyser.getByteFrequencyData(this.dataArray);
    
    // Calculate average level
    let sum = 0;
    for (let i = 0; i < this.dataArray.length; i++) {
      sum += this.dataArray[i];
    }
    
    return sum / this.dataArray.length / 255; // Normalize to 0-1
  }
  
  /**
   * Get current recording state
   */
  public getState(): RecordingState {
    return {
      isRecording: this.mediaRecorder?.state === 'recording',
      isPaused: this.mediaRecorder?.state === 'paused',
      duration: this.getDuration(),
      audioLevel: this.getAudioLevel(),
      hasRecording: this.recordedChunks.length > 0
    };
  }
  
  /**
   * Release microphone and clean up resources
   */
  public release(): void {
    this.clearInactivityDetection();
    
    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop();
    }
    
    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => track.stop());
      this.mediaStream = null;
    }
    
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close().catch(err => console.error('Error closing audio context', err));
    }
    
    this.mediaRecorder = null;
    this.analyser = null;
    this.dataArray = null;
    this.audioContext = null;
  }
  
  /**
   * Set callback for when audio data is available
   */
  public onDataAvailable(callback: (data: Blob) => void): void {
    this.onDataAvailableCallback = callback;
  }
  
  /**
   * Set callback for state changes
   */
  public onStateChange(callback: (state: RecordingState) => void): void {
    this.onStateChangeCallback = callback;
  }
  
  /**
   * Get recording duration in milliseconds
   */
  private getDuration(): number {
    if (this.mediaRecorder?.state === 'recording') {
      return this.recordingDuration + (Date.now() - this.recordingStartTime);
    } else if (this.mediaRecorder?.state === 'paused') {
      return this.recordingDuration;
    } else {
      return this.recordingDuration;
    }
  }
  
  /**
   * Handle data available event from media recorder
   */
  private handleDataAvailable(event: BlobEvent): void {
    if (event.data.size > 0) {
      this.recordedChunks.push(event.data);
      
      // Call data available callback if set
      if (this.onDataAvailableCallback) {
        this.onDataAvailableCallback(event.data);
      }
    }
  }
  
  /**
   * Set up inactivity detection
   */
  private setupInactivityDetection(): void {
    if (this.options.pauseAfterInactivity <= 0) {
      return; // Feature is disabled
    }
    
    this.clearInactivityDetection();
    
    // Check audio level periodically
    const checkInterval = 200; // ms
    
    this.inactivityTimer = window.setInterval(() => {
      const level = this.getAudioLevel();
      
      // Auto-pause if audio level is below threshold
      if (level < this.options.silenceThreshold) {
        this.pause();
        this.clearInactivityDetection();
      }
    }, checkInterval);
  }
  
  /**
   * Clear inactivity detection
   */
  private clearInactivityDetection(): void {
    if (this.inactivityTimer !== null) {
      clearInterval(this.inactivityTimer);
      this.inactivityTimer = null;
    }
  }
  
  /**
   * Notify about state change
   */
  private notifyStateChange(): void {
    if (this.onStateChangeCallback) {
      this.onStateChangeCallback(this.getState());
    }
  }
}

// For browsers that don't have these types defined
declare global {
  interface Window {
    webkitAudioContext: typeof AudioContext;
  }
} 